{"folders": [{"name": "🏠 Root", "path": "."}, {"name": "🔧 API", "path": "./apps/indaver-cz-api"}, {"name": "🌐 Web", "path": "./apps/indaver-cz-web"}, {"name": "📦 Types", "path": "./packages/types"}], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.workspaceSymbols.scope": "allOpenProjects", "eslint.workingDirectories": ["apps/indaver-cz-api", "apps/indaver-cz-web", "packages/types"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "extensions": {"recommendations": ["esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss"]}}