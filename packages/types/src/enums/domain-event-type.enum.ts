export enum DomainEventType {
  ANNOUNCEMENT_CREATED = 'announcement.created',
  ANNOUNCEMENT_DELETED = 'announcement.deleted',
  ANNOUNCEMENT_UPDATED = 'announcement.updated',

  CONTACT_CREATED = 'contact.created',
  CONTACT_DELETED = 'contact.deleted',
  CONTACT_UPDATED = 'contact.updated',

  DYNAMIC_TABLE_VIEW_CREATED = 'dynamic-table-view.created',
  DYNAMIC_TABLE_VIEW_DELETED = 'dynamic-table-view.deleted',
  DYNAMIC_TABLE_VIEW_UPDATED = 'dynamic-table-view.updated',

  FILE_CREATED = 'file.created',
  FILE_DELETED = 'file.deleted',
  FILE_UPLOADED = 'file.uploaded',

  NEWS_ITEM_CREATED = 'news-item.created',
  NEWS_ITEM_DELETED = 'news-item.deleted',
  NEWS_ITEM_UPDATED = 'news-item.updated',

  NOTIFICATION_CREATED = 'notification.created',
  NOTIFICATION_DELETED = 'notification.deleted',
  NOTIFICATION_UPDATED = 'notification.updated',

  PICK_UP_REQUEST_CREATED = 'pick-up-request.created',
  PICK_UP_REQUEST_DELETED = 'pick-up-request.deleted',
  PICK_UP_REQUEST_UPDATED = 'pick-up-request.updated',

  PICK_UP_REQUEST_TEMPLATE_CREATED = 'pick-up-request-template.created',
  PICK_UP_REQUEST_TEMPLATE_DELETED = 'pick-up-request-template.deleted',
  PICK_UP_REQUEST_TEMPLATE_UPDATED = 'pick-up-request-template.updated',

  USER_CREATED = 'user.created',
  USER_DELETED = 'user.deleted',
  USER_UPDATED = 'user.updated',

  WASTE_INQUIRY_CREATED = 'waste-inquiry.created',
  WASTE_INQUIRY_DELETED = 'waste-inquiry.deleted',
  WASTE_INQUIRY_UPDATED = 'waste-inquiry.updated',

  WEEKLY_PLANNING_CREATED = 'weekly-planning.created',
  WEEKLY_PLANNING_DELETED = 'weekly-planning.deleted',
  WEEKLY_PLANNING_UPDATED = 'weekly-planning.updated'
}
