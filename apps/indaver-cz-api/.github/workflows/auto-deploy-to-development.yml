---
name: Auto-deploy to development

on: # yamllint disable-line rule:truthy
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  build-and-deploy:
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-build-and-deploy-azure.yml@main
    with:
      environment: development
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}
