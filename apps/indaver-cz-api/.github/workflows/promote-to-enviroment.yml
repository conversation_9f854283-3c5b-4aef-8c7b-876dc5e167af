---
name: Promote to enviroment

on: # yamllint disable-line rule:truthy
  workflow_dispatch:
    inputs:
      environment:
        description: Target environment to deploy TO
        type: environment
        required: true
      source-tag:
        description: Source tag (defaults to previous environment)
        type: string
        required: false

jobs:
  promote-to-env:
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-promote-to-environment-azure.yml@main
    with:
      environment-source: ${{ inputs.source-tag }}
      environment-target: ${{ inputs.environment }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}
