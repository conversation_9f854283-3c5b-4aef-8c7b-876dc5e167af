{"packageManager": "pnpm@10.17.1", "name": "nestjs-example", "version": "0.1.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"clean": "rm -rf ./dist", "build": "nest build", "start": "node dist/src/entrypoints/api.js", "start:dev": "nest start --exec \"node --env-file=.env\" --watch", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint": "eslint --cache", "test:setup": "node --env-file=.env.test dist/test/setup/global-setup.js", "test:run": "node --enable-source-maps --experimental-test-isolation=none --env-file=.env.test --test \"**/*.test.js\"", "test:pipeline": "pnpm test:setup && pnpm test:run", "test:all": "pnpm clean && pnpm build && pnpm test:setup && pnpm test:run", "test:one": "pnpm clean && pnpm build && node --test --enable-source-maps --env-file=.env.test --experimental-test-isolation=none --test-reporter=spec", "typeorm": "pnpm clean && pnpm build && node --env-file=.env ./node_modules/typeorm/cli -d ./dist/src/sql/sources/main.js", "translate": "tsc --build tsconfig.translate.json && node --env-file=.env.test dist/entrypoints/generate-translations.js", "jetstream:migration:create": "node --env-file=.env dist/src/modules/nats/nats-application/streams/create-jetstream-migration.js"}, "dependencies": {"@indaver/types": "workspace:*", "@azure/identity": "^4.12.0", "@azure/storage-blob": "^12.29.0", "@connectrpc/connect": "^2.1.0", "@connectrpc/connect-node": "^2.1.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@nats-io/jetstream": "^3.2.0", "@nats-io/jwt": "0.0.11", "@nats-io/kv": "^3.2.0", "@nats-io/nkeys": "^2.0.3", "@nats-io/services": "^3.2.0", "@nats-io/transport-node": "^3.2.0", "@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.1.6", "@nestjs/platform-ws": "^11.1.6", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.6", "@ngneat/falso": "^8.0.2", "@onesignal/node-onesignal": "5.2.1-beta1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.205.0", "@opentelemetry/exporter-logs-otlp-http": "^0.205.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.205.0", "@opentelemetry/exporter-trace-otlp-http": "^0.205.0", "@opentelemetry/instrumentation": "^0.205.0", "@opentelemetry/instrumentation-aws-sdk": "^0.60.0", "@opentelemetry/instrumentation-express": "^0.54.0", "@opentelemetry/instrumentation-http": "^0.205.0", "@opentelemetry/instrumentation-nestjs-core": "^0.51.0", "@opentelemetry/instrumentation-pg": "^0.58.0", "@opentelemetry/instrumentation-redis-4": "^0.49.0", "@opentelemetry/resources": "^2.1.0", "@opentelemetry/sdk-logs": "^0.205.0", "@opentelemetry/sdk-metrics": "^2.1.0", "@opentelemetry/sdk-node": "^0.205.0", "@opentelemetry/sdk-trace-base": "^2.1.0", "@sentry/nestjs": "^10.15.0", "@wisemen/app-container": "^2.1.20", "@wisemen/coordinates": "0.0.10", "@wisemen/decorators": "^0.0.10", "@wisemen/nestjs-typeorm": "^0.0.30", "@wisemen/one-of": "^0.0.7", "@wisemen/pagination": "^0.0.7", "@wisemen/pgboss-nestjs-job": "^1.1.10", "@wisemen/time": "^0.0.26", "@wisemen/validators": "^0.0.16", "@zitadel/client": "^1.3.1", "@zitadel/proto": "^1.3.1", "axios": "^1.12.2", "change-case": "^5.4.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dayjs": "^1.11.18", "exceljs": "^4.4.0", "handlebars": "^4.7.8", "joi": "^18.0.1", "jose": "^6.1.0", "jsonwebtoken": "^9.0.2", "nestjs-i18n": "^10.5.1", "pg": "^8.16.3", "pg-boss": "^10.3.3", "qs": "^6.14.0", "redis": "^5.8.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "twilio": "^5.10.1", "typeorm": "^0.3.27", "typesense": "^2.1.0", "ws": "^8.18.3", "yargs": "^18.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.7", "@nestjs/testing": "^11.1.6", "@types/exceljs": "^1.3.2", "@types/express": "^5.0.3", "@types/microsoft-graph": "^2.40.1", "@types/node": "^24.5.2", "@types/qs": "^6.14.0", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.3", "@types/ws": "^8.18.1", "@types/yargs": "^17.0.33", "@wisemen/eslint-config-nestjs": "^0.1.7", "eslint": "9.36.0", "eslint-plugin-unicorn": "61.0.2", "expect": "^30.2.0", "sinon": "^21.0.0", "supertest": "^7.1.4", "typescript": "^5.9.2"}}