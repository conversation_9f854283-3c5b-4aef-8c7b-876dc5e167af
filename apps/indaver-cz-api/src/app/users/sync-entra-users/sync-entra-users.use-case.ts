import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import dayjs from 'dayjs'
import { CronjobUseCase } from '../../../modules/cronjobs/cronjob.use-case.js'
import { GetEntraUsersUseCase } from '../../../modules/microsoft-graph/use-cases/fetch-entra-users/fetch-entra-users.use-case.js'
import { User } from '../entities/user.entity.js'
import { DomainEventEmitter } from '../../../modules/domain-events/domain-event-emitter.js'
import { DomainEvent } from '../../../modules/domain-events/domain-event.js'
import { UserCreatedEvent } from '../use-cases/get-or-create-user/user-created.event.js'
import { UserUpdatedEvent } from './user-synced.event.js'

@Injectable()
export class SyncEntraUsersUseCase implements CronjobUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly getEntraUsersUseCase: GetEntraUsersUseCase,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private eventEmitter: DomainEventEmitter
  ) { }

  async execute (): Promise<void> {
    const entraUsers = await this.getEntraUsersUseCase.execute()

    const users: Partial<User>[] = entraUsers.map(entraUser => ({
      azureEntraId: entraUser.id,
      azureEntraUpn: entraUser.upn,
      email: entraUser.email ?? entraUser.upn,
      firstName: entraUser.firstName,
      lastName: entraUser.lastName
    }))

    const now = dayjs()
    const events: DomainEvent[] = []

    await transaction(this.dataSource, async () => {
      const result = await this.userRepository.upsert(users, ['azureEntraId'])
      for (const user of result.generatedMaps) {
        if (dayjs(user.createdAt as string).isSameOrAfter(now)) {
          events.push(new UserCreatedEvent(user.uuid as string))
        } else {
          events.push(new UserUpdatedEvent(user.uuid as string))
        }
      }
      await this.eventEmitter.emit(events)
    })
  }
}
