import { <PERSON>, <PERSON> } from '@nestjs/common'
import { ApiOAuth2, <PERSON>pi<PERSON><PERSON><PERSON>, ApiCreatedResponse } from '@nestjs/swagger'
import { Permissions } from '../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../modules/permission/permission.enum.js'
import { SyncEntraUsersUseCase } from './sync-entra-users.use-case.js'

@ApiTags('User')
@Controller('users/sync-external')
@ApiOAuth2([])
export class SyncEntraUsersController {
  constructor (private readonly syncEntraUsersUseCase: SyncEntraUsersUseCase) { }

  @Post()
  @Permissions(Permission.USER_READ)
  @ApiCreatedResponse({ description: 'Azure Entra users synced successfully' })
  async syncEntraUsers () {
    await this.syncEntraUsersUseCase.execute()
    return
  }
}
