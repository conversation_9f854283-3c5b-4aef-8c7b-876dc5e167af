import { Module } from '@nestjs/common'

import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Role } from '../roles/entities/role.entity.js'
import { UserRole } from '../roles/entities/user-role.entity.js'
import { RedisModule } from '../../modules/redis/redis.module.js'
import { TypesenseModule } from '../../modules/typesense/typesense.module.js'
import { UserAuthService } from './services/user-auth.service.js'
import { ViewMeModule } from './use-cases/view-me/view-me.module.js'
import { ViewUserDetailModule } from './use-cases/view-user-detail/view-user-detail.module.js'
import { ViewUserIndexModule } from './use-cases/view-user-index/view-user-index.module.js'
import { GetOrCreateUserModule } from './use-cases/get-or-create-user/get-or-create-user.module.js'
import { User } from './entities/user.entity.js'
import { SyncEntraUsersModule } from './sync-entra-users/sync-entra-users.module.js'
import { StartUserImpersonation } from './use-cases/start-user-impersonation/start-user-impersonation.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole
    ]),
    RedisModule,
    TypesenseModule,
    ViewMeModule,
    ViewUserDetailModule,
    ViewUserIndexModule,
    GetOrCreateUserModule,
    SyncEntraUsersModule,
    StartUserImpersonation
  ],
  providers: [UserAuthService],
  exports: [UserAuthService]
})
export class UserModule { }
