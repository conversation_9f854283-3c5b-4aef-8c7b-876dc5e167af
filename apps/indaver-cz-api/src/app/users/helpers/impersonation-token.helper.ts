import { InvalidImpersonationTokenError } from '../errors/invalid-impersonation-token.error.js'

export interface ImpersonationTokenPayload {
  userUuid: string
  azureEntraId: string
  azureEntraUpn: string
}

export function encodeImpersonationToken (payload: ImpersonationTokenPayload): string {
  return Buffer.from(JSON.stringify(payload)).toString('base64')
}

export function decodeImpersonationToken (token: string): ImpersonationTokenPayload {
  const jsonString = Buffer.from(token, 'base64').toString('utf-8')

  let obj: unknown
  try {
    obj = JSON.parse(jsonString)
  } catch {
    throw new InvalidImpersonationTokenError()
  }

  if (
    typeof obj === 'object'
    && obj !== null
    && 'userUuid' in obj
    && typeof obj.userUuid === 'string'
    && 'azureEntraId' in obj
    && typeof obj.azureEntraId === 'string'
    && 'azureEntraUpn' in obj
    && typeof obj.azureEntraUpn === 'string'
  ) {
    return obj as ImpersonationTokenPayload
  }

  throw new InvalidImpersonationTokenError()
}
