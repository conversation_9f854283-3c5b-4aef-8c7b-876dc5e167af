import { Module } from '@nestjs/common'
import { UserImpersonateService } from '../../services/user-impersonate.service.js'
import { SyncUserRolesJobModule } from '../sync-user-roles/sync-user-roles.job-module.js'
import { StartUserImpersonationController } from './start-user-impersonation.controller.js'
import { StartUserImpersonationUseCase } from './start-user-impersonation.use-case.js'

@Module({
  imports: [
    SyncUserRolesJobModule
  ],
  controllers: [
    StartUserImpersonationController
  ],
  providers: [
    StartUserImpersonationUseCase,
    UserImpersonateService
  ]
})
export class StartUserImpersonation {}
