import { Injectable } from '@nestjs/common'
import { UserImpersonateService } from '../../services/user-impersonate.service.js'
import { SyncUserRolesUseCase } from '../sync-user-roles/sync-user-roles.use-case.js'
import { encodeImpersonationToken } from '../../helpers/impersonation-token.helper.js'
import { NotFoundError } from '../../../../modules/exceptions/generic/not-found.error.js'
import { StartUserImpersonationResponse } from './start-user-impersonation.response.js'

@Injectable()
export class StartUserImpersonationUseCase {
  constructor (
    private readonly userImpersonateService: UserImpersonateService,
    private readonly syncUserRolesUseCase: SyncUserRolesUseCase
  ) {}

  async execute (userUuid: string): Promise<StartUserImpersonationResponse> {
    const impersonateUser = await this.userImpersonateService.checkImpersonateUserAccess(userUuid)

    await this.syncUserRolesUseCase.execute(userUuid)

    if (impersonateUser == null) {
      throw new NotFoundError(`error.auth.impersonated_user_not_found`)
    }

    const impersonationToken = encodeImpersonationToken({
      userUuid,
      azureEntraId: impersonateUser.azureEntraId,
      azureEntraUpn: impersonateUser.azureEntraUpn
    })

    return new StartUserImpersonationResponse(impersonationToken)
  }
}
