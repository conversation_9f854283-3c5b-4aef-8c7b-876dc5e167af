import { <PERSON>, <PERSON> } from '@nestjs/common'
import { ApiOAuth2, <PERSON>pi<PERSON><PERSON><PERSON>, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { StartUserImpersonationUseCase } from './start-user-impersonation.use-case.js'
import { StartUserImpersonationResponse } from './start-user-impersonation.response.js'

@ApiTags('User')
@Controller('users/:uuid/impersonate')
@ApiOAuth2([])
export class StartUserImpersonationController {
  constructor (private readonly userCase: StartUserImpersonationUseCase) { }

  @Post()
  @Permissions(Permission.USER_IMPERSONATE)
  @ApiOkResponse({
    description: 'User impersonation started',
    type: StartUserImpersonationResponse
  })
  async startUserImpersonation (
    @UuidParam('uuid') userUuid: string
  ): Promise<StartUserImpersonationResponse> {
    return await this.userCase.execute(userUuid)
  }
}
