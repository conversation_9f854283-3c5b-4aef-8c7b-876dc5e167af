import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { User } from '../../entities/user.entity.js'
import { DomainEventEmitterModule } from '../../../../modules/domain-events/domain-event-emitter.module.js'
import { Role } from '../../../roles/entities/role.entity.js'
import { UserRole } from '../../../roles/entities/user-role.entity.js'
import { UserCacheModule } from '../../cache/user-cache.module.js'
import { GetOrCreateUserUseCase } from './get-or-create-user.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole
    ]),
    DomainEventEmitterModule,
    UserCacheModule
  ],
  providers: [
    GetOrCreateUserUseCase
  ],
  exports: [
    GetOrCreateUserUseCase
  ]
})
export class GetOrCreateUserModule {}
