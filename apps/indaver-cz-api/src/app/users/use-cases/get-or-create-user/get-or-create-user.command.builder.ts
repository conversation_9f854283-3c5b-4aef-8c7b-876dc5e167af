import { randomUUID } from 'crypto'
import { GetOrCreateUserCommand } from './get-or-create-user.command.js'

export class GetOrCreateUserCommandBuilder {
  private readonly command: GetOrCreateUserCommand

  constructor () {
    this.command = new GetOrCreateUserCommand()
    this.command.email = '<EMAIL>'
    this.command.firstName = null
    this.command.lastName = null
    this.command.id = '1'
    this.command.azureEntraId = randomUUID()
    this.command.azureEntraUpn = randomUUID()
    this.command.roles = []
  }

  withEmail (email: string): this {
    this.command.email = email

    return this
  }

  withFirstName (firstName: string | null): this {
    this.command.firstName = firstName

    return this
  }

  withLastName (lastName: string | null): this {
    this.command.lastName = lastName

    return this
  }

  withId (id: string): this {
    this.command.id = id

    return this
  }

  withAzureEntraId (azureEntraId: string): this {
    this.command.azureEntraId = azureEntraId

    return this
  }

  withAzureEntraUpn (azureEntraUpn: string): this {
    this.command.azureEntraUpn = azureEntraUpn

    return this
  }

  withRoles (roles: string[]): this {
    this.command.roles = roles

    return this
  }

  build (): GetOrCreateUserCommand {
    return this.command
  }
}
