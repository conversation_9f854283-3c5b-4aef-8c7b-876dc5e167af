import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Any, DataSource, Repository } from 'typeorm'
import { Role } from '../../../../app/roles/entities/role.entity.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { UserRole } from '../../../../app/roles/entities/user-role.entity.js'
import { UserCache } from '../../../../app/users/cache/user-cache.js'
import { GetUserAppRolesUseCase } from '../../../../modules/microsoft-graph/use-cases/get-user-app-roles/get-user-app-roles.use-case.js'

@Injectable()
export class SyncUserRolesUseCase {
  constructor (
    private readonly getUserAppRolesUseCase: GetUserAppRolesUseCase,
    private readonly dataSource: DataSource,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    private readonly userCache: UserCache
  ) {}

  async execute (
    userUuid: string
  ): Promise<void> {
    const user = await this.userRepository.findOneOrFail({
      where: {
        uuid: userUuid
      },
      relations: {
        userRoles: true
      }
    })
    assert(user.userRoles !== undefined)

    if (user.azureEntraId === null) return

    const appRoles = await this.getUserAppRolesUseCase.execute(user.azureEntraId)

    const appRoleIds: string[] = []
    for (const appRoleAssignment of appRoles ?? []) {
      if (appRoleAssignment.deletedDateTime != null) continue
      if (appRoleAssignment.appRoleId == null) continue

      appRoleIds.push(appRoleAssignment.appRoleId)
    }

    const roles = await this.roleRepository.find({
      select: {
        uuid: true
      },
      where: {
        externalId: Any(appRoleIds)
      }
    })
    const roleUuids = roles.map(role => role.uuid)
    const existingRoleUuids = user.userRoles.map(userRole => userRole.roleUuid)

    const rolesToAdd = roleUuids.filter(roleUuid => !existingRoleUuids.includes(roleUuid))
      .map(roleUuid => this.userRoleRepository.create({
        userUuid: user.uuid,
        roleUuid
      }))

    const rolesToRemove = existingRoleUuids.filter(roleUuid => !roleUuids.includes(roleUuid))

    await transaction(this.dataSource, async () => {
      if (rolesToAdd.length > 0) {
        await this.userRoleRepository.insert(rolesToAdd)
      }
      if (rolesToRemove.length > 0) {
        await this.userRoleRepository.delete({
          userUuid: user.uuid,
          roleUuid: Any(rolesToRemove)
        })
      }
    })

    await this.userCache.setUserRoles(user.uuid, roleUuids)
  }
}
