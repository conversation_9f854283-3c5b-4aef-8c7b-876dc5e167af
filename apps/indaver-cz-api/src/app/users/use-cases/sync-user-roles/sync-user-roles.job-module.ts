import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { User } from '../../entities/user.entity.js'
import { Role } from '../../../roles/entities/role.entity.js'
import { UserRole } from '../../../roles/entities/user-role.entity.js'
import { UserCacheModule } from '../../cache/user-cache.module.js'
import { MicrosoftGraphModule } from '../../../../modules/microsoft-graph/microsoft-graph.module.js'
import { SyncUserRolesJobHandler } from './sync-user-roles.job-handler.js'
import { SyncUserRolesUseCase } from './sync-user-roles.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole
    ]),
    UserCacheModule,
    MicrosoftGraphModule
  ],
  providers: [
    SyncUserRolesUseCase,
    SyncUserRolesJobHand<PERSON>
  ],
  exports: [
    SyncUserRolesUseCase
  ]
})
export class SyncUserRolesJobModule {}
