import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { QueueName } from '../../../../modules/pgboss/enums/queue-name.enum.js'

export interface SyncUserRolesJobData extends BaseJobData {
  userUuid: string
}

@PgBossJob(QueueName.SYSTEM)
export class SyncUserRolesJob extends BaseJob<SyncUserRolesJobData> {
  constructor (userUuid: string) {
    super({ userUuid }, {
      singletonKey: userUuid
    })
  }
}
