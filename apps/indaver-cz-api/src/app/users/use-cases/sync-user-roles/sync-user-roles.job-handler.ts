import { Injectable } from '@nestjs/common'
import { <PERSON><PERSON><PERSON><PERSON>, PgBossJob<PERSON><PERSON><PERSON> } from '@wisemen/pgboss-nestjs-job'
import { SyncUserRolesJob, SyncUserRolesJobData } from './sync-user-roles.job.js'
import { SyncUserRolesUseCase } from './sync-user-roles.use-case.js'

@Injectable()
@PgBossJobHandler(SyncUserRolesJob)
export class SyncUserRolesJobHandler extends JobHandler<SyncUserRolesJob> {
  constructor (
    private readonly useCase: SyncUserRolesUseCase
  ) {
    super()
  }

  async run ({ userUuid }: SyncUserRolesJobData): Promise<void> {
    await this.useCase.execute(userUuid)
  }
}
