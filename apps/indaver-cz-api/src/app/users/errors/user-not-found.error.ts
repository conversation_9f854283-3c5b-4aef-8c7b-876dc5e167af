import { NotFoundApiError } from '../../../modules/exceptions/api-errors/not-found.api-error.js'
import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { translateCurrent } from '../../../modules/localization/helpers/translate.helper.js'

export class UserNotFoundError extends NotFoundApiError {
  @ApiErrorCode('user_not_found')
  code = 'user_not_found'

  meta: never

  constructor (userUuid?: string) {
    const detail = translateCurrent('error.user.user_not_found', { args: { userUuid: userUuid ?? '' } })
    super(detail)
  }
}
