import { randomUUID } from 'node:crypto'
import { randEmail, randFirstName, randLastName } from '@ngneat/falso'
import { User } from '../entities/user.entity.js'
import { Role } from '../../roles/entities/role.entity.js'
import { UserRoleEntityBuilder } from '../../roles/tests/builders/entities/user-role-entity.builder.js'

export class UserEntityBuilder {
  private user: User

  constructor () {
    this.user = new User()

    this.user.uuid = randomUUID()
    this.user.zitadelSub = randomUUID()
    this.user.createdAt = new Date()
    this.user.updatedAt = new Date()
    this.user.email = randEmail()
    this.user.firstName = randFirstName()
    this.user.lastName = randLastName()
    this.user.userRoles = []
    this.user.azureEntraId = randomUUID()
    this.user.azureEntraUpn = `${randomUUID()}@indaver.com`

    return this
  }

  withUuid (uuid: string): this {
    this.user.uuid = uuid
    return this
  }

  withEmail (email: string): this {
    this.user.email = email
    return this
  }

  withFirstName (firstName: string | null): this {
    this.user.firstName = firstName
    return this
  }

  withLastName (lastName: string | null): this {
    this.user.lastName = lastName
    return this
  }

  withZitadelSub (sub: string | null): this {
    this.user.zitadelSub = sub

    return this
  }

  withAzureEntraId (azureEntraId: string): this {
    this.user.azureEntraId = azureEntraId

    return this
  }

  withAzureEntraUpn (azureEntraUpn: string): this {
    this.user.azureEntraUpn = azureEntraUpn

    return this
  }

  addRole (role: Role): this {
    if (this.user.userRoles === undefined) {
      this.user.userRoles = []
    }

    this.user.userRoles.push(
      new UserRoleEntityBuilder()
        .withRoleUuid(this.user.uuid)
        .withRole(role)
        .build()
    )

    return this
  }

  build (): User {
    return this.user
  }
}
