import { NotFoundApiError } from '../../../modules/exceptions/api-errors/not-found.api-error.js'
import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { translateCurrent } from '../../../modules/localization/helpers/translate.helper.js'

export class RoleNotFoundError extends NotFoundApiError {
  @ApiErrorCode('role_not_found')
  code = 'role_not_found'

  meta: never

  constructor (roleUuid?: string) {
    const detail = translateCurrent('error.roles.role_not_found', { args: { roleUuid: roleUuid ?? '' } })
    super(detail)
  }
}
