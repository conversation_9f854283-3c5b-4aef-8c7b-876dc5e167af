import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../../modules/exceptions/api-errors/api-error-meta.decorator.js'
import { ConflictApiError } from '../../../modules/exceptions/api-errors/conflict.api-error.js'
import { translateCurrent } from '../../../modules/localization/helpers/translate.helper.js'

class RoleNameAlreadyInUseErrorMeta {
  @ApiProperty({
    required: true,
    description: 'the role name which is already in use',
    example: 'admin'
  })
  readonly name: string

  constructor (name: string) {
    this.name = name
  }
}

export class RoleNameAlreadyInUseError extends ConflictApiError {
  @ApiErrorCode('role_name_already_in_use')
  readonly code = 'role_name_already_in_use'

  @ApiErrorMeta()
  readonly meta: RoleNameAlreadyInUseErrorMeta

  constructor (name: string) {
    const detail = translateCurrent('error.roles.role_name_already_in_use', { args: { name: name } })
    super(detail)
    this.meta = new RoleNameAlreadyInUseErrorMeta(name)
  }
}
