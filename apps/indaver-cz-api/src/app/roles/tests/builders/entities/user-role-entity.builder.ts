import { randomUUID } from 'crypto'
import { UserRole } from '../../../entities/user-role.entity.js'
import { Role } from '../../../entities/role.entity.js'

export class UserRoleEntityBuilder {
  private entity: UserRole

  constructor () {
    this.reset()
  }

  reset (): this {
    this.entity = new UserRole()

    this.entity.userUuid = randomUUID()
    this.entity.roleUuid = randomUUID()

    return this
  }

  withUserUuid (userUuid: string): this {
    this.entity.userUuid = userUuid

    return this
  }

  withRoleUuid (uuid: string): this {
    this.entity.roleUuid = uuid

    return this
  }

  withRole (role: Role): this {
    this.entity.role = role
    this.entity.roleUuid = role.uuid

    return this
  }

  build (): UserRole {
    const result = this.entity

    this.reset()

    return result
  }
}
