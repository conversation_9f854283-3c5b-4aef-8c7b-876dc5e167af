import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Column, Index } from 'typeorm'
import { Permission } from '../../../modules/permission/permission.enum.js'

@Entity()
export class Role {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar', unique: true, nullable: true })
  @Index({ unique: true })
  externalId: string | null

  @Column({ type: 'varchar', unique: true, nullable: true })
  @Index({ unique: true })
  identifier: string | null

  @Column({ type: 'varchar', unique: true })
  name: string

  @Column({ type: 'varchar', default: [], array: true })
  permissions: Permission[]

  @Column({ type: 'boolean', default: false })
  @Index({ unique: true, where: 'is_default' })
  isDefault: boolean

  @Column({ type: 'boolean', default: false })
  @Index({ unique: true, where: 'is_system_admin' })
  isSystemAdmin: boolean
}
