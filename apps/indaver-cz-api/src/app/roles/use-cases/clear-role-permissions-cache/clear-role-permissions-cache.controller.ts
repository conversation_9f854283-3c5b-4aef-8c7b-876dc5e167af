import { Controller, Post, Body, HttpCode } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiNoContentResponse } from '@nestjs/swagger'
import { SystemAdmin } from '../../../../modules/permission/permission.decorator.js'
import { ClearRolePermissionsCacheUseCase } from './clear-role-permissions-cache.use-case.js'
import { ClearRolePermissionsCacheCommand } from './clear-role-permissions-cache.command.js'

@ApiTags('Role')
@Controller('roles/clear-cache')
@ApiOAuth2([])
export class ClearRolePermissionsCacheController {
  constructor (
    private readonly useCase: ClearRolePermissionsCacheUseCase
  ) {}

  @Post()
  @HttpCode(204)
  @ApiNoContentResponse()
  @SystemAdmin()
  async clearCache (
    @Body() command: ClearRolePermissionsCacheCommand
  ): Promise<void> {
    await this.useCase.execute(command.roleUuids ?? undefined)
  }
}
