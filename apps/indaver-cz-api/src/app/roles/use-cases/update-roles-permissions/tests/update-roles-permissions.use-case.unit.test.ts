import { before, describe, it } from 'node:test'
import { randomUUID } from 'node:crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { UpdateRolesPermissionsUseCase } from '../update-roles-permissions.use-case.js'
import { DomainEventEmitter } from '../../../../../modules/domain-events/domain-event-emitter.js'
import { UpdateRolesPermissionsRepository } from '../update-roles-permissions.repository.js'
import { NotFoundCompositeApiError } from '../../../../../modules/exceptions/api-errors/not-found-composite.api-error.js'
import { RoleNotFoundError } from '../../../errors/role-not-found.error.js'
import { RolePermissionsUpdatedEvent } from '../role-permissions-updated.event.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { RoleNotEditableError } from '../../../errors/role-not-editable.error.js'

import { RoleEntityBuilder } from '../../../tests/builders/entities/role-entity.builder.js'
import { Permission } from '../../../../../modules/permission/permission.enum.js'
import { UpdateRolesPermissionsCommandBuilder } from './update-roles-permissions.command.builder.js'

describe('Update role permissions use case unit tests', () => {
  let updateRolesRepository: SinonStubbedInstance<UpdateRolesPermissionsRepository>

  let useCase: UpdateRolesPermissionsUseCase
  let eventEmitter: DomainEventEmitter

  before(() => {
    TestBench.setupUnitTest()

    updateRolesRepository = createStubInstance(UpdateRolesPermissionsRepository)

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new UpdateRolesPermissionsUseCase(
      stubDataSource(),
      eventEmitter,
      updateRolesRepository
    )
  })

  it('throws an error when a role does not exist', async () => {
    updateRolesRepository.findRoles.resolves([])

    const roleUuid = randomUUID()
    const command = new UpdateRolesPermissionsCommandBuilder()
      .addRole(roleUuid, [])
      .build()

    await expect(useCase.updateRolePermissions(command)).rejects.toThrow(
      new NotFoundCompositeApiError([new RoleNotFoundError(roleUuid)])
    )
  })

  it('throws an error when a system admin role is changed', async () => {
    const nonEditableRole = new RoleEntityBuilder()
      .withIsSystemAdmin(true)
      .build()

    updateRolesRepository.findRoles.resolves([nonEditableRole])

    const command = new UpdateRolesPermissionsCommandBuilder()
      .addRole(nonEditableRole.uuid, [])
      .build()

    await expect(useCase.updateRolePermissions(command)).rejects.toThrow(
      new RoleNotEditableError(nonEditableRole)
    )
  })

  it('adds contacts permissions', async () => {
    const roleUuid = randomUUID()

    const role = new RoleEntityBuilder()
      .withUuid(roleUuid)
      .build()

    updateRolesRepository.findRoles.resolves([role])

    const command = new UpdateRolesPermissionsCommandBuilder()
      .addRole(roleUuid, [Permission.PICK_UP_REQUEST_MANAGE])
      .build()

    await useCase.updateRolePermissions(command)

    expect(role.permissions).toContain(Permission.CONTACT_MANAGE)
  })

  it('emits an event for each role', async () => {
    const role1Uuid = randomUUID()
    const role2Uuid = randomUUID()

    const roles = [
      new RoleEntityBuilder()
        .withUuid(role1Uuid)
        .build(),
      new RoleEntityBuilder()
        .withUuid(role2Uuid)
        .build()
    ]

    updateRolesRepository.findRoles.resolves(roles)

    const command = new UpdateRolesPermissionsCommandBuilder()
      .addRole(role1Uuid, [])
      .addRole(role2Uuid, [])
      .build()

    await useCase.updateRolePermissions(command)
    expect(eventEmitter).toHaveEmitted(new RolePermissionsUpdatedEvent(roles[0]))
    expect(eventEmitter).toHaveEmitted(new RolePermissionsUpdatedEvent(roles[1]))
  })
})
