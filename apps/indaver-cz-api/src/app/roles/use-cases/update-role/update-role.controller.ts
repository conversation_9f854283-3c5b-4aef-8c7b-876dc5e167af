import { <PERSON>, Controller, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2 } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { UpdateRoleUseCase } from './update-role.use-case.js'
import { UpdateRoleCommand } from './update-role.command.js'

@ApiTags('Role')
@Controller('roles/:uuid')
@ApiOAuth2([])
export class UpdateRoleController {
  constructor (
    private readonly useCase: UpdateRoleUseCase
  ) {}

  @Post()
  @Permissions(Permission.ROLE_MANAGE)
  async updateRole (
    @Body() updateRoleCommand: UpdateRoleCommand,
    @UuidParam('uuid') uuid: string
  ): Promise<void> {
    await this.useCase.execute(uuid, updateRoleCommand)
  }
}
