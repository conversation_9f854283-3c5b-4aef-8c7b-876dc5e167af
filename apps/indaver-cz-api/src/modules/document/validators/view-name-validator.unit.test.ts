import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../test/setup/test-bench.js'
import { AuthContext } from '../../auth/auth.context.js'
import { PermissionGuardService } from '../../permission/guards/permission.guard.service.js'
import { SharepointDocumentViewName } from '../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { ForbiddenError } from '../../exceptions/generic/forbidden.error.js'
import { ViewNameValidator } from './view-name.validator.js'

describe('ViewNameValidator - Unit tests', () => {
  let validator: ViewNameValidator

  let authContext: SinonStubbedInstance<AuthContext>
  let permissionGuardService: SinonStubbedInstance<PermissionGuardService>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    permissionGuardService = createStubInstance(PermissionGuardService)

    authContext.getUserUuidOrFail.returns(randomUUID())
    permissionGuardService.hasPermissions.resolves(true)

    validator = new ViewNameValidator(
      authContext,
      permissionGuardService
    )
  })

  it('does not throw error when sufficient permissions to access view', () => {
    permissionGuardService.hasPermissions.resolves(true)

    expect(
      validator.validateViewName(SharepointDocumentViewName.TFS)
    ).resolves.not.toThrow()
  })

  it('throws error when insufficient permissions to access view', () => {
    permissionGuardService.hasPermissions.resolves(false)

    expect(
      validator.validateViewName(SharepointDocumentViewName.TFS)
    ).rejects.toThrow(ForbiddenError)
  })
})
