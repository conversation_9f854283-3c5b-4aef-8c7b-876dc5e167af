import { Injectable } from '@nestjs/common'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SharepointDocumentViewName } from '../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { Permission } from '../../permission/permission.enum.js'
import { AuthContext } from '../../auth/auth.context.js'
import { PermissionGuardService } from '../../permission/guards/permission.guard.service.js'
import { ForbiddenError } from '../../exceptions/generic/forbidden.error.js'

@Injectable()
export class ViewNameValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly permissionGuardService: PermissionGuardService
  ) {}

  async validateViewName (viewName: SharepointDocumentViewName): Promise<void> {
    viewName = viewName.toLowerCase() as SharepointDocumentViewName
    const userUuid = this.authContext.getUserUuidOrFail()

    const permissions: Permission[] = []

    switch (viewName) {
      case SharepointDocumentViewName.MASTER_TABLE:
        permissions.push(Permission.DOCUMENT_READ_MASTER_TABLE)
        break
      case SharepointDocumentViewName.TFS:
        permissions.push(Permission.DOCUMENT_READ_TFS)
        break
      case SharepointDocumentViewName.QUOTATION:
        permissions.push(Permission.DOCUMENT_READ_QUOTATION)
        break
      case SharepointDocumentViewName.MEETINGS:
        permissions.push(Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS)
        break
      case SharepointDocumentViewName.MANUAL:
        permissions.push(Permission.DOCUMENT_READ_MANUAL)
        break
      case SharepointDocumentViewName.BALANCED_SCORE_CARD:
        permissions.push(Permission.DOCUMENT_READ_BSC)
        break
      case SharepointDocumentViewName.CONTRACT:
        permissions.push(Permission.DOCUMENT_READ_CONTRACT)
        break
      case SharepointDocumentViewName.TRANSPORT:
        permissions.push(Permission.DOCUMENT_READ_TRANSPORT)
        return
      default:
        exhaustiveCheck(viewName)
    }

    const hasRequiredPermissions = await this.permissionGuardService.hasPermissions(
      userUuid, permissions
    )

    if (!hasRequiredPermissions) {
      throw new ForbiddenError()
    }
  }
}
