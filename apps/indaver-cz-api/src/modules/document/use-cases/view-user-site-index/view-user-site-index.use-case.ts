import { Injectable } from '@nestjs/common'
import { GetUserSiteIndexUseCase } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewUserSiteIndexResponse } from './view-user-site-index.response.js'

@Injectable()
export class ViewUserSiteIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly getUserSiteIndexUseCase: GetUserSiteIndexUseCase
  ) {}

  async execute (): Promise<ViewUserSiteIndexResponse[]> {
    const entraUpn = this.authContext.getAzureEntraUpn()
    const responses = await this.getUserSiteIndexUseCase.execute(entraUpn)
    return responses.map(response => new ViewUserSiteIndexResponse(response))
  }
}
