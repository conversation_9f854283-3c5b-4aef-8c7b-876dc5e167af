import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewUserSiteIndexResponse } from './view-user-site-index.response.js'
import { ViewUserSiteIndexUseCase } from './view-user-site-index.use-case.js'

@ApiTags('Document')
@ApiOAuth2([])
@Controller('documents/sites')
export class ViewUserSiteIndexController {
  constructor (
    private readonly useCase: ViewUserSiteIndexUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.DOCUMENT_READ_MASTER_TABLE,
    Permission.DOCUMENT_READ_TFS,
    Permission.DOCUMENT_READ_QUOTATION,
    Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS,
    Permission.DOCUMENT_READ_MANUAL,
    Permission.DOCUMENT_READ_BSC,
    Permission.DOCUMENT_READ_CONTRACT,
    Permission.DOCUMENT_READ_TRANSPORT
  )
  @ApiOkResponse({ type: ViewUserSiteIndexResponse, isArray: true })
  async getUserSiteIndex (): Promise<ViewUserSiteIndexResponse[]> {
    return this.useCase.execute()
  }
}
