import { ApiProperty } from '@nestjs/swagger'
import { GetUserSiteIndexResponse, GetUserSiteIndexWasteProducerResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'

export class ViewUserSiteIndexWasteProducerResponse {
  @ApiProperty({ type: Number })
  id: number

  @ApiProperty({ type: String })
  name: string

  constructor (wasteProducer: GetUserSiteIndexWasteProducerResponse) {
    this.id = wasteProducer.id
    this.name = wasteProducer.name
  }
}

export class ViewUserSiteIndexResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: ViewUserSiteIndexWasteProducerResponse, isArray: true })
  wasteProducers: ViewUserSiteIndexWasteProducerResponse[]

  constructor (site: GetUserSiteIndexResponse) {
    this.uuid = site.siteId
    this.name = site.displayName
    this.wasteProducers = site.wasteProducerResource
      .map(wp => new ViewUserSiteIndexWasteProducerResponse(wp))
  }
}
