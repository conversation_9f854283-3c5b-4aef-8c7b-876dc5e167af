import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { ViewUserSiteIndexResponse } from '../view-user-site-index.response.js'

describe('ViewUserSiteIndexResponse - Unit tests', () => {
  before(() => TestBench.setupUnitTest())

  it('maps the response correctly', () => {
    const sharepointResponse = new GetUserSiteIndexResponseBuilder()
      .build()

    const result = new ViewUserSiteIndexResponse(sharepointResponse)

    expect(result).toStrictEqual(expect.objectContaining({
      uuid: sharepointResponse.siteId,
      name: sharepointResponse.displayName,
      wasteProducers: expect.arrayContaining(sharepointResponse.wasteProducerResource
        .map(wp => expect.objectContaining({
          id: wp.id,
          name: wp.name
        })))
    }))
  })
})
