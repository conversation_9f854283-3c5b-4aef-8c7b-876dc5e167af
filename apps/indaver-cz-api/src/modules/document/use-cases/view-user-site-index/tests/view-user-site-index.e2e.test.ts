import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('ViewUserSiteIndex - E2E Test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('retrieves the user sites', async () => {
    const user = await setup.authContext.getUser([Permission.DOCUMENT_READ_MASTER_TABLE])

    const response = await request(setup.httpServer)
      .get(`/documents/sites`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
