import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery, PaginatedKeysetQuery, PaginatedKeysetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ArrayMinSize, ArrayUnique, Equals, IsArray, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { SharepointDocumentStatus, SharepointDocumentStatusApiProperty } from '../../../sharepoint/enums/sharepoint-document-status.enum.js'
import { SharepointDocumentViewName, SharepointDocumentViewNameApiProperty } from '../../../sharepoint/enums/sharepoint-document-view-name.enum.js'

export class ViewDocumentIndexPaginationQuery extends PaginatedKeysetQuery {
  @ApiProperty({ type: String, nullable: true, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  key?: string | null
}

export class ViewDocumentIndexFilterQuery extends FilterQuery {
  @SharepointDocumentViewNameApiProperty()
  @IsEnum(SharepointDocumentViewName)
  viewName: SharepointDocumentViewName

  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  customerUuid: string

  @ApiProperty({ type: String, isArray: true })
  @IsString({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  wasteProducerIds: string[]

  @SharepointDocumentStatusApiProperty({ required: false })
  @IsUndefinable()
  @IsEnum(SharepointDocumentStatus)
  status?: SharepointDocumentStatus

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsNotEmpty()
  @IsUndefinable()
  year?: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsNotEmpty()
  @IsUndefinable()
  refExt?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  transportType?: string
}

export class ViewDocumentIndexQuery extends PaginatedKeysetSearchQuery {
  @ApiProperty({ type: ViewDocumentIndexPaginationQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewDocumentIndexPaginationQuery)
  @ValidateNested()
  @IsObject()
  pagination?: ViewDocumentIndexPaginationQuery

  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewDocumentIndexFilterQuery })
  @ValidateNested()
  @Type(() => ViewDocumentIndexFilterQuery)
  @IsNotEmpty()
  filter: ViewDocumentIndexFilterQuery

  @ApiProperty({ type: String, required: false, description: 'Searches on document name' })
  @IsUndefinable()
  @IsString()
  search?: string
}
