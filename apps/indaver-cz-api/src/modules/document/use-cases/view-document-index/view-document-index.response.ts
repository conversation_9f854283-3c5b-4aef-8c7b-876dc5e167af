import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedKeysetResponse, PaginatedKeysetResponseMeta } from '@wisemen/pagination'
import { SharePointDocumentTypeApiProperty } from '../../../sharepoint/enums/sharepoint-document-type.enum.js'
import { GetDocumentIndexFileResponse, GetDocumentIndexResponse } from '../../../sharepoint/use-cases/get-document-index/get-document-index.response.js'
import { SharepointDocumentStatus, SharepointDocumentStatusApiProperty } from '../../../sharepoint/enums/sharepoint-document-status.enum.js'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'

class ViewDocumentIndexResponseMeta implements PaginatedKeysetResponseMeta {
  @ApiProperty({ type: String, nullable: true })
  next: string | null

  constructor (response: GetDocumentIndexResponse) {
    this.next = response.oDataNextLink !== ''
      ? response.oDataNextLink
      : null
  }
}

export class ViewDocumentIndexItemResponse {
  @ApiProperty({ type: String })
  id: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  actionAt: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  applicableFrom: string | null

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  applicableTill: string | null

  @SharePointDocumentTypeApiProperty({ type: String, nullable: true })
  tfsType: string | null

  @ApiProperty({ type: String })
  wasteProducer: string

  @SharepointDocumentStatusApiProperty({ nullable: true })
  status: SharepointDocumentStatus | null

  constructor (fileResponse: GetDocumentIndexFileResponse, matchingSite: GetUserSiteIndexResponse) {
    this.id = fileResponse.id
    this.name = fileResponse.name
    this.actionAt = fileResponse.actionDate
    this.applicableFrom = fileResponse.applicapleFrom
    this.applicableTill = fileResponse.applicapleUntil
    this.tfsType = fileResponse.typeTFS !== ''
      ? fileResponse.typeTFS
      : null

    const wasteProducer = matchingSite.wasteProducerResource
      .find(r => r.id.toString() === fileResponse.customerName)

    assert(wasteProducer !== undefined)
    this.wasteProducer = wasteProducer.name
    this.status = fileResponse.status !== ''
      ? fileResponse.status
      : null
  }
}

export class ViewDocumentIndexResponse implements PaginatedKeysetResponse {
  @ApiProperty({ type: ViewDocumentIndexItemResponse, isArray: true })
  items: ViewDocumentIndexItemResponse[]

  @ApiProperty({ type: ViewDocumentIndexResponseMeta })
  meta: ViewDocumentIndexResponseMeta

  constructor (response: GetDocumentIndexResponse, matchingSite: GetUserSiteIndexResponse) {
    this.items = response.files.map(file => new ViewDocumentIndexItemResponse(file, matchingSite))
    this.meta = new ViewDocumentIndexResponseMeta(response)
  }
}
