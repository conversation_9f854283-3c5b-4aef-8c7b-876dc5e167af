import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiForbiddenErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { ViewDocumentIndexResponse } from './view-document-index.response.js'
import { ViewDocumentIndexUseCase } from './view-document-index.use-case.js'
import { ViewDocumentIndexQuery } from './view-document-index.query.js'

@ApiTags('Document')
@ApiOAuth2([])
@Controller('documents')
export class ViewDocumentIndexController {
  constructor (
    private readonly useCase: ViewDocumentIndexUseCase
  ) {}

  @Get()
  @Permissions(
    Permission.DOCUMENT_READ_MASTER_TABLE,
    Permission.DOCUMENT_READ_TFS,
    Permission.DOCUMENT_READ_QUOTATION,
    Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS,
    Permission.DOCUMENT_READ_MANUAL,
    Permission.DOCUMENT_READ_BSC,
    Permission.DOCUMENT_READ_CONTRACT,
    Permission.DOCUMENT_READ_TRANSPORT
  )
  @ApiOkResponse({ type: ViewDocumentIndexResponse })
  @ApiForbiddenErrorResponse(ForbiddenError)
  async viewDocumentIndex (
    @Query() query: ViewDocumentIndexQuery
  ): Promise<ViewDocumentIndexResponse> {
    return this.useCase.execute(query)
  }
}
