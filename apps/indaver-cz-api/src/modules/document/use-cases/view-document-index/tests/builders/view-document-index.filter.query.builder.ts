import { randomUUID } from 'crypto'
import { ViewDocumentIndexFilterQuery } from '../../view-document-index.query.js'
import { SharepointDocumentViewName } from '../../../../../sharepoint/enums/sharepoint-document-view-name.enum.js'
import { SharepointDocumentStatus } from '../../../../../sharepoint/enums/sharepoint-document-status.enum.js'

export class ViewDocumentIndexFilterQueryBuilder {
  private filter: ViewDocumentIndexFilterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.filter = new ViewDocumentIndexFilterQuery()

    this.filter.customerUuid = randomUUID()
    this.filter.viewName = SharepointDocumentViewName.MEETINGS
    this.filter.wasteProducerIds = ['1', '2', '3']
    this.filter.status = undefined
    this.filter.year = undefined

    return this
  }

  withCustomerUuid (customerUuid: string): this {
    this.filter.customerUuid = customerUuid

    return this
  }

  withViewName (viewName: SharepointDocumentViewName): this {
    this.filter.viewName = viewName

    return this
  }

  withWasteProducerIds (ids: string[]): this {
    this.filter.wasteProducerIds = ids

    return this
  }

  withStatus (status: SharepointDocumentStatus): this {
    this.filter.status = status

    return this
  }

  withYear (year: string): this {
    this.filter.year = year

    return this
  }

  public build (): ViewDocumentIndexFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
