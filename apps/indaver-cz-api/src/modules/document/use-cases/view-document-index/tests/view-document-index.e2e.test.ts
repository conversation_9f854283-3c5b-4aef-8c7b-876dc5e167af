import { after, before, describe, it, mock } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import qs from 'qs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { ViewDocumentIndexValidator } from '../view-document-index.validator.js'
import { GetUserSiteIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index.response.builder.js'
import { GetUserSiteIndexWasteProducerResponseBuilder } from '../../../../sharepoint/use-cases/get-user-site-index/builders/get-user-site-index-waste-producer.builder.js'
import { GetDocumentIndexUseCase } from '../../../../sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { GetDocumentIndexResponseBuilder } from '../../../../sharepoint/use-cases/get-document-index/builders/get-document-index.response.builder.js'
import { GetDocumentIndexFileResponseBuilder } from '../../../../sharepoint/use-cases/get-document-index/builders/get-document-index-file.response.builder.js'
import { ViewDocumentIndexFilterQueryBuilder } from './builders/view-document-index.filter.query.builder.js'

describe('ViewDocumentIndex - E2E Test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 200', async () => {
    const user = await setup.authContext.getUser([Permission.DOCUMENT_READ_MASTER_TABLE])

    const site = new GetUserSiteIndexResponseBuilder()
      .withWasteProducerResource([new
      GetUserSiteIndexWasteProducerResponseBuilder()
        .withId(1)
        .build()]
      )
      .build()

    const validationMock = mock.method(ViewDocumentIndexValidator.prototype, 'validateAndReturnMatchingSite', () => {
      return site
    })

    const fileMock = mock.method(GetDocumentIndexUseCase.prototype, 'execute', () => {
      return new GetDocumentIndexResponseBuilder()
        .withFiles([
          new GetDocumentIndexFileResponseBuilder()
            .withCustomerName('1')
            .build()
        ])
        .build()
    })

    const response = await request(setup.httpServer)
      .get(`/documents`)
      .set('Authorization', `Bearer ${user.token}`)
      .query((
        qs.stringify({
          filter: new ViewDocumentIndexFilterQueryBuilder()
            .withWasteProducerIds([site.wasteProducerListId])
            .build()
        })))

    validationMock.mock.restore()
    fileMock.mock.restore()

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
