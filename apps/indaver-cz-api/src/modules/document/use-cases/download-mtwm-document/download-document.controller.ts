import { Body, Controller, HttpCode, HttpStatus, Post, Res } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiResponse } from '@nestjs/swagger'
import type { Response } from 'express'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { DocumentNotFoundError } from '../../errors/document-not-found-error.js'
import { DownloadDocumentUseCase } from './download-document.use-case.js'
import { DownloadDocumentCommand } from './download-document.command.js'

@ApiTags('Document')
@ApiOAuth2([])
@Controller('documents/download')
export class DownloadDocumentController {
  constructor (
    private readonly useCase: DownloadDocumentUseCase
  ) {}

  @Post()
  @HttpCode(HttpStatus.FOUND)
  @Permissions(
    Permission.DOCUMENT_READ_MASTER_TABLE,
    Permission.DOCUMENT_READ_TFS,
    Permission.DOCUMENT_READ_QUOTATION,
    Permission.DOCUMENT_READ_MINUTES_AND_PRESENTATIONS,
    Permission.DOCUMENT_READ_MANUAL,
    Permission.DOCUMENT_READ_BSC,
    Permission.DOCUMENT_READ_CONTRACT,
    Permission.DOCUMENT_READ_TRANSPORT
  )
  @ApiResponse({
    status: HttpStatus.FOUND
  })
  @ApiNotFoundErrorResponse(DocumentNotFoundError)
  async downloadDocument (
    @Body() command: DownloadDocumentCommand,
    @Res() res: Response
  ): Promise<void> {
    const url = await this.useCase.execute(command)

    res.setHeader('Location', url)
    res.redirect(url)
  }
}
