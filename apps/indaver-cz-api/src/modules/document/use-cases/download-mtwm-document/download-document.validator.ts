import { Injectable } from '@nestjs/common'
import { GetUserSiteIndexResponse } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { GetUserSiteIndexUseCase } from '../../../sharepoint/use-cases/get-user-site-index/get-user-site-index.use-case.js'
import { ForbiddenError } from '../../../exceptions/generic/forbidden.error.js'
import { GetDocumentIndexCommandBuilder } from '../../../sharepoint/use-cases/get-document-index/builders/get-document-index.command.builder.js'
import { GetDocumentIndexUseCase } from '../../../sharepoint/use-cases/get-document-index/get-document-index.use-case.js'
import { DocumentNotFoundError } from '../../errors/document-not-found-error.js'
import { ViewNameValidator } from '../../validators/view-name.validator.js'
import { DownloadDocumentCommand } from './download-document.command.js'

@Injectable()
export class DownloadDocumentValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly getUserSiteIndexUseCase: GetUserSiteIndexUseCase,
    private readonly getDocumentIndexUseCase: GetDocumentIndexUseCase,
    private readonly viewNameValidator: ViewNameValidator
  ) {}

  async validateAndReturnMatchingSite (
    command: DownloadDocumentCommand
  ): Promise<GetUserSiteIndexResponse> {
    const entraUpn = this.authContext.getAzureEntraUpn()
    const siteResponses = await this.getUserSiteIndexUseCase.execute(entraUpn)
    const matchingSite = siteResponses.find(r => r.siteId === command.customerUuid)

    if (matchingSite === undefined) {
      throw new ForbiddenError()
    }

    return matchingSite
  }

  async validateDocumentAccessible (
    command: DownloadDocumentCommand,
    matchingSite: GetUserSiteIndexResponse
  ): Promise<void> {
    const getDocumentIndexCommand = new GetDocumentIndexCommandBuilder()
      .withSiteId(command.customerUuid)
      .withPublishedDocumentsLibraryId(matchingSite.publishedDocumentsLibraryId)
      .withWebUrl(matchingSite.webUrl)
      .withWasteProducerIds(matchingSite.wasteProducerResource.map(wp => Number(wp.id)))
      .withItemIdFilter(command.documentId)
      .build()
    const response = await this.getDocumentIndexUseCase.execute(
      getDocumentIndexCommand
    )

    if (response.files.length === 0) {
      throw new DocumentNotFoundError({
        customerUuid: command.customerUuid,
        documentId: command.documentId
      })
    }

    await this.viewNameValidator.validateViewName(response.files[0].viewName)
  }
}
