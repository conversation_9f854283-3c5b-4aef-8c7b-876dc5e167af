import { ApiProperty } from '@nestjs/swagger'
import { ViewDocumentIndexFilterQuery } from '../view-document-index/view-document-index.query.js'

class DocumentFilterValue {
  @ApiProperty({ type: String, description: 'The internal key of the filter' })
  key: string

  @ApiProperty({ type: String, description: 'The display value of the filter' })
  value: string
}

export class DocumentFilterResponse {
  @ApiProperty({ type: String })
  filterName: keyof ViewDocumentIndexFilterQuery

  @ApiProperty({ type: DocumentFilterValue, isArray: true })
  filterValues: DocumentFilterValue[]
}

export class GetDocumentFiltersResponse {
  @ApiProperty({ type: DocumentFilterResponse, isArray: true })
  filters: DocumentFilterResponse[]

  constructor (filters: DocumentFilterResponse[]) {
    this.filters = filters
  }
}
