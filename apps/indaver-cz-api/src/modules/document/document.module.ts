import { Module } from '@nestjs/common'
import { ViewUserSiteIndexModule } from './use-cases/view-user-site-index/view-user-site-index.module.js'
import { ViewDocumentIndexModule } from './use-cases/view-document-index/view-document-index.module.js'
import { DownloadDocumentModule } from './use-cases/download-mtwm-document/download-document.module.js'
import { GetDocumentFiltersModule } from './use-cases/get-document-filters/get-document-filters.module.js'

@Module({
  imports: [
    DownloadDocumentModule,
    GetDocumentFiltersModule,
    ViewDocumentIndexModule,
    ViewUserSiteIndexModule
  ]
})
export class DocumentModule {}
