import { ApiProperty } from '@nestjs/swagger'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { tc } from '../../localization/helpers/translate.helper.js'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'

export class DocumentNotFoundErrorMeta {
  @ApiProperty({ type: String, format: 'uuid' })
  customerUuid: string

  @ApiProperty({ type: String })
  documentId: string
}

export class DocumentNotFoundError extends NotFoundApiError {
  @ApiErrorCode('document_not_found')
  code = 'document_not_found'

  @ApiErrorMeta()
  meta: DocumentNotFoundErrorMeta

  constructor (meta: DocumentNotFoundErrorMeta) {
    super(tc('error.document.not-found'))
    this.meta = meta
  }
}
