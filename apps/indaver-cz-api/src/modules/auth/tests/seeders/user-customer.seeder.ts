import type { EntityManager } from 'typeorm'
import { TypeOrmRepository } from '@wisemen/nestjs-typeorm'
import { AbstractSeeder } from '../../../../../test/seeders/abstract-seeder.js'
import { UserCustomer } from '../../entities/user-customer.entity.js'

export class UserCustomerSeeder extends AbstractSeeder<UserCustomer> {
  constructor (manager: EntityManager) {
    super(new TypeOrmRepository(UserCustomer, manager))
  }
}
