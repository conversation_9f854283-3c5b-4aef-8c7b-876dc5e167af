import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Request } from 'express'
import { UnauthorizedError } from '../../exceptions/generic/unauthorized.error.js'
import { AuthContext } from '../auth.context.js'
import { GLOBAL_CUSTOMER_REQUIRED_KEY } from '../decorators/no-customer-required.decorator.js'
import { UserCustomerAuthService } from '../services/user-customer-auth.service.js'
import { NotFoundError } from '../../exceptions/generic/not-found.error.js'

@Injectable()
export class SelectedCustomerGuard implements CanActivate {
  constructor (
    private readonly reflector: Reflector,
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService
  ) {}

  async canActivate (context: ExecutionContext): Promise<boolean> {
    const globalCustomerRequired = this.reflector.getAllAndOverride<boolean>(
      GLOBAL_CUSTOMER_REQUIRED_KEY,
      [context.getHandler(), context.getClass()]
    )
    if (!globalCustomerRequired) return true

    const request = context.switchToHttp().getRequest<Request>()
    const selectedCustomerId = request.headers['x-selected-customer'] as string | undefined

    const isInternalUser = this.authContext.isInternalUser()
    if (isInternalUser) {
      this.setSelectedCustomerId(selectedCustomerId ?? null)

      return true
    }

    if (selectedCustomerId == null) {
      throw new UnauthorizedError('error.auth.selected_customer_required')
    }

    await this.checkUserCustomerAccess(selectedCustomerId)
    this.setSelectedCustomerId(selectedCustomerId)

    return true
  }

  private setSelectedCustomerId (id: string | null): void {
    const authContent = this.authContext.getAuthOrFail()
    authContent.selectedCustomerId = id
  }

  private async checkUserCustomerAccess (customerId: string): Promise<void> {
    if (customerId === '') throw new NotFoundError('error.auth.customer_not_found')

    const canAccessCustomer = await this.userCustomerAuthService.canUserAccessCustomer(
      this.authContext.getAzureEntraUpn(),
      customerId
    )

    if (!canAccessCustomer) {
      throw new NotFoundError('error.auth.customer_not_found')
    }
  }
}
