import { Global, Module } from '@nestjs/common'
import { APP_GUARD } from '@nestjs/core'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PermissionModule } from '../permission/permission.module.js'
import { PermissionsGuard } from '../permission/guards/permission.guard.js'
import { UserModule } from '../../app/users/user.module.js'
import { CustomerModule } from '../customer/customer.module.js'
import { SapModule } from '../sap/sap.module.js'
import { AuthGuard } from './guards/auth.guard.js'
import { UserCustomerAuthService } from './services/user-customer-auth.service.js'
import { UserWasteProducerAuthService } from './services/user-waste-producer-auth.service.js'
import { CustomerPickUpAddressAuthService } from './services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from './services/customer-waste-producer-auth.service.js'
import { UserCustomer } from './entities/user-customer.entity.js'
import { AuthContext } from './auth.context.js'
import { AuthMiddleware } from './middleware/auth.middleware.js'
import { UserWasteProducer } from './entities/user-waste-producer.entity.js'
import { SelectedCustomerGuard } from './guards/selected-customer.guard.js'
import { GeneralInfoValidator } from './validators/general-info.validator.js'

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserCustomer,
      UserWasteProducer
    ]),
    UserModule,
    PermissionModule,
    CustomerModule,
    SapModule
  ],
  providers: [
    AuthContext,
    AuthMiddleware,
    {
      provide: APP_GUARD,
      useClass: AuthGuard
    },
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard
    },
    {
      provide: APP_GUARD,
      useClass: SelectedCustomerGuard
    },
    UserCustomerAuthService,
    UserWasteProducerAuthService,
    CustomerWasteProducerAuthService,
    CustomerPickUpAddressAuthService,
    GeneralInfoValidator
  ],
  exports: [
    AuthContext,
    UserCustomerAuthService,
    UserWasteProducerAuthService,
    CustomerWasteProducerAuthService,
    CustomerPickUpAddressAuthService,
    GeneralInfoValidator
  ]
})

export class AuthModule {}
