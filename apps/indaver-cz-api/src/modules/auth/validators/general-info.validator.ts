import { Injectable } from '@nestjs/common'
import { SelectedCustomerFilterMismatchError } from '../../customer/errors/selected-customer-filter-mismatch.error.js'
import { MissingRequiredFieldError } from '../../exceptions/generic/missing-required-field.error.js'
import { PickUpAddressNotAccessibleError } from '../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { AuthContext } from '../auth.context.js'
import { CustomerPickUpAddressAuthService } from '../services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../services/user-waste-producer-auth.service.js'

export interface GeneralInfoValidationOptions {
  customerId?: string
  wasteProducerId?: string
  pickUpAddressIds?: string | string[]
}

@Injectable()
export class GeneralInfoValidator {
  constructor (
    private readonly authContext: AuthContext,
    private readonly customerWasteProducerAuthService: CustomerWasteProducerAuthService,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly customerPickUpAddressAuthService: CustomerPickUpAddressAuthService
  ) {}

  async validate (options: GeneralInfoValidationOptions): Promise<void> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    const customerId = selectedCustomerId !== null
      ? selectedCustomerId
      : options.customerId ?? null

    if (customerId !== null) {
      this.validateCustomer(customerId)
    }

    if (options.wasteProducerId !== undefined) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }

      await this.validateWasteProducer(
        customerId,
        options.wasteProducerId
      )
    }
    if (options.pickUpAddressIds !== undefined) {
      if (customerId === null) {
        throw new MissingRequiredFieldError({ pointer: '$.customerId' })
      }

      const pickUpAddressIds = Array.isArray(options.pickUpAddressIds)
        ? options.pickUpAddressIds
        : [options.pickUpAddressIds]

      await this.validatePickUpAddresses(
        customerId,
        pickUpAddressIds
      )
    }
  }

  private validateCustomer (customerId: string): void {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    if (selectedCustomerId != null && selectedCustomerId !== customerId) {
      throw new SelectedCustomerFilterMismatchError({ pointer: '$.customerId' })
    }
  }

  private async validateWasteProducer (
    customerId: string,
    wasteProducerId: string
  ): Promise<void> {
    const canCustomerAccessWasteProducer = await this.customerWasteProducerAuthService
      .canCustomerAccessWasteProducer(
        customerId,
        wasteProducerId
      )

    if (!canCustomerAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }

    const userId = this.authContext.getAzureEntraUpn()

    const canUserAccessWasteProducer = await this.userWasteProducerAuthService
      .canUserAccessWasteProducer(
        userId,
        customerId,
        wasteProducerId
      )

    if (!canUserAccessWasteProducer) {
      throw new WasteProducerNotAccessibleError({ pointer: '$.wasteProducerId' })
    }
  }

  private async validatePickUpAddresses (
    customerId: string,
    pickUpAddressIds: string[]
  ): Promise<void> {
    if (pickUpAddressIds.length === 0) return

    const canCustomerAccessPickUpAddresses = await this.customerPickUpAddressAuthService
      .canCustomerAccessPickUpAddresses(
        customerId,
        pickUpAddressIds
      )

    if (!canCustomerAccessPickUpAddresses) {
      throw new PickUpAddressNotAccessibleError({ pointer: '$.pickUpAddressId' })
    }
  }
}
