import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { PickUpAddressNotAccessibleError } from '../../../pick-up-address/errors/pick-up-address-not-accessible.error.js'
import { WasteProducerNotAccessibleError } from '../../../waste-producer/errors/waste-producer-not-accessible.error.js'
import { AuthContext } from '../../auth.context.js'
import { CustomerPickUpAddressAuthService } from '../../services/customer-pick-up-address-auth.service.js'
import { CustomerWasteProducerAuthService } from '../../services/customer-waste-producer-auth.service.js'
import { UserWasteProducerAuthService } from '../../services/user-waste-producer-auth.service.js'
import { GeneralInfoValidationOptions, GeneralInfoValidator } from '../general-info.validator.js'

describe('General info validator unit test', () => {
  let validator: GeneralInfoValidator

  let options: GeneralInfoValidationOptions

  let authContext: SinonStubbedInstance<AuthContext>
  let customerWasteProducerAuthService: SinonStubbedInstance<CustomerWasteProducerAuthService>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let customerPickUpAddressAuthService: SinonStubbedInstance<CustomerPickUpAddressAuthService>

  before(() => {
    TestBench.setupUnitTest()

    options = {
      customerId: randomUUID(),
      wasteProducerId: randomUUID(),
      pickUpAddressIds: [randomUUID()]
    }

    authContext = createStubInstance(AuthContext)

    customerWasteProducerAuthService = createStubInstance(CustomerWasteProducerAuthService)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    customerPickUpAddressAuthService = createStubInstance(CustomerPickUpAddressAuthService)

    validator = new GeneralInfoValidator(
      authContext,
      customerWasteProducerAuthService,
      userWasteProducerAuthService,
      customerPickUpAddressAuthService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getAzureEntraUpn.returns(randomUUID())
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(true)
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(true)
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(true)
  }

  it('doesn\'t throw an error when validation passes', async () => {
    await expect(validator.validate(options))
      .resolves.not.toThrow()
  })

  it('throws an error when waste producer filter is given without customer', async () => {
    authContext.getSelectedCustomerId.returns(null)

    const options = {
      wasteProducerId: randomUUID()
    }

    await expect(validator.validate(options))
      .rejects.toThrow(MissingRequiredFieldError)
  })

  it('throws an error when waste producer is not accessible by customer', async () => {
    customerWasteProducerAuthService.canCustomerAccessWasteProducer.resolves(false)

    await expect(validator.validate(options))
      .rejects.toThrow(WasteProducerNotAccessibleError)
  })

  it('throws an error when waste producer is not accessible by auth user', async () => {
    userWasteProducerAuthService.canUserAccessWasteProducer.resolves(false)

    await expect(validator.validate(options))
      .rejects.toThrow(WasteProducerNotAccessibleError)
  })

  it('throws an error when pick-up address filter is given without customer', async () => {
    authContext.getSelectedCustomerId.returns(null)

    const options = {
      pickUpAddressIds: [randomUUID()]
    }

    await expect(validator.validate(options))
      .rejects.toThrow(MissingRequiredFieldError)
  })

  it('throws an error when pick-up addresses is not accessible by customer', async () => {
    customerPickUpAddressAuthService.canCustomerAccessPickUpAddresses.resolves(false)

    await expect(validator.validate(options))
      .rejects.toThrow(PickUpAddressNotAccessibleError)
  })
})
