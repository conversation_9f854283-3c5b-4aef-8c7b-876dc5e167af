import { Injectable } from '@nestjs/common'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetWasteProducerIndexUseCase } from '../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.use-case.js'
import { SapQuery } from '../../sap/query/sap-query.js'
import { SapGetWasteProducerIndexResponse } from '../../sap/use-cases/get-waste-producer-index/get-waste-producer-index.response.js'

@Injectable()
export class CustomerWasteProducerAuthService {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly getWasteProducerIndexUseCase: SapGetWasteProducerIndexUseCase
  ) {}

  async canCustomerAccessWasteProducer (
    customerId: string,
    wasteProducerId: string
  ): Promise<boolean> {
    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        customerId
      )

    const sapQuery = new SapQuery<SapGetWasteProducerIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
      .andWhere('WasteProducer', wasteProducerId)

    const wasteProducers = await this.getWasteProducerIndexUseCase.execute(sapQuery)

    return wasteProducers.items.length > 0
  }
}
