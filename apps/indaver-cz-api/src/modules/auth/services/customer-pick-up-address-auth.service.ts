import { Injectable } from '@nestjs/common'
import { SapQuery } from '../../sap/query/sap-query.js'
import { SapGetPickUpAddressIndexResponse } from '../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.response.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetPickUpAddressIndexUseCase } from '../../sap/use-cases/get-pick-up-address-index/get-pick-up-address-index.use-case.js'

@Injectable()
export class CustomerPickUpAddressAuthService {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly getPickUpAddressIndexUseCase: SapGetPickUpAddressIndexUseCase
  ) {}

  async canCustomerAccessPickUpAddress (
    customerId: string,
    pickUpAddressId: string
  ): Promise<boolean> {
    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        customerId
      )

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
      .andWhere('PickUpAddress', pickUpAddressId)

    const pickUpAddresses = await this.getPickUpAddressIndexUseCase.execute(sapQuery)

    return pickUpAddresses.items.length > 0
  }

  async canCustomerAccessPickUpAddresses (
    customerId: string,
    pickUpAddressIds: string[]
  ): Promise<boolean> {
    const uniquePickUpAddressIds = Array.from(new Set(pickUpAddressIds))

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        customerId
      )

    const sapQuery = new SapQuery<SapGetPickUpAddressIndexResponse>()
      .where('Customer', customerId)
      .andWhere('SalesOrganization', customerDefaultSalesOrganisationId)
      .andWhere((qb) => {
        qb.where('PickUpAddress', uniquePickUpAddressIds[0])
        for (let i = 1; i < uniquePickUpAddressIds.length; i++) {
          qb.orWhere('PickUpAddress', uniquePickUpAddressIds[i])
        }
        return qb
      })

    const pickUpAddresses = await this.getPickUpAddressIndexUseCase.execute(sapQuery)

    return pickUpAddresses.items.length === uniquePickUpAddressIds.length
  }
}
