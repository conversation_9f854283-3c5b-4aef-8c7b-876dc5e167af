import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { UserWasteProducer } from '../entities/user-waste-producer.entity.js'

@Injectable()
export class UserWasteProducerAuthService {
  constructor (
    @InjectRepository(UserWasteProducer)
    private readonly userWasteProducerRepository: Repository<UserWasteProducer>
  ) {}

  async canUserAccessWasteProducer (
    userId: string,
    customerId: string,
    wasteProducerId: string
  ): Promise<boolean> {
    const restrictedWasteProducers = await this.userWasteProducerRepository.findBy({
      userId,
      customerId
    })

    if (restrictedWasteProducers.length === 0) {
      return true // User can access all waste producers of the customer
    }

    return restrictedWasteProducers.some(
      wasteProducer => wasteProducer.wasteProducerId === wasteProducerId
    )
  }

  async getRestrictedWasteProducerIds (
    userId: string,
    customerId: string
  ): Promise<undefined | string[]> {
    const wasteProducers = await this.userWasteProducerRepository.findBy({
      userId,
      customerId
    })

    if (wasteProducers.length === 0) {
      return undefined // Can access all waste producers of the customer
    }

    return wasteProducers.map(wasteProducer => wasteProducer.wasteProducerId)
  }
}
