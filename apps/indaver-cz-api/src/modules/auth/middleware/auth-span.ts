import { Span } from '@opentelemetry/api'
import { AuthContent } from '../auth.context.js'

export function setAuthContentSpanAttributes (
  span: Span,
  content: AuthContent
): void {
  for (const [key, value] of Object.entries(content)) {
    if (
      typeof value === 'string'
      || typeof value === 'number'
      || typeof value === 'boolean'
    ) {
      span.setAttribute(key, value)
    } else {
      span.setAttribute(key, JSON.stringify(value))
    }
  }
}
