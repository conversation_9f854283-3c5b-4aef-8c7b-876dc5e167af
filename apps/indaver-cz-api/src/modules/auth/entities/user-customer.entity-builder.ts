import { randomUUID } from 'node:crypto'
import { UserCustomer } from './user-customer.entity.js'

export class UserCustomerEntityBuilder {
  private userCustomer: UserCustomer

  constructor () {
    this.userCustomer = new UserCustomer()
    this.userCustomer.userId = randomUUID()
    this.userCustomer.customerId = randomUUID()
  }

  withUserId (userId: string): this {
    this.userCustomer.userId = userId
    return this
  }

  withCustomerId (customerId: string): this {
    this.userCustomer.customerId = customerId
    return this
  }

  build (): UserCustomer {
    return this.userCustomer
  }
}
