import { CreateDateColumn, <PERSON><PERSON>ty, PrimaryColumn, UpdateDateColumn } from 'typeorm'
import { AUTHORIZATION_DB_SCHEME } from '../constants/authorization-db-scheme.constant.js'

@Entity({ schema: AUTHORIZATION_DB_SCHEME })
export class UserCustomer {
  @PrimaryColumn('citext')
  userId: string

  @PrimaryColumn('varchar')
  customerId: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date
}
