import { AsyncLocalStorage } from 'async_hooks'
import { Injectable } from '@nestjs/common'
import { UnauthorizedError } from '../exceptions/generic/unauthorized.error.js'

export interface AuthContent {
  zitadelSub: string | null
  uuid: string
  azureEntraId: string
  azureEntraUpn: string

  impersonateUserUuid: string | null
  impersonateAzureEntraId: string | null
  impersonateAzureEntraUpn: string | null

  selectedCustomerId: string | null
}

@Injectable()
export class AuthContext {
  private readonly authStorage = new AsyncLocalStorage<AuthContent>()

  public getAuthOrFail (): AuthContent {
    const token = this.authStorage.getStore()

    if (token == null) {
      throw new UnauthorizedError()
    }

    return token
  }

  public getAuth (): AuthContent | null {
    const token = this.authStorage.getStore()

    return token ?? null
  }

  public getUserUuidOrFail (): string {
    return this.getAuthOrFail().impersonateUserUuid ?? this.getAuthOrFail().uuid
  }

  public getUserUuid (): string | null {
    return this.getAuth()?.impersonateUserUuid ?? this.getAuth()?.uuid ?? null
  }

  public getAzureEntraId (): string {
    const azureEntraId = this.getAuthOrFail().impersonateAzureEntraId
      ?? this.getAuthOrFail().azureEntraId

    return azureEntraId
  }

  public getAzureEntraUpn (): string {
    const azureEntraUpn = this.getAuthOrFail().impersonateAzureEntraUpn
      ?? this.getAuthOrFail().azureEntraUpn

    return azureEntraUpn
  }

  public isInternalUser (): boolean {
    const azureEntraUpn = this.getAzureEntraUpn()

    const domain = azureEntraUpn.split('@').at(-1)

    return domain?.toLocaleLowerCase() === 'indaver.com'
  }

  public getSelectedCustomerId (): string | null {
    return this.getAuthOrFail().selectedCustomerId
  }

  public run (content: AuthContent, callback: () => void): void {
    this.authStorage.run(content, callback)
  }
}
