import { Modu<PERSON> } from '@nestjs/common'
import { ViewContactIndexModule } from './use-cases/view-contact-index/view-contact-index.module.js'
import { CreateContactModule } from './use-cases/create-contact/create-contact.module.js'
import { UpdateContactModule } from './use-cases/update-contact/update-contact.module.js'
import { DeleteContactModule } from './use-cases/delete-contact/delete-contact.module.js'

@Module({
  imports: [
    ViewContactIndexModule,
    CreateContactModule,
    UpdateContactModule,
    DeleteContactModule
  ]
})
export class ContactModule {}
