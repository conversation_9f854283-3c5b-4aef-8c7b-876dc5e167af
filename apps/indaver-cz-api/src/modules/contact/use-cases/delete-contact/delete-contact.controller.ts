import { Controller, Delete, HttpCode } from '@nestjs/common'
import { <PERSON>pi<PERSON><PERSON>s, ApiOAuth2, ApiNoContentResponse, ApiUnauthorizedResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { DeleteContactUseCase } from './delete-contact.use-case.js'

@ApiTags('Contact')
@ApiOAuth2([])
@Controller('contacts/:uuid')
export class DeleteContactController {
  constructor (
    private readonly useCase: DeleteContactUseCase
  ) { }

  @Delete()
  @HttpCode(204)
  @Permissions(Permission.CONTACT_MANAGE)
  @ApiNoContentResponse()
  @ApiUnauthorizedResponse()
  @ApiNotFoundErrorResponse()
  async deleteContact (
    @UuidParam('uuid') contactUuid: string
  ): Promise<void> {
    await this.useCase.execute(contactUuid)
  }
}
