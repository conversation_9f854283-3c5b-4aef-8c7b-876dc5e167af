import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { ContactEntityBuilder } from '../../../tests/contact-entity.builder.js'
import { Contact } from '../../../entities/contact.entity.js'

describe('Delete contact e2e test', () => {
  let setup: EndToEndTestSetup

  let contactRepository: Repository<Contact>

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    contactRepository = setup.dataSource.getRepository(Contact)
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .delete(`/contacts/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .delete(`/contacts/${randomUUID()}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('delete contact when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.CONTACT_MANAGE])

    const contact = await contactRepository.save(
      new ContactEntityBuilder()
        .withUserUuid(user.user.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .delete(`/contacts/${contact.uuid}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(204)
  })
})
