import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { Contact } from '../../../entities/contact.entity.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { DeleteContactUseCase } from '../delete-contact.use-case.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ContactDeletedEvent } from '../contact-deleted.event.js'

describe('Delete contact use-case unit test', () => {
  let useCase: DeleteContactUseCase

  let userUuid: string

  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>
  let contactRepository: SinonStubbedInstance<Repository<Contact>>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    eventEmitter = createStubInstance(DomainEventEmitter)

    contactRepository = createStubInstance<Repository<Contact>>(
        Repository<Contact>
    )

    useCase = new DeleteContactUseCase(
      stubDataSource(),
      authContext,
      eventEmitter,
      contactRepository
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    contactRepository.delete.resolves({
      raw: {},
      affected: 1
    })
  }

  it('Throws an error when contact not from current user', async () => {
    contactRepository.delete.resolves({
      raw: {},
      affected: 0
    })

    await expect(useCase.execute(randomUUID()))
      .rejects.toThrow(NotFoundError)
  })

  it('Emits a ContactDeleted event', async () => {
    const contactUuid = randomUUID()

    await useCase.execute(contactUuid)

    expect(eventEmitter).toHaveEmitted(new ContactDeletedEvent(contactUuid))
  })
})
