import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Contact } from '../../entities/contact.entity.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { ContactDeletedEvent } from './contact-deleted.event.js'

@Injectable()
export class DeleteContactUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>
  ) {}

  async execute (
    uuid: string
  ): Promise<void> {
    const userUuid = this.authContext.getUserUuidOrFail()

    await transaction(this.dataSource, async () => {
      const deleteResult = await this.contactRepository.delete({
        uuid,
        userUuid: userUuid
      })

      if (deleteResult.affected === 0) {
        throw new NotFoundError('error.contact.contact_not_found')
      }

      await this.eventEmitter.emit([new ContactDeletedEvent(uuid)])
    })
  }
}
