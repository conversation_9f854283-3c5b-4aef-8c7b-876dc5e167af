import { ApiProperty } from '@nestjs/swagger'
import { IsE<PERSON>, IsNotEmpty, IsString, MaxLength } from 'class-validator'

export class CreateContactCommand {
  @ApiProperty({ type: String, maxLength: 40 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(40)
  firstName: string

  @ApiProperty({ type: String, maxLength: 40 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(40)
  lastName: string

  @ApiProperty({ type: String, format: 'email', maxLength: 241 })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(241)
  email: string
}
