import { Body, Controller, Post } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { CreateContactCommand } from './create-contact.command.js'
import { CreateContactResponse } from './create-contact.response.js'
import { CreateContactUseCase } from './create-contact.use-case.js'

@ApiTags('Contact')
@ApiOAuth2([])
@Controller('contacts')
export class CreateContactController {
  constructor (
    private readonly useCase: CreateContactUseCase
  ) { }

  @Post()
  @Permissions(Permission.CONTACT_MANAGE)
  @ApiCreatedResponse({ type: CreateContactResponse })
  public async createContact (
    @Body() command: CreateContactCommand
  ): Promise<CreateContactResponse> {
    return await this.useCase.execute(command)
  }
}
