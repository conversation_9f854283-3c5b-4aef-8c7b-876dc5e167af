import { CreateContactCommand } from '../create-contact.command.js'

export class CreateContactCommandBuilder {
  private command: CreateContactCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new CreateContactCommand()

    this.command.firstName = 'John'
    this.command.lastName = 'Doe'
    this.command.email = '<EMAIL>'

    return this
  }

  build (): CreateContactCommand {
    const result = this.command

    this.reset()

    return result
  }
}
