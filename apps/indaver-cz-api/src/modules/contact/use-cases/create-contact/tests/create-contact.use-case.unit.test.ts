import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CreateContactUseCase } from '../create-contact.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { Contact } from '../../../entities/contact.entity.js'
import { ContactEntityBuilder } from '../../../tests/contact-entity.builder.js'
import { ContactCreatedEvent } from '../contact-created.event.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { CreateContactCommandBuilder } from './create-contact.command.builder.js'

describe('Create contact use-case unit test', () => {
  let useCase: CreateContactUseCase

  let userUuid: string

  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>
  let contactRepository: SinonStubbedInstance<Repository<Contact>>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    eventEmitter = createStubInstance(DomainEventEmitter)

    contactRepository = createStubInstance<Repository<Contact>>(
        Repository<Contact>, {
          create: new ContactEntityBuilder().build()
        }
    )

    useCase = new CreateContactUseCase(
      stubDataSource(),
      authContext,
      eventEmitter,
      contactRepository
    )
  })

  it('Emits a ContactCreated event', async () => {
    const command = new CreateContactCommandBuilder().build()

    const result = await useCase.execute(command)

    const expectedContact = new ContactEntityBuilder()
      .withUuid(result.uuid)
      .withFirstName(command.firstName)
      .withLastName(command.lastName)
      .withEmail(command.email)
      .build()

    expect(eventEmitter).toHaveEmitted(new ContactCreatedEvent(expectedContact))
  })
})
