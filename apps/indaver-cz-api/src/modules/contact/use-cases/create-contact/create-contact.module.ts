import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Modu<PERSON> } from '@nestjs/common'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { Contact } from '../../entities/contact.entity.js'
import { File } from '../../../files/entities/file.entity.js'
import { Create<PERSON>ontactController } from './create-contact.controller.js'
import { CreateContactUseCase } from './create-contact.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Contact, File]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateContactController
  ],
  providers: [
    CreateContactUseCase
  ]
})
export class CreateContactModule {}
