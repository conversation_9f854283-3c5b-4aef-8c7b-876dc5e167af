import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { Contact } from '../../entities/contact.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { ContactAlreadyExistsError } from '../../errors/contact-already-exists.error.js'
import { CreateContactResponse } from './create-contact.response.js'
import { CreateContactCommand } from './create-contact.command.js'
import { ContactCreatedEvent } from './contact-created.event.js'

@Injectable()
export class CreateContactUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>
  ) {}

  async execute (command: CreateContactCommand): Promise<CreateContactResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const contact = this.contactRepository.create({
      firstName: command.firstName,
      lastName: command.lastName,
      email: command.email,
      userUuid: userUuid
    })

    await transaction(this.dataSource, async () => {
      try {
        await this.contactRepository.insert(contact)
      } catch (error: unknown) {
        if (
          typeof error === 'object'
          && error !== null
          && 'code' in error
          && error.code === '23505'
        ) {
          throw new ContactAlreadyExistsError()
        }
      }
      await this.eventEmitter.emit([new ContactCreatedEvent(contact)])
    })

    return new CreateContactResponse(contact)
  }
}
