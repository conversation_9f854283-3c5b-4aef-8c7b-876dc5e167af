import { UpdateContactCommand } from '../update-contact.command.js'

export class UpdateContactCommandBuilder {
  private command: UpdateContactCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdateContactCommand()

    return this
  }

  withFirstName (firstName: string): this {
    this.command.firstName = firstName

    return this
  }

  withLastName (lastName: string): this {
    this.command.lastName = lastName

    return this
  }

  withEmail (email: string): this {
    this.command.email = email

    return this
  }

  build (): UpdateContactCommand {
    const result = this.command

    this.reset()

    return result
  }
}
