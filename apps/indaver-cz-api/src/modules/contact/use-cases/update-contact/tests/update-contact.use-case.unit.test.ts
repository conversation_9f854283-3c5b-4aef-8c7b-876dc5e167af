import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { Contact } from '../../../entities/contact.entity.js'
import { UpdateContactUseCase } from '../update-contact.use-case.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ContactUpdatedEvent } from '../contact-updated.event.js'
import { UpdateContactCommandBuilder } from './update-contact-command.builder.js'

describe('Update contact use-case unit test', () => {
  let useCase: UpdateContactUseCase

  let userUuid: string

  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>
  let contactRepository: SinonStubbedInstance<Repository<Contact>>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    eventEmitter = createStubInstance(DomainEventEmitter)

    contactRepository = createStubInstance<Repository<Contact>>(
        Repository<Contact>
    )

    useCase = new UpdateContactUseCase(
      stubDataSource(),
      authContext,
      eventEmitter,
      contactRepository
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    contactRepository.update.resolves({
      raw: {},
      affected: 1,
      generatedMaps: []
    })
  }

  it('Throws an error when contact not from current user', async () => {
    contactRepository.update.resolves({
      raw: {},
      affected: 0,
      generatedMaps: []
    })

    const command = new UpdateContactCommandBuilder().build()

    await expect(useCase.execute(randomUUID(), command))
      .rejects.toThrow(NotFoundError)
  })

  it('Emits a ContactUpdated event', async () => {
    const contactUuid = randomUUID()
    const command = new UpdateContactCommandBuilder().build()

    await useCase.execute(contactUuid, command)

    expect(eventEmitter).toHaveEmitted(new ContactUpdatedEvent(contactUuid))
  })
})
