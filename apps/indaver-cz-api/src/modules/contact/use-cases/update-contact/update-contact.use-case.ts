import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Contact } from '../../entities/contact.entity.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { ContactAlreadyExistsError } from '../../errors/contact-already-exists.error.js'
import { UpdateContactCommand } from './update-contact.command.js'
import { ContactUpdatedEvent } from './contact-updated.event.js'

@Injectable()
export class UpdateContactUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly eventEmitter: DomainEventEmitter,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>
  ) {}

  async execute (
    uuid: string,
    command: UpdateContactCommand
  ): Promise<void> {
    const userUuid = this.authContext.getUserUuidOrFail()

    await transaction(this.dataSource, async () => {
      try {
        const updateResult = await this.contactRepository.update({
          uuid,
          userUuid: userUuid
        }, {
          firstName: command.firstName,
          lastName: command.lastName,
          email: command.email
        })
        if (updateResult.affected === 0) {
          throw new NotFoundError('error.contact.contact_not_found')
        }
      } catch (error: unknown) {
        if (
          typeof error === 'object'
          && error !== null
          && 'code' in error
          && error.code === '23505'
        ) {
          throw new ContactAlreadyExistsError()
        }
        throw error
      }

      await this.eventEmitter.emit([new ContactUpdatedEvent(uuid)])
    })
  }
}
