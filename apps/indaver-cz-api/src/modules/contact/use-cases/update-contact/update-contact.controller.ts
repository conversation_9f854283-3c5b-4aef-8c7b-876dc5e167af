import { Body, Controller, HttpCode, Patch } from '@nestjs/common'
import { <PERSON>piTags, ApiOAuth2, ApiNoContentResponse, ApiUnauthorizedResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { UpdateContactUseCase } from './update-contact.use-case.js'
import { UpdateContactCommand } from './update-contact.command.js'

@ApiTags('Contact')
@ApiOAuth2([])
@Controller('contacts/:uuid')
export class UpdateContactController {
  constructor (
    private readonly useCase: UpdateContactUseCase
  ) { }

  @Patch()
  @HttpCode(204)
  @Permissions(Permission.CONTACT_MANAGE)
  @ApiNoContentResponse()
  @ApiUnauthorizedResponse()
  @ApiNotFoundErrorResponse()
  async updateContact (
    @UuidParam('uuid') contactUuid: string,
    @Body() command: UpdateContactCommand
  ): Promise<void> {
    await this.useCase.execute(contactUuid, command)
  }
}
