import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator'

export class UpdateContactCommand {
  @ApiProperty({ type: String, required: false, maxLength: 40 })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MaxLength(40)
  firstName?: string

  @ApiProperty({ type: String, required: false, maxLength: 40 })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MaxLength(40)
  lastName?: string

  @ApiProperty({ type: String, required: false, format: 'email', maxLength: 241 })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(241)
  email?: string
}
