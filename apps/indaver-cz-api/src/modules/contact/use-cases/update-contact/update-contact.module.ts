import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Contact } from '../../entities/contact.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { UpdateContactController } from './update-contact.controller.js'
import { UpdateContactUseCase } from './update-contact.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Contact]),
    DomainEventEmitterModule
  ],
  controllers: [
    UpdateContactController
  ],
  providers: [
    UpdateContactUseCase
  ]
})
export class UpdateContactModule {}
