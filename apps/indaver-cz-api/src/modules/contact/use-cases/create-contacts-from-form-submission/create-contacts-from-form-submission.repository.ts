import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Contact } from '../../entities/contact.entity.js'

@Injectable()
export class CreateContactsFromFormSubmissionRepository {
  constructor (
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>
  ) {}

  async insert (contacts: Contact[]): Promise<string[]> {
    const insertResult = await this.contactRepository.createQueryBuilder()
      .insert()
      .values(contacts)
      .orIgnore()
      .execute()

    const contactUuids = (insertResult.raw as { uuid?: string }[]).map(
      rawResult => rawResult?.uuid !== undefined
        ? rawResult.uuid
        : undefined
    ).filter(uuid => uuid !== undefined)

    return contactUuids
  }
}
