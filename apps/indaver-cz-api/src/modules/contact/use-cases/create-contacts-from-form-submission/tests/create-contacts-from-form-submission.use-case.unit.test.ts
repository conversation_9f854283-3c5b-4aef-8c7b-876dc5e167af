import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'node:crypto'
import Sinon, { SinonStubbedInstance, createStubInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CreateContactsFromFormSubmissionUseCase } from '../create-contacts-from-form-submission.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { WasteInquiry } from '../../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { WasteInquiryEntityBuilder } from '../../../../waste-inquiry/tests/waste-inquiry-entity.builder.js'
import { PickUpRequestEntityBuilder } from '../../../../pick-up-request/tests/pick-up-request-entity.builder.js'
import { FormType } from '../create-contacts-from-form-submission.job.js'
import { CreateContactsFromFormSubmissionRepository } from '../create-contacts-from-form-submission.repository.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { ContactCreatedEvent } from '../../create-contact/contact-created.event.js'
import { ContactEntityBuilder } from '../../../tests/contact-entity.builder.js'
import { Contact } from '../../../entities/contact.entity.js'
import { WeeklyPlanningRequest } from '../../../../weekly-planning-request/entities/weekly-planning-request.entity.js'
import { WeeklyPlanningRequestEntityBuilder } from '../../../../weekly-planning-request/tests/weekly-planning-request-entity.builder.js'

describe('Create contacts from form submission use-case unit test', () => {
  let useCase: CreateContactsFromFormSubmissionUseCase

  let userUuid: string
  let wasteInquiry: WasteInquiry
  let pickUpRequest: PickUpRequest
  let weeklyPlanningRequest: WeeklyPlanningRequest
  let contact: Contact

  let wasteInquiryRepository: SinonStubbedInstance<Repository<WasteInquiry>>
  let pickUpRequestRepository: SinonStubbedInstance<Repository<PickUpRequest>>
  let weeklyPlanningRequestRepository: SinonStubbedInstance<Repository<WeeklyPlanningRequest>>
  let contactRepository: SinonStubbedInstance<Repository<Contact>>
  let createContactsRepository: SinonStubbedInstance<CreateContactsFromFormSubmissionRepository>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    wasteInquiry = new WasteInquiryEntityBuilder().build()
    pickUpRequest = new PickUpRequestEntityBuilder().build()
    weeklyPlanningRequest = new WeeklyPlanningRequestEntityBuilder().build()
    contact = new ContactEntityBuilder().build()

    wasteInquiryRepository = createStubInstance<Repository<WasteInquiry>>(
      Repository<WasteInquiry>
    )
    pickUpRequestRepository = createStubInstance<Repository<PickUpRequest>>(
      Repository<PickUpRequest>
    )
    weeklyPlanningRequestRepository = createStubInstance<Repository<WeeklyPlanningRequest>>(
      Repository<WeeklyPlanningRequest>
    )
    contactRepository = createStubInstance<Repository<Contact>>(
        Repository<Contact>
    )
    createContactsRepository = createStubInstance(CreateContactsFromFormSubmissionRepository)

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new CreateContactsFromFormSubmissionUseCase(
      stubDataSource(),
      wasteInquiryRepository,
      pickUpRequestRepository,
      weeklyPlanningRequestRepository,
      contactRepository,
      createContactsRepository,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    wasteInquiryRepository.findOneByOrFail.resolves(wasteInquiry)
    pickUpRequestRepository.findOneByOrFail.resolves(pickUpRequest)
    weeklyPlanningRequestRepository.findOneByOrFail.resolves(weeklyPlanningRequest)
    contactRepository.create.returns(contact)
    createContactsRepository.insert.resolves([])
  }

  describe('Waste inquiry', () => {
    it('Completes when no contacts exist for a form', async () => {
      await expect(
        useCase.execute(userUuid, wasteInquiry.uuid, FormType.WASTE_INQUIRY)
      ).resolves.not.toThrow()
    })

    it('Emit a ContactCreatedEvent for every new contact', async () => {
      wasteInquiryRepository.findOneByOrFail.resolves({
        ...wasteInquiry,
        sendCopyToContacts: [{
          email: contact.email,
          firstName: contact.firstName,
          lastName: contact.lastName
        }]
      })
      createContactsRepository.insert.resolves([contact.uuid])

      await expect(
        useCase.execute(userUuid, wasteInquiry.uuid, FormType.WASTE_INQUIRY)
      ).resolves.not.toThrow()
      assert.calledOnce(eventEmitter.emit)
      expect(eventEmitter).toHaveEmitted(new ContactCreatedEvent(contact))
    })
  })

  describe('Pick-up request', () => {
    it('Completes when no contacts exist for a form', async () => {
      await expect(
        useCase.execute(userUuid, pickUpRequest.uuid, FormType.PICK_UP_REQUEST)
      ).resolves.not.toThrow()
    })

    it('Emit a ContactCreatedEvent for every new contact', async () => {
      const pickUpRequestWithContacts = new PickUpRequest()

      Object.assign(pickUpRequestWithContacts, pickUpRequest, {
        sendCopyToContacts: [{
          email: contact.email,
          firstName: contact.firstName,
          lastName: contact.lastName
        }]
      })

      pickUpRequestRepository.findOneByOrFail.resolves(pickUpRequestWithContacts)
      createContactsRepository.insert.resolves([contact.uuid])

      await expect(
        useCase.execute(userUuid, pickUpRequest.uuid, FormType.PICK_UP_REQUEST)
      ).resolves.not.toThrow()
      assert.calledOnce(eventEmitter.emit)
      expect(eventEmitter).toHaveEmitted(new ContactCreatedEvent(contact))
    })
  })

  describe('Weekly planning request', () => {
    it('Completes when no contacts exist for a form', async () => {
      await expect(
        useCase.execute(userUuid, weeklyPlanningRequest.uuid, FormType.WEEKLY_PLANNING_REQUEST)
      ).resolves.not.toThrow()
    })

    it('Emit a ContactCreatedEvent for every new contact', async () => {
      const weeklyPlanningRequestWithContacts = {
        ...weeklyPlanningRequest,
        sendCopyToContacts: [{
          email: contact.email,
          firstName: contact.firstName,
          lastName: contact.lastName
        }]
      }

      weeklyPlanningRequestRepository.findOneByOrFail.resolves(weeklyPlanningRequestWithContacts)
      createContactsRepository.insert.resolves([contact.uuid])

      await expect(
        useCase.execute(
          userUuid,
          weeklyPlanningRequestWithContacts.uuid,
          FormType.WEEKLY_PLANNING_REQUEST
        )
      ).resolves.not.toThrow()
      assert.calledOnce(eventEmitter.emit)
      expect(eventEmitter).toHaveEmitted(new ContactCreatedEvent(contact))
    })
  })
})
