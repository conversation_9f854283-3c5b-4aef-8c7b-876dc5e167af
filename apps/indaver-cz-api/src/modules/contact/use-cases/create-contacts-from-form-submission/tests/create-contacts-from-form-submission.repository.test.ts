import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CreateContactsFromFormSubmissionRepository } from '../create-contacts-from-form-submission.repository.js'
import { Contact } from '../../../entities/contact.entity.js'
import { ContactEntityBuilder } from '../../../tests/contact-entity.builder.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'

describe('Create contacts from form submission use-case unit test', () => {
  let repository: CreateContactsFromFormSubmissionRepository

  let contactRepository: Repository<Contact>

  let user: User
  let existingContact: Contact

  before(async () => {
    const setup = await TestBench.setupRepositoryTest()

    const userRepository = setup.dataSource.getRepository(User)

    contactRepository = setup.dataSource.getRepository(Contact)

    user = new UserEntityBuilder().build()
    await userRepository.insert(user)
    existingContact = new ContactEntityBuilder()
      .withUserUuid(user.uuid)
      .withEmail('<EMAIL>')
      .build()
    await contactRepository.insert(existingContact)

    repository = new CreateContactsFromFormSubmissionRepository(contactRepository)
  })

  describe('Insert', () => {
    it('Inserts non existing contacts based on email', async () => {
      const newContact = new ContactEntityBuilder()
        .withUserUuid(user.uuid)
        .withEmail('<EMAIL>')
        .build()

      const uuids = await repository.insert([existingContact, newContact])
      const contactCount = await contactRepository.countBy({ userUuid: user.uuid })

      expect(uuids).toHaveLength(1)
      expect(contactCount).toBe(2)
    })
  })
})
