import { BaseJob, BaseJobData, PgBossJob } from '@wisemen/pgboss-nestjs-job'
import { QueueName } from '../../../pgboss/enums/queue-name.enum.js'

export enum FormType {
  WASTE_INQUIRY = 'waste-inquiry',
  PICK_UP_REQUEST = 'pick-up-request',
  WEEKLY_PLANNING_REQUEST = 'weekly-planning-request'
}

export interface CreateContactsFromFormSubmissionJobData extends BaseJobData {
  userUuid: string
  formUuid: string
  formType: FormType
}

@PgBossJob(QueueName.SYSTEM)
export class CreateContactsFromFormSubmissionJob
  extends BaseJob<CreateContactsFromFormSubmissionJobData> {
  constructor (userUuid: string, formUuid: string, formType: FormType) {
    super({ userUuid, formUuid, formType })
  }

  uniqueBy (data: CreateContactsFromFormSubmissionJobData): string {
    return `create-contacts-from-form-submission-${data.formUuid}`
  }
}
