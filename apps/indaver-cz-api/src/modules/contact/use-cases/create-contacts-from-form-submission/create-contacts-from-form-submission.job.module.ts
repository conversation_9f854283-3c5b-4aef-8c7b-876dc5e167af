import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Contact } from '../../entities/contact.entity.js'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { WeeklyPlanningRequest } from '../../../weekly-planning-request/entities/weekly-planning-request.entity.js'
import { CreateContactsFromFormSubmissionJobHandler } from './create-contacts-from-form-submission.job-handler.js'
import { CreateContactsFromFormSubmissionUseCase } from './create-contacts-from-form-submission.use-case.js'
import { CreateContactsFromFormSubmissionRepository } from './create-contacts-from-form-submission.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Contact,
      WasteInquiry,
      PickUpRequest,
      WeeklyPlanningRequest
    ]),
    DomainEventEmitterModule
  ],
  providers: [
    CreateContactsFromFormSubmissionJobHandler,
    CreateContactsFromFormSubmissionUseCase,
    CreateContactsFromFormSubmissionRepository
  ]
})
export class CreateContactsFromFormSubmissionJobModule {}
