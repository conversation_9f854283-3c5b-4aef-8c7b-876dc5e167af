import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@wisemen/pgboss-nestjs-job'
import { Injectable } from '@nestjs/common'
import { CreateContactsFromFormSubmissionJob, CreateContactsFromFormSubmissionJobData } from './create-contacts-from-form-submission.job.js'
import { CreateContactsFromFormSubmissionUseCase } from './create-contacts-from-form-submission.use-case.js'

@Injectable()
@PgBossJobHandler(CreateContactsFromFormSubmissionJob)
export class CreateContactsFromFormSubmissionJobHandler extends
  JobHandler<CreateContactsFromFormSubmissionJob> {
  constructor (
    private readonly useCase: CreateContactsFromFormSubmissionUseCase
  ) {
    super()
  }

  async run (
    { userUuid, formUuid, formType }: CreateContactsFromFormSubmissionJobData
  ): Promise<void> {
    await this.useCase.execute(userUuid, formUuid, formType)
  }
}
