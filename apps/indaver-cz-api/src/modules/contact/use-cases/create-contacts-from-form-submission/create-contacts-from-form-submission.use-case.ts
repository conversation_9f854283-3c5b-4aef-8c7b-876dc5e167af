import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { Contact as ContactType } from '../../types/contact.type.js'

import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { ContactCreatedEvent } from '../create-contact/contact-created.event.js'
import { Contact } from '../../entities/contact.entity.js'
import { WeeklyPlanningRequest } from '../../../weekly-planning-request/entities/weekly-planning-request.entity.js'
import { FormType } from './create-contacts-from-form-submission.job.js'
import { CreateContactsFromFormSubmissionRepository } from './create-contacts-from-form-submission.repository.js'

@Injectable()
export class CreateContactsFromFormSubmissionUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>,
    @InjectRepository(WeeklyPlanningRequest)
    private readonly weeklyPlanningRequestRepository: Repository<WeeklyPlanningRequest>,
    @InjectRepository(Contact)
    private readonly contactRepository: Repository<Contact>,
    private readonly createContactsRepository: CreateContactsFromFormSubmissionRepository,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (userUuid: string, formUuid: string, formType: FormType): Promise<void> {
    let contacts: ContactType[]

    switch (formType) {
      case FormType.WASTE_INQUIRY: {
        const wasteInquiry = await this.wasteInquiryRepository.findOneByOrFail({ uuid: formUuid })

        contacts = wasteInquiry.sendCopyToContacts

        break
      }
      case FormType.PICK_UP_REQUEST: {
        const pickUpRequest = await this.pickUpRequestRepository.findOneByOrFail({ uuid: formUuid })

        contacts = pickUpRequest.sendCopyToContacts

        break
      }
      case FormType.WEEKLY_PLANNING_REQUEST: {
        const weeklyPlanningRequest = await this.weeklyPlanningRequestRepository.findOneByOrFail(
          { uuid: formUuid }
        )

        contacts = weeklyPlanningRequest.sendCopyToContacts

        break
      }
      default:
        exhaustiveCheck(formType)
    }

    if (contacts.length === 0) return

    const contactEntities = contacts.map((contact) => {
      return this.contactRepository.create({
        firstName: contact.firstName,
        lastName: contact.lastName,
        email: contact.email,
        userUuid: userUuid
      })
    })

    await transaction(this.dataSource, async () => {
      const insertedUuids = await this.createContactsRepository.insert(contactEntities)

      const events: ContactCreatedEvent[] = []
      for (const insertedUuid of insertedUuids) {
        const contact = contactEntities.find(contact => contact.uuid === insertedUuid)

        if (contact !== undefined) {
          events.push(new ContactCreatedEvent(contact))
        }
      }

      if (events.length > 0) {
        await this.eventEmitter.emit(events)
      }
    })
  }
}
