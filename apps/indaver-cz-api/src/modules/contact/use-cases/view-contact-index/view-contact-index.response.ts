import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { TypesenseContact } from '../../typesense/typesense-contact.js'

export class ContactResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  @ApiProperty({ type: String, format: 'email' })
  email: string

  constructor (contact: TypesenseContact) {
    this.uuid = contact.uuid
    this.firstName = contact.firstName
    this.lastName = contact.lastName
    this.email = contact.email
  }
}

export class ViewContactIndexResponse extends PaginatedOffsetResponse<ContactResponse> {
  @ApiProperty({ type: ContactResponse, isArray: true })
  declare items: ContactResponse[]

  constructor (items: TypesenseContact[], total: number, limit: number, offset: number) {
    const result = items.map(contact => new ContactResponse(contact))

    super(result, total, limit, offset)
  }
}
