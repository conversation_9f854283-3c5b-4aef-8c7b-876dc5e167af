import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewContactIndexRepository } from './view-contact-index.repository.js'
import { ViewContactIndexQuery } from './view-contact-index.query.js'
import { ViewContactIndexResponse } from './view-contact-index.response.js'

@Injectable()
export class ViewContactIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly contactTypesenseRepository: ViewContactIndexRepository
  ) {}

  async execute (query: ViewContactIndexQuery): Promise<ViewContactIndexResponse> {
    const pagination = typeormPagination(query.pagination)
    const userUuid = this.authContext.getUserUuidOrFail()

    const [contacts, count] = await this.contactTypesenseRepository.findPaginated(query, userUuid)

    return new ViewContactIndexResponse(
      contacts,
      count,
      pagination.take,
      pagination.skip
    )
  }
}
