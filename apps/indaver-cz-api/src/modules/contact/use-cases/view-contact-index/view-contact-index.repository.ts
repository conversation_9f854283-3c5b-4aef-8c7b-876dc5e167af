import { Injectable } from '@nestjs/common'
import type { SearchParams } from 'typesense/lib/Typesense/Documents.js'
import { SortDirection } from '@wisemen/pagination'
import { TypesenseQueryService } from '../../../typesense/services/typesense-query.service.js'
import { TypesenseContact } from '../../typesense/typesense-contact.js'
import { TypesenseCollectionName } from '../../../typesense/collections/typesense-collection-name.enum.js'
import { TypesenseSearchParamsBuilder } from '../../../typesense/param-builders/search-params.builder.js'
import { TypesenseOperationMode } from '../../../typesense/param-builders/enums/typesense-operation-mode.enum.js'
import { ContactTypesenseCollection } from '../../typesense/contact.collections.js'
import { ViewContactIndexQuery } from './view-contact-index.query.js'

@Injectable()
export class ViewContactIndexRepository {
  constructor (
    private readonly typesenseService: TypesenseQueryService
  ) {}

  async findPaginated (
    query: ViewContactIndexQuery,
    userUuid: string
  ): Promise<[items: TypesenseContact[], count: number]> {
    const typesenseSearchParams = this.createTypesenseSearchParams(query, userUuid)

    const contacts = await this.typesenseService.search(
      TypesenseCollectionName.CONTACT,
      typesenseSearchParams
    )

    return [
      contacts?.items ?? [],
      contacts?.meta.total ?? 0
    ]
  }

  private createTypesenseSearchParams (
    query: ViewContactIndexQuery,
    userUuid: string
  ): SearchParams<object> {
    const searchParamBuilder
      = new TypesenseSearchParamsBuilder<ContactTypesenseCollection>()
        .withQuery(query.search)
        .withOffset(query.pagination?.offset)
        .withLimit(query.pagination?.limit)
        .addSearchOn(
          ['firstName', 'lastName', 'email'],
          [
            TypesenseOperationMode.OFF,
            TypesenseOperationMode.OFF,
            TypesenseOperationMode.FALLBACK
          ]
        )
        .addFilterOn('userUuid', [userUuid])
        .addSortOn('firstName', SortDirection.ASC)
        .addSortOn('lastName', SortDirection.ASC)

    return searchParamBuilder.build()
  }
}
