import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('View contact index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/contacts`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/contacts`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns customers when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.CONTACT_READ])

    const response = await request(setup.httpServer)
      .get(`/contacts`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
