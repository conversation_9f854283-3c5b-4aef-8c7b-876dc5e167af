import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { ViewContactIndexUseCase } from '../view-contact-index.use-case.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewContactIndexRepository } from '../view-contact-index.repository.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ViewContactIndexQueryBuilder } from './view-contact-index-query.builder.js'

describe('View contact index use-case unit test', () => {
  let useCase: ViewContactIndexUseCase

  let userUuid: string

  let contactTypesenseRepository: SinonStubbedInstance<ViewContactIndexRepository>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    contactTypesenseRepository = createStubInstance(ViewContactIndexRepository, {
      findPaginated: Promise.resolve([[], 0])
    })

    useCase = new ViewContactIndexUseCase(
      authContext,
      contactTypesenseRepository
    )
  })

  it('Calls the typesense repository method once', async () => {
    const query = new ViewContactIndexQueryBuilder().build()

    await useCase.execute(query)

    assert.calledOnce(contactTypesenseRepository.findPaginated)
  })
})
