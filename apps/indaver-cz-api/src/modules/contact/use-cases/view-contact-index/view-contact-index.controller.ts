import { Controller, Get, Query } from '@nestjs/common'
import { <PERSON>piT<PERSON><PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewContactIndexResponse } from './view-contact-index.response.js'
import { ViewContactIndexQuery } from './view-contact-index.query.js'
import { ViewContactIndexUseCase } from './view-contact-index.use-case.js'

@ApiTags('Contact')
@ApiOAuth2([])
@Controller('contacts')
export class ViewContactIndexController {
  constructor (
    private readonly useCase: ViewContactIndexUseCase
  ) { }

  @Get()
  @Permissions(Permission.CONTACT_READ, Permission.CONTACT_MANAGE)
  @ApiOkResponse({ type: ViewContactIndexResponse })
  public async viewContactIndex (
    @Query() query: ViewContactIndexQuery
  ): Promise<ViewContactIndexResponse> {
    return await this.useCase.execute(query)
  }
}
