import { Column, CreateDateColumn, Entity, <PERSON>inColumn, ManyToOne, PrimaryGeneratedColumn, type Relation, Unique, UpdateDateColumn } from 'typeorm'
import { User } from '../../../app/users/entities/user.entity.js'

@Entity()
@Unique(['userUuid', 'email'])
export class Contact {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar' })
  firstName: string

  @Column({ type: 'varchar' })
  lastName: string

  @Column({ type: 'varchar' })
  email: string

  @Column({ type: 'uuid' })
  userUuid: string

  @ManyToOne(() => User, user => user.contacts)
  @JoinColumn({ name: 'user_uuid' })
  user?: Relation<User>
}
