import { randomUUID } from 'node:crypto'
import { Contact } from '../entities/contact.entity.js'

export class ContactEntityBuilder {
  private contact: Contact

  constructor () {
    this.reset()
  }

  reset (): this {
    this.contact = new Contact()

    this.contact.uuid = randomUUID()
    this.contact.createdAt = new Date()
    this.contact.updatedAt = new Date()
    this.contact.firstName = 'John'
    this.contact.lastName = 'Doe'
    this.contact.email = '<EMAIL>'
    this.contact.userUuid = randomUUID()

    return this
  }

  withUuid (uuid: string): this {
    this.contact.uuid = uuid

    return this
  }

  withUserUuid (userUuid: string): this {
    this.contact.userUuid = userUuid

    return this
  }

  withFirstName (firstName: string): this {
    this.contact.firstName = firstName

    return this
  }

  withLastName (lastName: string): this {
    this.contact.lastName = lastName

    return this
  }

  withEmail (email: string): this {
    this.contact.email = email

    return this
  }

  build (): Contact {
    const result = this.contact

    this.reset()

    return result
  }
}
