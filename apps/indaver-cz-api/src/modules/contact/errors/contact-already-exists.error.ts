import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class ContactAlreadyExistsError extends BadRequestApiError {
  @ApiErrorCode('contact_already_exists')
  readonly code = 'contact_already_exists'

  readonly meta: never

  constructor () {
    const detail = translateCurrent('error.contact.contact_already_exists')
    super(detail)
  }
}
