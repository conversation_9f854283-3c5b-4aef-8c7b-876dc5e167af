import { ApiProperty } from '@nestjs/swagger'
import { IsEmail, IsNotEmpty, IsString, MaxLength } from 'class-validator'

export class Contact {
  @ApiProperty({ type: String, format: 'email', maxLength: 241 })
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  @MaxLength(241)
  email: string

  @ApiProperty({ type: String, maxLength: 40 })
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  firstName: string

  @ApiProperty({ type: String, maxLength: 40 })
  @IsString()
  @IsNotEmpty()
  @MaxLength(40)
  lastName: string
}
