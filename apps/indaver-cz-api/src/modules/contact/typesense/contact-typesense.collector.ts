import { AnyOrIgnore, InjectRepository } from '@wisemen/nestjs-typeorm'
import { MoreThanOrEqual, Repository } from 'typeorm'
import { Contact } from '../entities/contact.entity.js'
import { RegisterTypesenseCollector } from '../../typesense/collectors/typesense-collector.decorator.js'
import { TypesenseCollectionName } from '../../typesense/collections/typesense-collection-name.enum.js'
import { TypesenseCollector } from '../../typesense/collectors/typesense-collector.js'
import { TypesenseContact } from './typesense-contact.js'

@RegisterTypesenseCollector(TypesenseCollectionName.CONTACT)
export class ContactTypesenseCollector implements TypesenseCollector {
  constructor (
    @InjectRepository(Contact) private readonly contactRepository: Repository<Contact>
  ) {}

  transform (contacts: Contact[]): TypesenseContact[] {
    return contacts.map(contact => new TypesenseContact(contact))
  }

  async fetch (uuids?: string[]): Promise<Contact[]> {
    return await this.contactRepository.find({
      where: { uuid: AnyOrIgnore(uuids) }
    })
  }

  async fetchChanged (since: Date): Promise<Contact[]> {
    return await this.contactRepository.find({
      where: [
        { updatedAt: MoreThanOrEqual(since) }
      ]
    })
  }

  async fetchRemoved (_since: Date): Promise<string[]> {
    return Promise.resolve([])
  }
}
