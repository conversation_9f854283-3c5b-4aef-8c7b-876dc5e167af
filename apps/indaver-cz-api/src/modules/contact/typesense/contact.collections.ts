import { TypesenseCollectionName } from '../../typesense/collections/typesense-collection-name.enum.js'
import { RegisterTypesenseCollection } from '../../typesense/collections/typesense-collection.decorator.js'
import { TypesenseCollection } from '../../typesense/collections/typesense.collection.js'

@RegisterTypesenseCollection(TypesenseCollectionName.CONTACT)
export class ContactTypesenseCollection extends TypesenseCollection {
  readonly name = TypesenseCollectionName.CONTACT

  readonly searchableFields = [
    { name: 'firstName', type: 'string', sort: true, infix: true },
    { name: 'lastName', type: 'string', sort: true, infix: true },
    { name: 'email', type: 'string', sort: true, infix: true }
  ] as const

  readonly filterableFields = [
    { name: 'userUuid', type: 'string', optional: true }
  ] as const

  readonly referenceFields = [] as const
}
