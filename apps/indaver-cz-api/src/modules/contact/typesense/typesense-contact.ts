import { Contact } from '../entities/contact.entity.js'

export class TypesenseContact {
  id: string
  uuid: string
  firstName: string
  lastName: string
  email: string
  userUuid: string

  constructor (contact: Contact) {
    return {
      id: contact.uuid,
      uuid: contact.uuid,
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      userUuid: contact.userUuid
    }
  }
}
