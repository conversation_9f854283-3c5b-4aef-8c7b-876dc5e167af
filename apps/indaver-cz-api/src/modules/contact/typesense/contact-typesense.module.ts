import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { PgBossSchedulerModule } from '@wisemen/pgboss-nestjs-job'
import { Contact } from '../entities/contact.entity.js'
import { ContactTypesenseCollector } from './contact-typesense.collector.js'
import { ContactTypesenseCollection } from './contact.collections.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([Contact]),
    PgBossSchedulerModule
  ],
  providers: [
    ContactTypesenseCollector,
    ContactTypesenseCollection
  ],
  exports: [ContactTypesenseCollector]
})
export class TypesenseContactModule {}
