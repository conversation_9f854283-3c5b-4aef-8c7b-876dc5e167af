import { Injectable } from '@nestjs/common'
import { PgBossScheduler } from '@wisemen/pgboss-nestjs-job'
import { Subscribe } from '../../domain-events/subscribe.decorator.js'
import { ContactCreatedEvent } from '../use-cases/create-contact/contact-created.event.js'
import { ContactUpdatedEvent } from '../use-cases/update-contact/contact-updated.event.js'
import { SyncTypesenseJob } from '../../typesense/use-cases/sync-collection/sync-typesense-collection.job.js'
import { TypesenseCollectionName } from '../../typesense/collections/typesense-collection-name.enum.js'
import { ContactDeletedEvent } from '../use-cases/delete-contact/contact-deleted.event.js'
import { RemoveFromTypesenseJob } from '../../typesense/jobs/remove-from-typesense/remove-from-typesense.job.js'

@Injectable()
export class ContactTypesenseSubscriber {
  constructor (
    private readonly jobScheduler: PgBossScheduler
  ) {}

  @Subscribe(ContactCreatedEvent)
  @Subscribe(ContactUpdatedEvent)
  async handle (): Promise<void> {
    const job = new SyncTypesenseJob(TypesenseCollectionName.CONTACT)
    await this.jobScheduler.scheduleJob(job)
  }

  @Subscribe(ContactDeletedEvent)
  async handleDelete (events: ContactDeletedEvent[]): Promise<void> {
    const jobs = events.map(event => new RemoveFromTypesenseJob({
      collection: TypesenseCollectionName.CONTACT,
      uuid: event.content.contactUuid
    }))
    await this.jobScheduler.scheduleJobs(jobs)
  }
}
