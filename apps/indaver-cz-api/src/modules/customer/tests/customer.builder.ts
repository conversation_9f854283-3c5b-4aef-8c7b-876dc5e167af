import { Address } from '../../../utils/address/types/address.type.js'
import { Customer } from '../types/customer.type.js'

export class CustomerBuilder {
  private readonly customer: Customer

  constructor () {
    this.customer = {
      id: 'customer-id',
      name: 'Customer name',
      address: null
    }
  }

  withId (id: string): this {
    this.customer.id = id
    return this
  }

  withName (name: string): this {
    this.customer.name = name
    return this
  }

  withAddress (address: Address): this {
    this.customer.address = address ?? null
    return this
  }

  build (): Customer {
    return this.customer
  }
}
