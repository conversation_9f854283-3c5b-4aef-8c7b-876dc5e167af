import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { GetCustomerDefaultSalesOrganisationIdModule } from '../get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.module.js'
import { ViewCustomerCountryUseCase } from './view-customer-country.use-case.js'
import { ViewCustomerCountryController } from './view-customer-country.controller.js'

@Module({
  imports: [
    SapModule,
    GetCustomerDefaultSalesOrganisationIdModule
  ],
  controllers: [
    ViewCustomerCountryController
  ],
  providers: [
    ViewCustomerCountryUseCase
  ],
  exports: [
    ViewCustomerCountryUseCase
  ]
})
export class ViewCustomerCountryModule { }
