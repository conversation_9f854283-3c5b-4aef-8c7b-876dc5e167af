import { Controller, Get, Param } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { CustomerNotFoundError } from '../../errors/customer-not-found.error.js'
import { ViewCustomerCountryResponse } from './view-customer-country.response.js'
import { ViewCustomerCountryUseCase } from './view-customer-country.use-case.js'

@ApiTags('Customer')
@ApiOAuth2([])
@Controller('customers/:id/country')
export class ViewCustomerCountryController {
  constructor (
    private readonly viewCustomerCountryUseCase: ViewCustomerCountryUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewCustomerCountryResponse })
  @ApiNotFoundErrorResponse(CustomerNotFoundError)
  public async viewCustomerCountry (
    @Param('id') id: string
  ): Promise<ViewCustomerCountryResponse> {
    return this.viewCustomerCountryUseCase.execute(id)
  }
}
