import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewCustomerCountryUseCase } from '../view-customer-country.use-case.js'
import { CustomerNotFoundError } from '../../../errors/customer-not-found.error.js'
import { SapGetSalesOrganisationIndexUseCase } from '../../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { GetSalesOrganisationIndexResponseBuilder } from '../../../../sap/use-cases/get-sales-organisation-index/tests/get-sales-organisation-index.response.builder.js'

describe('View customer country use case unit tests', () => {
  let useCase: ViewCustomerCountryUseCase

  let getCustomerSalesOrganisationId: SinonStubbedInstance<
    GetCustomerDefaultSalesOrganisationIdUseCase
  >
  let getSalesOrganisationIndexUseCase: SinonStubbedInstance<SapGetSalesOrganisationIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    getCustomerSalesOrganisationId = createStubInstance(
      GetCustomerDefaultSalesOrganisationIdUseCase
    )
    getSalesOrganisationIndexUseCase = createStubInstance(SapGetSalesOrganisationIndexUseCase)

    useCase = new ViewCustomerCountryUseCase(
      getCustomerSalesOrganisationId,
      getSalesOrganisationIndexUseCase
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    getCustomerSalesOrganisationId.getOrganisationIdOrFail.resolves(randomUUID())
    getSalesOrganisationIndexUseCase.execute.resolves([
      new GetSalesOrganisationIndexResponseBuilder().build()
    ])
  }

  it('throws an error when the customer does not exist', async () => {
    getSalesOrganisationIndexUseCase.execute.resolves([])

    const customerId = randomUUID()
    await expect(useCase.execute(customerId)).rejects.toThrow(new CustomerNotFoundError(customerId))
  })

  it('Call all methods once', async () => {
    await useCase.execute(randomUUID())

    assert.calledOnce(getCustomerSalesOrganisationId.getOrganisationIdOrFail)
    assert.calledOnce(getSalesOrganisationIndexUseCase.execute)
  })
})
