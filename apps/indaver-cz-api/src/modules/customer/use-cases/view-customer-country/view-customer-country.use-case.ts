import { Injectable } from '@nestjs/common'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetSalesOrganisationIndexUseCase } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetSalesOrganisationIndexResponse } from '../../../sap/use-cases/get-sales-organisation-index/get-sales-organisation-index.response.js'
import { CustomerNotFoundError } from '../../errors/customer-not-found.error.js'
import { ViewCustomerCountryResponse } from './view-customer-country.response.js'

@Injectable()
export class ViewCustomerCountryUseCase {
  constructor (
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly getSalesOrganisationIndexUseCase: SapGetSalesOrganisationIndexUseCase
  ) { }

  public async execute (customerId: string): Promise<ViewCustomerCountryResponse> {
    const salesOrganisationId = await this.getCustomerSalesOrganisationId.getOrganisationIdOrFail(
      customerId
    )

    const query = this.getSapQuery(salesOrganisationId)
    const salesOrganisations = await this.getSalesOrganisationIndexUseCase.execute(query)

    if (salesOrganisations.length === 0) throw new CustomerNotFoundError(customerId)

    const salesOrganisation = salesOrganisations[0]

    return new ViewCustomerCountryResponse(salesOrganisation.Country)
  }

  private getSapQuery (
    salesOrganisationId: string
  ): SapQuery<SapGetSalesOrganisationIndexResponse> {
    const query = new SapQuery<SapGetSalesOrganisationIndexResponse>()
      .where('SalesOrganization', salesOrganisationId)
      .setTop(1)

    return query
  }
}
