import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { randFullName } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewSuggestedCustomersUseCase } from '../view-suggested-customers.use-case.js'
import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { SUGGESTED_CUSTOMERS_AMOUNT } from '../../../../../utils/constants/suggested-entities-amount.constant.js'
import { ViewSuggestedCustomersRepository } from '../view-suggested-customers.repository.js'
import { FilterOperator } from '../../../../sap/enums/odata-filter-operator.enum.js'
import { Customer } from '../../../types/customer.type.js'
import { MapCustomerSapService } from '../../../services/map-customer-sap.service.js'
import { SapGetCustomerIndexUseCase } from '../../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { SapPaginatedResponseBuilder } from '../../../../sap/tests/builders/responses/sap-paginated.response.builder.js'
import { SapQuery } from '../../../../sap/query/sap-query.js'
import { ViewSuggestedCustomersFilterQueryBuilder } from './view-suggested-customers.filter.builder.js'
import { ViewSuggestedCustomersQueryBuilder } from './view-suggested-customers.query.builder.js'

describe('View suggested customers use-case unit test', () => {
  let useCase: ViewSuggestedCustomersUseCase

  let authContext: SinonStubbedInstance<AuthContext>
  let userCustomerAuthService: SinonStubbedInstance<UserCustomerAuthService>
  let repository: SinonStubbedInstance<ViewSuggestedCustomersRepository>
  let sapGetCustomerIndex: SinonStubbedInstance<SapGetCustomerIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    authContext = createStubInstance(AuthContext)
    userCustomerAuthService = createStubInstance(UserCustomerAuthService)
    repository = createStubInstance(ViewSuggestedCustomersRepository)
    sapGetCustomerIndex = createStubInstance(SapGetCustomerIndexUseCase)

    useCase = new ViewSuggestedCustomersUseCase(
      authContext,
      userCustomerAuthService,
      repository,
      sapGetCustomerIndex
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.resolves(randomUUID())
    userCustomerAuthService.getAccessibleCustomerIds.resolves([])
    repository.findRecentWasteInquiryCustomerIds.resolves([])
    repository.findRecentPickUpRequestCustomerIds.resolves([])
    sapGetCustomerIndex.execute.resolves(
      new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>().build()
    )
  }

  describe ('Request type "waste"', () => {
    const query = new ViewSuggestedCustomersQueryBuilder()
      .withFilter(
        new ViewSuggestedCustomersFilterQueryBuilder()
          .withRequestType(RequestType.WASTE)
          .build()
      )
      .build()

    it('Retrieves first X-amount of customers when no past waste inquiry', async () => {
      const accessibleCustomerIds = [randomUUID(), randomUUID()]
      userCustomerAuthService.getAccessibleCustomerIds.resolves(accessibleCustomerIds)

      await useCase.execute(query)

      const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()

      sapQuery.where((qb) => {
        qb.where('Customer', accessibleCustomerIds[0])
        for (let i = 1; i < accessibleCustomerIds.length; i++) {
          qb.orWhere('Customer', accessibleCustomerIds[i])
        }
        return qb
      })

      sapQuery.addOrderBy('CustomerName', 'asc')
      sapQuery.setTop(SUGGESTED_CUSTOMERS_AMOUNT)

      assert.calledOnceWithExactly(sapGetCustomerIndex.execute, sapQuery)
    })

    it('Retrieves customers of past waste inquiries completed with customers alphabetically until X-amount', async () => {
      const customers: Customer[] = [{
        id: randomUUID(),
        name: randFullName(),
        address: null
      }, {
        id: randomUUID(),
        name: randFullName(),
        address: null
      }]
      const customerIds = customers.map(customer => customer.id)

      const sapResults = MapCustomerSapService.mapCustomersToResults(customers)

      userCustomerAuthService.getAccessibleCustomerIds.resolves(customerIds)
      repository.findRecentWasteInquiryCustomerIds.resolves(customerIds)
      sapGetCustomerIndex.execute.resolves(
        new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
          .addItem(sapResults)
          .build()
      )

      await useCase.execute(query)

      const sapQuerySpecific = new SapQuery<SapGetCustomerIndexResponse>()
        .where('Customer', customerIds[0])

      for (let i = 1; i < customerIds.length; i++) {
        sapQuerySpecific.orWhere('Customer', customerIds[i])
      }

      const sapQueryAll = new SapQuery<SapGetCustomerIndexResponse>()
      sapQueryAll.where((qb) => {
        qb.where('Customer', customerIds[0])
        for (let i = 1; i < customerIds.length; i++) {
          qb.orWhere('Customer', customerIds[i])
        }
        return qb
      })
      sapQueryAll.andWhere((qb) => {
        qb.where('Customer', customerIds[0], FilterOperator.NOT_EQUAL)
        for (let i = 1; i < customerIds.length; i++) {
          qb.andWhere('Customer', customerIds[i], FilterOperator.NOT_EQUAL)
        }
        return qb
      })

      sapQueryAll.addOrderBy('CustomerName', 'asc')
      sapQueryAll.setTop(SUGGESTED_CUSTOMERS_AMOUNT - customerIds.length)

      assert.calledTwice(sapGetCustomerIndex.execute)
      assert.calledWithExactly(sapGetCustomerIndex.execute.getCall(0), sapQuerySpecific)
      assert.calledWithExactly(sapGetCustomerIndex.execute.getCall(1), sapQueryAll)
    })
  })

  describe ('Request type "pick-up"', () => {
    const query = new ViewSuggestedCustomersQueryBuilder()
      .withFilter(
        new ViewSuggestedCustomersFilterQueryBuilder()
          .withRequestType(RequestType.PICK_UP)
          .build()
      )
      .build()

    it('Retrieves first X-amount of customers when no past pick-up requests', async () => {
      const accessibleCustomerIds = [randomUUID(), randomUUID()]
      userCustomerAuthService.getAccessibleCustomerIds.resolves(accessibleCustomerIds)

      await useCase.execute(query)

      const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()

      sapQuery.where((qb) => {
        qb.where('Customer', accessibleCustomerIds[0])
        for (let i = 1; i < accessibleCustomerIds.length; i++) {
          qb.orWhere('Customer', accessibleCustomerIds[i])
        }
        return qb
      })

      sapQuery.addOrderBy('CustomerName', 'asc')
      sapQuery.setTop(SUGGESTED_CUSTOMERS_AMOUNT)

      assert.calledOnceWithExactly(sapGetCustomerIndex.execute, sapQuery)
    })

    it('Retrieves customers of past pick-up requests completed with customers alphabetically until X-amount', async () => {
      const customers: Customer[] = [{
        id: randomUUID(),
        name: randFullName(),
        address: null
      }, {
        id: randomUUID(),
        name: randFullName(),
        address: null
      }]
      const customerIds = customers.map(customer => customer.id)

      const sapResults = MapCustomerSapService.mapCustomersToResults(customers)

      userCustomerAuthService.getAccessibleCustomerIds.resolves(customerIds)
      repository.findRecentPickUpRequestCustomerIds.resolves(customerIds)
      sapGetCustomerIndex.execute.resolves(
        new SapPaginatedResponseBuilder<SapGetCustomerIndexResponse>()
          .addItem(sapResults)
          .build()
      )

      await useCase.execute(query)

      const sapQuerySpecific = new SapQuery<SapGetCustomerIndexResponse>()
        .where('Customer', customerIds[0])

      for (let i = 1; i < customerIds.length; i++) {
        sapQuerySpecific.orWhere('Customer', customerIds[i])
      }

      const sapQueryAll = new SapQuery<SapGetCustomerIndexResponse>()
      sapQueryAll.where((qb) => {
        qb.where('Customer', customerIds[0])
        for (let i = 1; i < customerIds.length; i++) {
          qb.orWhere('Customer', customerIds[i])
        }
        return qb
      })
      sapQueryAll.andWhere((qb) => {
        qb.where('Customer', customerIds[0], FilterOperator.NOT_EQUAL)
        for (let i = 1; i < customerIds.length; i++) {
          qb.andWhere('Customer', customerIds[i], FilterOperator.NOT_EQUAL)
        }
        return qb
      })

      sapQueryAll.addOrderBy('CustomerName', 'asc')
      sapQueryAll.setTop(SUGGESTED_CUSTOMERS_AMOUNT - customerIds.length)

      assert.calledTwice(sapGetCustomerIndex.execute)
      assert.calledWithExactly(sapGetCustomerIndex.execute.getCall(0), sapQuerySpecific)
      assert.calledWithExactly(sapGetCustomerIndex.execute.getCall(1), sapQueryAll)
    })
  })
})
