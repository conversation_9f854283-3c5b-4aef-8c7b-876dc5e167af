import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { ViewSuggestedCustomersFilterQuery } from '../view-suggested-customers.query.js'

export class ViewSuggestedCustomersFilterQueryBuilder {
  private filter: ViewSuggestedCustomersFilterQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.filter = new ViewSuggestedCustomersFilterQuery()

    return this
  }

  withRequestType (type: RequestType): this {
    this.filter.requestType = type

    return this
  }

  build (): ViewSuggestedCustomersFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
