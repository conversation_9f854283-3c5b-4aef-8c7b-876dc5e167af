import { ViewSuggestedCustomersFilterQuery, ViewSuggestedCustomersQuery } from '../view-suggested-customers.query.js'

export class ViewSuggestedCustomersQueryBuilder {
  private query: ViewSuggestedCustomersQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewSuggestedCustomersQuery()

    return this
  }

  withFilter (filter: ViewSuggestedCustomersFilterQuery): this {
    this.query.filter = filter

    return this
  }

  build (): ViewSuggestedCustomersQuery {
    const result = this.query

    this.reset()

    return result
  }
}
