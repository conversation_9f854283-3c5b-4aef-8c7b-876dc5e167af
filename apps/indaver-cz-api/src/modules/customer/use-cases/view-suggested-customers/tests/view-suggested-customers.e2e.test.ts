import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { RequestType } from '../../../../../utils/enums/request-type.enum.js'
import { ViewSuggestedCustomersQueryBuilder } from './view-suggested-customers.query.builder.js'
import { ViewSuggestedCustomersFilterQueryBuilder } from './view-suggested-customers.filter.builder.js'

describe('View suggested customers e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/suggested-customers`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/suggested-customers`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns customers when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_READ])

    const query = new ViewSuggestedCustomersQueryBuilder()
      .withFilter(
        new ViewSuggestedCustomersFilterQueryBuilder()
          .withRequestType(RequestType.WASTE)
          .build()
      )
      .build()

    const response = await request(setup.httpServer)
      .get(`/suggested-customers`)
      .query(query)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
