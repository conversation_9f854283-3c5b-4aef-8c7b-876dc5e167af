import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { SUGGESTED_CUSTOMERS_AMOUNT } from '../../../../utils/constants/suggested-entities-amount.constant.js'

@Injectable()
export class ViewSuggestedCustomersRepository {
  constructor (
    @InjectRepository(WasteInquiry)
    private readonly wasteInquiryRepository: Repository<WasteInquiry>,
    @InjectRepository(PickUpRequest)
    private readonly pickUpRequestRepository: Repository<PickUpRequest>
  ) {}

  async findRecentWasteInquiryCustomerIds (
    userUuid: string
  ): Promise<string[]> {
    const results = await this.wasteInquiryRepository.createQueryBuilder('wasteInquiry')
      .select('wasteInquiry.customerId', 'customerId')
      .addSelect('MAX(wasteInquiry.createdAt)', 'maxCreatedAt')
      .where('wasteInquiry.createdByUserUuid = :userUuid', { userUuid })
      .andWhere('wasteInquiry.customerId IS NOT NULL')
      .groupBy('wasteInquiry.customerId')
      .orderBy('"maxCreatedAt"', 'DESC')
      .limit(SUGGESTED_CUSTOMERS_AMOUNT)
      .getRawMany<{ customerId: string, maxCreatedAt: string }>()

    return results.map(result => result.customerId)
  }

  async findRecentPickUpRequestCustomerIds (
    userUuid: string
  ): Promise<string[]> {
    const pickUpRequests = await this.pickUpRequestRepository.createQueryBuilder('pickUpRequest')
      .select('pickUpRequest.customerId', 'customerId')
      .addSelect('MAX(pickUpRequest.createdAt)', 'maxCreatedAt')
      .where('pickUpRequest.createdByUserUuid = :userUuid', { userUuid })
      .andWhere('pickUpRequest.customerId IS NOT NULL')
      .groupBy('pickUpRequest.customerId')
      .orderBy('"maxCreatedAt"', 'DESC')
      .limit(SUGGESTED_CUSTOMERS_AMOUNT)
      .getRawMany<{ customerId: string, maxCreatedAt: string }>()

    return pickUpRequests.map(pickUpRequest => pickUpRequest.customerId)
  }
}
