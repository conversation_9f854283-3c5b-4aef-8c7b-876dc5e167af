import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { WasteInquiry } from '../../../waste-inquiry/entities/waste-inquiry.entity.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { SapModule } from '../../../sap/sap.module.js'
import { ViewSuggestedCustomersController } from './view-suggested-customers.controller.js'
import { ViewSuggestedCustomersUseCase } from './view-suggested-customers.use-case.js'
import { ViewSuggestedCustomersRepository } from './view-suggested-customers.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WasteInquiry,
      PickUpRequest
    ]),
    SapModule
  ],
  controllers: [
    ViewSuggestedCustomersController
  ],
  providers: [
    ViewSuggestedCustomersUseCase,
    ViewSuggestedCustomersRepository
  ]
})
export class ViewSuggestedCustomersModule {}
