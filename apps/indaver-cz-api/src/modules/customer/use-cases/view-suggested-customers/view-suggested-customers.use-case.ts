import { Injectable } from '@nestjs/common'
import { RequestType } from '../../../../utils/enums/request-type.enum.js'
import { Customer } from '../../types/customer.type.js'
import { MapCustomerSapService } from '../../services/map-customer-sap.service.js'
import { exhaustiveCheck } from '../../../../utils/helpers/exhaustive-check.helper.js'
import { SUGGESTED_CUSTOMERS_AMOUNT } from '../../../../utils/constants/suggested-entities-amount.constant.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { ViewSuggestedCustomersResponse } from './view-suggested-customers.response.js'
import { ViewSuggestedCustomersQuery } from './view-suggested-customers.query.js'
import { ViewSuggestedCustomersRepository } from './view-suggested-customers.repository.js'

@Injectable()
export class ViewSuggestedCustomersUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly repository: ViewSuggestedCustomersRepository,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase
  ) {}

  async execute (query: ViewSuggestedCustomersQuery): Promise<ViewSuggestedCustomersResponse> {
    const accessibleCustomerIds = await this.getAccessibleCustomerIds()
    let recentCustomerIds = await this.getRecentCustomerIds(
      query.filter.requestType
    )

    if (accessibleCustomerIds !== null) {
      recentCustomerIds = recentCustomerIds.filter((customerId) => {
        return accessibleCustomerIds.includes(customerId)
      })
    }

    let sapCustomers: SapGetCustomerIndexResponse[] = []
    if (recentCustomerIds.length > 0) {
      sapCustomers = await this.getSapCustomersByIds(recentCustomerIds)
    }

    if (sapCustomers.length < SUGGESTED_CUSTOMERS_AMOUNT) {
      const sapExtraCustomers = await this.getSapCustomers(
        SUGGESTED_CUSTOMERS_AMOUNT - sapCustomers.length,
        accessibleCustomerIds,
        recentCustomerIds
      )

      sapCustomers.push(...sapExtraCustomers)
    }

    const customers = this.mapSapCustomer(sapCustomers)

    return new ViewSuggestedCustomersResponse(customers)
  }

  private async getAccessibleCustomerIds (): Promise<string[] | null> {
    if (this.authContext.isInternalUser()) {
      return null
    }

    const userId = this.authContext.getAzureEntraUpn()
    return await this.userCustomerAuthService.getAccessibleCustomerIds(
      userId
    )
  }

  private async getRecentCustomerIds (
    requestType: RequestType
  ): Promise<string[]> {
    const userUuid = this.authContext.getUserUuidOrFail()

    switch (requestType) {
      case RequestType.WASTE:
        return this.repository.findRecentWasteInquiryCustomerIds(userUuid)
      case RequestType.PICK_UP:
        return this.repository.findRecentPickUpRequestCustomerIds(userUuid)
      default:
        exhaustiveCheck(requestType)
    }
  }

  private async getSapCustomersByIds (
    customerIds: string[]
  ): Promise<SapGetCustomerIndexResponse[]> {
    if (customerIds.length === 0) return []

    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()
      .where('Customer', customerIds[0])

    for (let i = 1; i < customerIds.length; i++) {
      sapQuery.orWhere('Customer', customerIds[i])
    }

    const sapResponse = await this.sapGetCustomerIndex.execute(sapQuery)

    return sapResponse.items
  }

  private async getSapCustomers (
    amount: number,
    accessibleCustomerIds: string[] | null,
    excludeCustomerIds?: string[]
  ): Promise<SapGetCustomerIndexResponse[]> {
    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>()

    if (accessibleCustomerIds !== null && accessibleCustomerIds.length > 0) {
      sapQuery.where((qb) => {
        qb.where('Customer', accessibleCustomerIds[0])
        for (let i = 1; i < accessibleCustomerIds.length; i++) {
          qb.orWhere('Customer', accessibleCustomerIds[i])
        }
        return qb
      })
    }

    if (excludeCustomerIds && excludeCustomerIds.length > 0) {
      sapQuery.andWhere((qb) => {
        qb.where('Customer', excludeCustomerIds[0], FilterOperator.NOT_EQUAL)
        for (let i = 1; i < excludeCustomerIds.length; i++) {
          qb.andWhere('Customer', excludeCustomerIds[i], FilterOperator.NOT_EQUAL)
        }
        return qb
      })
    }

    sapQuery.addOrderBy('CustomerName', 'asc')
    sapQuery.setTop(amount)

    const sapResponse = await this.sapGetCustomerIndex.execute(sapQuery)

    return sapResponse.items
  }

  private mapSapCustomer (customers: SapGetCustomerIndexResponse[]): Customer[] {
    return MapCustomerSapService.mapResultsToCustomers(customers)
  }
}
