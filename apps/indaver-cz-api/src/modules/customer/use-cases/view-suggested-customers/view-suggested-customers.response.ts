import { ApiProperty } from '@nestjs/swagger'
import { CustomerResponse } from '../../responses/customer.response.js'
import { Customer } from '../../types/customer.type.js'

export class ViewSuggestedCustomersResponse {
  @ApiProperty({ type: CustomerResponse, isArray: true, maximum: 3 })
  declare items: CustomerResponse[]

  constructor (items: Customer[]) {
    this.items = items.map(customer => new CustomerResponse(customer))
  }
}
