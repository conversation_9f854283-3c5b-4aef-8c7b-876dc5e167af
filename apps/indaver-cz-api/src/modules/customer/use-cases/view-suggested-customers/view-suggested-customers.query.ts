import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsNotEmpty, IsString, ValidateNested } from 'class-validator'
import { RequestType } from '../../../../utils/enums/request-type.enum.js'

export class ViewSuggestedCustomersFilterQuery {
  @ApiProperty({ type: String, enum: RequestType, enumName: 'RequestType' })
  @IsString()
  @IsNotEmpty()
  @IsEnum(RequestType)
  requestType: RequestType
}

export class ViewSuggestedCustomersQuery {
  @ApiProperty({ type: ViewSuggestedCustomersFilterQuery })
  @Type(() => ViewSuggestedCustomersFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewSuggestedCustomersFilterQuery
}
