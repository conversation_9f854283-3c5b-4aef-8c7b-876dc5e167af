import { Controller, Get, Query } from '@nestjs/common'
import { ApiTag<PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewSuggestedCustomersResponse } from './view-suggested-customers.response.js'
import { ViewSuggestedCustomersUseCase } from './view-suggested-customers.use-case.js'
import { ViewSuggestedCustomersQuery } from './view-suggested-customers.query.js'

@ApiTags('Customer')
@ApiOAuth2([])
@Controller('suggested-customers')
export class ViewSuggestedCustomersController {
  constructor (
    private readonly useCase: ViewSuggestedCustomersUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewSuggestedCustomersResponse })
  public async viewCustomerIndex (
    @Query() query: ViewSuggestedCustomersQuery
  ): Promise<ViewSuggestedCustomersResponse> {
    return await this.useCase.execute(query)
  }
}
