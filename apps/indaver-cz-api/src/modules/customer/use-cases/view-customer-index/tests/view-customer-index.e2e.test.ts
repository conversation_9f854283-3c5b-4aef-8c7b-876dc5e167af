import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('View customer index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/customers`)

    expect(response).toHaveStatus(401)
  })

  it('returns customers when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.PICK_UP_REQUEST_READ])

    const response = await request(setup.httpServer)
      .get(`/customers`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
