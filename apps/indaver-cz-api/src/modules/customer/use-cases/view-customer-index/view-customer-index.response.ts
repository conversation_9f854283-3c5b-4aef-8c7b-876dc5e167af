import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { Customer } from '../../types/customer.type.js'
import { CustomerResponse } from '../../responses/customer.response.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class ViewCustomerIndexResponse extends PaginatedOffsetResponse<CustomerResponse> {
  @ApiProperty({ type: CustomerResponse, isArray: true })
  declare items: CustomerResponse[]

  constructor (items: Customer[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new CustomerResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
