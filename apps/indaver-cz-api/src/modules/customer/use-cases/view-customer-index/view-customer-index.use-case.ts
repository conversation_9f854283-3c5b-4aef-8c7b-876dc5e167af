import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { MapCustomerSapService } from '../../services/map-customer-sap.service.js'
import { SapGetCustomerIndexUseCase } from '../../../sap/use-cases/get-customer-index/get-customer-index.use-case.js'
import { SapGetCustomerIndexResponse } from '../../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { UserCustomerAuthService } from '../../../auth/services/user-customer-auth.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { ViewCustomerIndexResponse } from './view-customer-index.response.js'
import { ViewCustomerIndexQuery } from './query/view-customer-index.query.js'

@Injectable()
export class ViewCustomerIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userCustomerAuthService: UserCustomerAuthService,
    private readonly sapGetCustomerIndex: SapGetCustomerIndexUseCase
  ) {}

  public async execute (
    query: ViewCustomerIndexQuery
  ): Promise<ViewCustomerIndexResponse> {
    const pagination = typeormPagination(query.pagination)
    const accessibleCustomerIds = await this.getAccessibleCustomerIds()

    if (accessibleCustomerIds?.length === 0) {
      return new ViewCustomerIndexResponse([], null, pagination.take, pagination.skip)
    }

    const sapQuery = this.getSapQuery(query, accessibleCustomerIds)
    const sapResponse = await this.sapGetCustomerIndex.execute(sapQuery)

    const customers = MapCustomerSapService.mapResultsToCustomers(sapResponse.items)

    return new ViewCustomerIndexResponse(
      customers,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getAccessibleCustomerIds (): Promise<Promise<string[]> | undefined> {
    if (this.authContext.isInternalUser()) {
      return undefined
    }

    const userId = this.authContext.getAzureEntraUpn()
    return this.userCustomerAuthService.getAccessibleCustomerIds(userId)
  }

  private getSapQuery (
    query: ViewCustomerIndexQuery,
    accessibleCustomerIds?: string[]
  ): SapQuery<SapGetCustomerIndexResponse> {
    const sapQuery = new SapQuery<SapGetCustomerIndexResponse>(query)
      .addOrderBy('CustomerName', 'asc')

    if (accessibleCustomerIds !== undefined && accessibleCustomerIds.length > 0) {
      sapQuery.where((qb) => {
        qb.where('Customer', accessibleCustomerIds[0])
        for (let i = 1; i < accessibleCustomerIds.length; i++) {
          qb.orWhere('Customer', accessibleCustomerIds[i])
        }
        return qb
      })
    }

    return sapQuery
  }
}
