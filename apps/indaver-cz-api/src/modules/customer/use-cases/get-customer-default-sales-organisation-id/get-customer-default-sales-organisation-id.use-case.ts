import { Injectable } from '@nestjs/common'
import { SapGetCustomerSalesOrganizationIndexUseCase } from '../../../sap/use-cases/get-customer-sales-organization-index/get-customer-sales-organization-index.use-case.js'
import { SapGetCustomerSalesOrganizationIndexResponse } from '../../../sap/use-cases/get-customer-sales-organization-index/get-customer-sales-organization-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'

@Injectable()
export class GetCustomerDefaultSalesOrganisationIdUseCase {
  constructor (
    private readonly sapUseCase: SapGetCustomerSalesOrganizationIndexUseCase
  ) {}

  async getOrganisationId (customerId: string): Promise<string | null> {
    return await this.getOrganisationIdByCustomerId(customerId)
  }

  async getOrganisationIdOrFail (customerId: string): Promise<string> {
    const organisationId = await this.getOrganisationIdByCustomerId(customerId)

    if (organisationId === null) {
      throw new Error(`No sales organisation found for customer ID: ${customerId}`)
    }

    return organisationId
  }

  private async getOrganisationIdByCustomerId (
    customerId: string
  ): Promise<string | null> {
    const sapQuery = new SapQuery<SapGetCustomerSalesOrganizationIndexResponse>()
      .where('Customer', customerId)

    const sapResult = await this.sapUseCase.execute(sapQuery)

    if (sapResult.items.length === 0) {
      return null
    }

    return sapResult.items[0].SalesOrganization
  }
}
