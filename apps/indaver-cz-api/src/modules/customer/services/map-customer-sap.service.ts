import { SapGetCustomerIndexResponse } from '../../sap/use-cases/get-customer-index/get-customer-index.response.js'
import { Customer } from '../types/customer.type.js'

export class MapCustomerSapService {
  static mapResultToCustomer (response: SapGetCustomerIndexResponse): Customer {
    return {
      id: response.Customer,
      name: response.CustomerName,
      address: {
        countryCode: null,
        postalCode: response.CustomerPostalCode,
        locality: response.CustomerCityName,
        addressLine1: response.CustomerStreetname,
        addressLine2: null,
        coordinates: null
      }
    }
  }

  static mapResultsToCustomers (responses: SapGetCustomerIndexResponse[] = []): Customer[] {
    return responses.map(response => this.mapResultToCustomer(response))
  }

  static mapCustomerToResult (customer: Customer): SapGetCustomerIndexResponse {
    return {
      Customer: customer.id,
      CustomerFirstName: '',
      CustomerLastName: '',
      CustomerStreetname: customer.address?.addressLine1 ?? '',
      CustomerCityName: customer.address?.locality ?? '',
      CustomerPostalCode: customer.address?.postalCode ?? '',
      CustomerName: customer.name,
      CustomerAddress: ''
    }
  }

  static mapCustomersToResults (customers: Customer[]): SapGetCustomerIndexResponse[] {
    return customers.map(customer => this.mapCustomerToResult(customer))
  }
}
