import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger'
import { AddressResponse } from '../../../utils/address/address.response.js'
import { Customer } from '../types/customer.type.js'
import { SapCustomer } from '../types/sap-customer.type.js'

@ApiExtraModels(
  AddressResponse
)
export class CustomerResponse {
  @ApiProperty({ type: String })
  id: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({
    oneOf: [
      { type: 'string' },
      { type: getSchemaPath(AddressResponse) }
    ], nullable: true
  })
  address: AddressResponse | string | null

  constructor (customer: Customer | SapCustomer) {
    this.id = customer.id
    this.name = customer.name
    this.address = customer.address != null
      ? (typeof customer.address === 'string'
          ? customer.address
          : new AddressResponse(customer.address)
        )
      : null
  }
}
