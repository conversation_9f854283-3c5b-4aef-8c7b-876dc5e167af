import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class CustomerNotFoundError extends NotFoundApiError {
  @ApiErrorCode('customer_not_found')
  code: 'customer_not_found'

  meta: never

  constructor (customerId: string) {
    super(translateCurrent('error.customer.customer_not_found', { args: { customerId } }))
    this.code = 'customer_not_found'
  }
}
