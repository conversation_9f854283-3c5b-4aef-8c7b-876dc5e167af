import { Injectable } from '@nestjs/common'
import { InjectEntityManager } from '@wisemen/nestjs-typeorm'
import { Repository, EntityManager } from 'typeorm'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../../../../pick-up-request/enums/pick-up-transport-mode.enum.js'

export interface PickUpRequestContractLineOptions {
  bulkTransports: PickUpTransportMode[]
  contractLineIds: string[]
  customerId: string
  wasteProducerId?: string
  pickUpAddressIds?: string[]
  skip: number
  take: number
}

@Injectable()
export class ViewWprContractLineRepository extends Repository<PickUpRequest> {
  constructor (@InjectEntityManager() entityManager: EntityManager) {
    super(PickUpRequest, entityManager)
  }

  async findPickUpRequestContractLines (
    options: PickUpRequestContractLineOptions
  ): Promise<[PickUpRequest[], number]> {
    const whereConditions: string[] = []

    const params: (PickUpTransportMode[] | number | string | string[])[] = [
      options.bulkTransports,
      options.contractLineIds,
      options.customerId
    ]

    if (options.wasteProducerId !== undefined) {
      whereConditions.push(`waste_producer_id = $${params.length + 1}`)
      params.push(options.wasteProducerId)
    }

    if (options.pickUpAddressIds !== undefined) {
      whereConditions.push(`pick_up_address_ids @> $${params.length + 1}`)
      params.push(options.pickUpAddressIds)
    }

    const subquery = `SELECT DISTINCT ON (materials->0->>'contractLineId') *
      FROM pick_up_request
      WHERE jsonb_array_length(materials) = 1
      AND transport_mode = ANY($1)
      AND (materials->0->>'contractLineId' = ANY($2))
      AND submitted_on IS NOT NULL
      AND customer_id = $3
      ${whereConditions.length > 0 ? 'AND ' + whereConditions.join(' AND ') : ''}
      ORDER BY materials->0->>'contractLineId', submitted_on DESC, start_date DESC`

    const pickUpRequest = await this.manager.query<PickUpRequest[]>(
      `
      SELECT * FROM (${subquery}) AS distinct_results
      LIMIT ${options.take} OFFSET ${options.skip}
      `,
      params
    )

    const count = await this.manager.query<{ count: string }[]>(
      `SELECT COUNT(*) FROM (${subquery}) AS count_results`,
      params
    )

    return [pickUpRequest, parseInt(count[0].count, 10)]
  }
}
