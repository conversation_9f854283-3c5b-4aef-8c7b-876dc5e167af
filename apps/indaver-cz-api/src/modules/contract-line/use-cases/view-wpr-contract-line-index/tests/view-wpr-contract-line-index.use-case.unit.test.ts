import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ContractLine } from '../../../types/contract-line.type.js'
import { SapGetContractLineIndexUseCase } from '../../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { ViewWprContractLineIndexUseCase } from '../view-wpr-contract-line-index.use-case.js'
import { PickUpTransportMode } from '../../../../pick-up-request/enums/pick-up-transport-mode.enum.js'
import { ValidPickUpRequestEntityBuilder } from '../../../../pick-up-request/tests/valid-pick-up-request-entity.builder.js'
import { ValidPickUpRequestMaterialBuilder } from '../../../../pick-up-request/tests/valid-pick-up-request-material.builder.js'
import { PickUpRequestContractLineMapper } from '../mappers/pick-up-request-contract-line.mapper.js'
import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { ViewWprContractLineRepository } from '../repositories/view-wpr-contract-line-index.repository.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewWprContractLineIndexValidator } from '../view-wpr-contract-line-index.validator.js'
import { GetContractLineIndexResponseBuilder } from '../../../../sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'
import { MapContractLineSapService } from '../../../services/map-contract-line.service.js'
import { ViewWprContractLineIndexQueryBuilder } from './view-wpr-contract-line-index.query.builder.js'
import { ViewWprContractLineIndexFilterQueryBuilder } from './view-wpr-contract-line-index.filter.builder.js'

describe('View weekly planning contract lines index use-case unit test', () => {
  let useCase: ViewWprContractLineIndexUseCase

  let userUuid: string

  let pickUpRequest: PickUpRequest
  let contractLine: ContractLine

  let sapGetContractLineIndex: SinonStubbedInstance<SapGetContractLineIndexUseCase>
  let validator: SinonStubbedInstance<ViewWprContractLineIndexValidator>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let pickUpRequestRepository: SinonStubbedInstance<ViewWprContractLineRepository>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    sapGetContractLineIndex = createStubInstance(SapGetContractLineIndexUseCase)
    validator = createStubInstance(ViewWprContractLineIndexValidator)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    pickUpRequestRepository = createStubInstance(ViewWprContractLineRepository)

    const sapContractLine = new GetContractLineIndexResponseBuilder().build()
    contractLine = MapContractLineSapService.mapResultToContractLine(sapContractLine)

    sapGetContractLineIndex.execute.resolves([
      sapContractLine
    ])
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())

    pickUpRequest = new ValidPickUpRequestEntityBuilder()
      .createdByUserUuid(userUuid)
      .addMaterial(
        new ValidPickUpRequestMaterialBuilder().build(),
        true
      )
      .withSubmittedOn(new Date())
      .withTransportMode(PickUpTransportMode.BULK_ISO_TANK)
      .build()

    pickUpRequestRepository.findPickUpRequestContractLines.resolves([
      [pickUpRequest], 1
    ])

    useCase = new ViewWprContractLineIndexUseCase(
      validator,
      salesOrganisationUseCase,
      pickUpRequestRepository,
      sapGetContractLineIndex
    )
  })

  it('Calls all methods once', async () => {
    const query = new ViewWprContractLineIndexQueryBuilder()
      .withFilter(new ViewWprContractLineIndexFilterQueryBuilder().build())
      .build()

    await useCase.execute(query)

    assert.calledOnce(validator.validate)
    assert.calledOnce(salesOrganisationUseCase.getOrganisationIdOrFail)
    assert.calledOnce(sapGetContractLineIndex.execute)
    assert.calledOnce(pickUpRequestRepository.findPickUpRequestContractLines)
  })

  it('Returns pick up requests from contract lines', async () => {
    const query = new ViewWprContractLineIndexQueryBuilder()
      .withFilter(new ViewWprContractLineIndexFilterQueryBuilder().build())
      .build()

    const result = await useCase.execute(query)

    const mergedItems = PickUpRequestContractLineMapper.mapPickUpRequestContractLines(
      [pickUpRequest],
      [contractLine]
    )

    expect(result.items).toStrictEqual(expect.arrayContaining(mergedItems))
  })
})
