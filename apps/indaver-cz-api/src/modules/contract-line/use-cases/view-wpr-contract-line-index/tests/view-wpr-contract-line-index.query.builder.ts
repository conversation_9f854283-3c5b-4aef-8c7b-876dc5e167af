import { ViewWprContractLineIndexFilterQuery } from '../view-wpr-contract-line-index.filter.query.js'
import { ViewWprContractLineIndexQuery } from '../view-wpr-contract-line-index.query.js'

export class ViewWprContractLineIndexQueryBuilder {
  private query: ViewWprContractLineIndexQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewWprContractLineIndexQuery()

    return this
  }

  withFilter (filter: ViewWprContractLineIndexFilterQuery): this {
    this.query.filter = filter

    return this
  }

  build (): ViewWprContractLineIndexQuery {
    const result = this.query

    this.reset()

    return result
  }
}
