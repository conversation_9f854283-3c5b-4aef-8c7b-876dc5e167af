import { randomUUID } from 'crypto'
import { ViewWprContractLineIndexFilterQuery } from '../view-wpr-contract-line-index.filter.query.js'

export class ViewWprContractLineIndexFilterQueryBuilder {
  private filter: ViewWprContractLineIndexFilterQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.filter = new ViewWprContractLineIndexFilterQuery()

    this.filter.customerId = randomUUID()

    return this
  }

  withCustomerId (customerId: string): this {
    this.filter.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string): this {
    this.filter.wasteProducerId = wasteProducerId

    return this
  }

  withPickUpAddressIds (pickUpAddressIds: string[]): this {
    this.filter.pickUpAddressIds = pickUpAddressIds

    return this
  }

  build (): ViewWprContractLineIndexFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
