import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { PickUpRequestContractLine } from '../../types/pick-up-request-contract-line.type.js'
import { ContractLineDynamicTableFields } from '../../types/contract-line.dynamic-table-fields.type.js'

export class WprContractLineResponse implements ContractLineDynamicTableFields {
  @ApiProperty({ type: String })
  pickUpRequestUuid: string

  @ApiProperty({ type: String })
  contractLineId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: String })
  contractItem: string

  @ApiProperty({ type: String, nullable: true })
  customerReference: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: String, nullable: true })
  materialNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  treatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  installationName: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  @ApiProperty({ type: String, nullable: true })
  asn: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  tfs: boolean | null

  @ApiProperty({ type: Boolean, nullable: true })
  isHazardous: boolean | null

  @ApiProperty({ type: String, nullable: true })
  packaged: string | null

  @ApiProperty({ type: String, nullable: true })
  tcNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  materialAnalysis: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcCode: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterId: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCenterName: string | null

  @ApiProperty({ type: String, nullable: true })
  remarks: string | null

  @ApiProperty({ type: String, nullable: true })
  processCode: string | null

  @ApiProperty({ type: String, nullable: true })
  esnNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  deliveryInfo: string | null

  constructor (contractLine: PickUpRequestContractLine) {
    this.pickUpRequestUuid = contractLine.pickUpRequestUuid
    this.contractLineId = contractLine.contractLineId
    this.contractNumber = contractLine.contractNumber
    this.contractItem = contractLine.contractItem
    this.customerReference = contractLine.customerReference
    this.wasteMaterial = contractLine.wasteMaterial
    this.materialNumber = contractLine.materialNumber
    this.treatmentCenterName = contractLine.treatmentCenterName
    this.installationName = contractLine.installationName
    this.customerId = contractLine.customerId
    this.customerName = contractLine.customerName
    this.wasteProducerId = contractLine.wasteProducerId
    this.wasteProducerName = contractLine.wasteProducerName
    this.pickUpAddressId = contractLine.pickUpAddressId
    this.pickUpAddressName = contractLine.pickUpAddressName
    this.asn = contractLine.asn
    this.tfs = contractLine.tfs
    this.isHazardous = contractLine.isHazardous
    this.packaged = contractLine.packaged
    this.tcNumber = contractLine.tcNumber
    this.materialAnalysis = contractLine.materialAnalysis
    this.ewcCode = contractLine.ewcCode
    this.endTreatmentCenterId = contractLine.endTreatmentCenterId
    this.endTreatmentCenterName = contractLine.endTreatmentCenterName
    this.remarks = contractLine.remarks
    this.processCode = contractLine.processCode
    this.esnNumber = contractLine.esnNumber
    this.deliveryInfo = contractLine.deliveryInfo
  }
}

export class ViewWprContractLineIndexResponse
  extends PaginatedOffsetResponse<WprContractLineResponse> {
  @ApiProperty({ type: WprContractLineResponse, isArray: true })
  declare items: WprContractLineResponse[]

  constructor (items: PickUpRequestContractLine[], total: number, limit: number, offset: number) {
    const result = items.map(item => new WprContractLineResponse(item))

    super(result, total, limit, offset)
  }
}
