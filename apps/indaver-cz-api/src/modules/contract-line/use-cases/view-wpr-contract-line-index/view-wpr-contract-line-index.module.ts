import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { SapModule } from '../../../sap/sap.module.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { PermissionsGuardModule } from '../../../permission/guards/permission.guard.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewWprContractLineIndexController } from './view-wpr-contract-line-index.controller.js'
import { ViewWprContractLineIndexUseCase } from './view-wpr-contract-line-index.use-case.js'
import { ViewWprContractLineRepository } from './repositories/view-wpr-contract-line-index.repository.js'
import { ViewWprContractLineIndexValidator } from './view-wpr-contract-line-index.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([PickUpRequest]),
    SapModule,
    PermissionsGuardModule,
    CustomerModule
  ],
  controllers: [
    ViewWprContractLineIndexController
  ],
  providers: [
    ViewWprContractLineIndexUseCase,
    ViewWprContractLineIndexValidator,
    ViewWprContractLineRepository
  ]
})
export class ViewWprContractLineIndexModule {}
