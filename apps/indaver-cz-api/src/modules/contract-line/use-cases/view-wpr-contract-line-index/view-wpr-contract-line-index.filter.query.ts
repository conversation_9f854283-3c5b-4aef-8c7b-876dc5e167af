import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { IsString, IsNotEmpty, IsOptional, IsArray } from 'class-validator'

export class ViewWprContractLineIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  customerId: string

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  wasteProducerId?: string

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  pickUpAddressIds?: string[]
}
