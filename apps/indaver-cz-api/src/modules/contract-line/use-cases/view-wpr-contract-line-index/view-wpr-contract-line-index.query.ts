import { Equals, IsNotEmpty, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ViewWprContractLineIndexFilterQuery } from './view-wpr-contract-line-index.filter.query.js'

export class ViewWprContractLineIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewWprContractLineIndexFilterQuery })
  @Type(() => ViewWprContractLineIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewWprContractLineIndexFilterQuery

  @Equals(undefined)
  search?: never
}
