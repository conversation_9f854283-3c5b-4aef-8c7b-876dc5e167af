import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { SapGetContractLineIndexResponse } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.response.js'
import { SapGetContractLineIndexUseCase } from '../../../sap/use-cases/get-contract-line-index/get-contract-line-index.use-case.js'
import { MapContractLineSapService } from '../../services/map-contract-line.service.js'
import { PickUpRequest } from '../../../pick-up-request/entities/pick-up-request.entity.js'
import { PickUpTransportMode } from '../../../pick-up-request/enums/pick-up-transport-mode.enum.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewWprContractLineIndexQuery } from './view-wpr-contract-line-index.query.js'
import { ViewWprContractLineIndexResponse } from './view-wpr-contract-line-index.response.js'
import { PickUpRequestContractLineMapper } from './mappers/pick-up-request-contract-line.mapper.js'
import { ViewWprContractLineRepository } from './repositories/view-wpr-contract-line-index.repository.js'
import { ViewWprContractLineIndexValidator } from './view-wpr-contract-line-index.validator.js'

@Injectable()
export class ViewWprContractLineIndexUseCase {
  constructor (
    private readonly validator: ViewWprContractLineIndexValidator,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly pickUpRequestRepository: ViewWprContractLineRepository,
    private readonly sapGetContractLineIndex: SapGetContractLineIndexUseCase
  ) {}

  public async execute (
    query: ViewWprContractLineIndexQuery
  ): Promise<ViewWprContractLineIndexResponse> {
    await this.validator.validate(query)

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )

    const pagination = typeormPagination(query.pagination)

    const sapQuery = this.getSapQuery(query, customerDefaultSalesOrganisationId)
    const sapResult = await this.sapGetContractLineIndex.execute(sapQuery)

    const contractLines = MapContractLineSapService.mapResultsToContractLines(sapResult)

    const contractLineIds = contractLines.map(contractLine => contractLine.contractLineId)

    const [pickUpRequests, count] = await this.findPickUpRequests(
      contractLineIds,
      query
    )

    const pickUpRequestContractLines = PickUpRequestContractLineMapper
      .mapPickUpRequestContractLines(
        pickUpRequests,
        contractLines
      )

    return new ViewWprContractLineIndexResponse(
      pickUpRequestContractLines,
      count,
      pagination.take,
      pagination.skip
    )
  }

  private getSapQuery (
    query: ViewWprContractLineIndexQuery,
    customerDefaultSalesOrganisationId: string
  ): SapQuery<SapGetContractLineIndexResponse> {
    const sapQuery = new SapQuery<SapGetContractLineIndexResponse>(query)
      .where('MaterialType', 'YWAS')
      .andWhere('Kunnr', query.filter.customerId)
      .andWhere('Vkorg', customerDefaultSalesOrganisationId)

    if (query.filter.wasteProducerId !== undefined) {
      sapQuery.andWhere('KunnrY2', query.filter.wasteProducerId)
    }
    if (query.filter.pickUpAddressIds !== undefined && query.filter.pickUpAddressIds.length > 0) {
      const pickUpAddressIds = query.filter.pickUpAddressIds
      sapQuery.andWhere((qb) => {
        for (let i = 0; i < pickUpAddressIds.length; i++) {
          if (i === 0) {
            qb.where('KunnrWe', pickUpAddressIds[i])
          } else {
            qb.orWhere('KunnrWe', pickUpAddressIds[i])
          }
        }
        return qb
      })
    }

    // Overwrite top and skip from pagination, as we need all contract lines to map against
    sapQuery.setTop(1000)
      .setSkip(0)

    return sapQuery
  }

  private async findPickUpRequests (
    contractLineIds: string[],
    query: ViewWprContractLineIndexQuery
  ): Promise<[PickUpRequest[], number]> {
    const pagination = typeormPagination(query.pagination)

    const bulkTransports = [
      PickUpTransportMode.BULK_SKIPS_CONTAINER,
      PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS,
      PickUpTransportMode.BULK_ISO_TANK
    ]

    return await this.pickUpRequestRepository.findPickUpRequestContractLines({
      bulkTransports,
      contractLineIds,
      customerId: query.filter.customerId,
      wasteProducerId: query.filter.wasteProducerId,
      pickUpAddressIds: query.filter.pickUpAddressIds,
      take: pagination.take,
      skip: pagination.skip
    })
  }
}
