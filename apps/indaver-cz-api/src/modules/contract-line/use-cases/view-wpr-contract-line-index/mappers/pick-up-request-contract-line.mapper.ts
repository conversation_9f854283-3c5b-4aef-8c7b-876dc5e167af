import { PickUpRequest } from '../../../../pick-up-request/entities/pick-up-request.entity.js'
import { ContractLine } from '../../../types/contract-line.type.js'
import { PickUpRequestContractLine } from '../../../types/pick-up-request-contract-line.type.js'

export class PickUpRequestContractLineMapper {
  static mapPickUpRequestContractLines (
    pickUpRequests: PickUpRequest[],
    contractLines: ContractLine[]
  ): PickUpRequestContractLine[] {
    const items: PickUpRequestContractLine[] = []

    pickUpRequests.forEach((pickUpRequest) => {
      pickUpRequest.materials.forEach((material) => {
        const contractLine = contractLines.find((contractLine) => {
          return contractLine.contractLineId === material.contractLineId
        })

        if (contractLine !== undefined) {
          const item: PickUpRequestContractLine = {
            pickUpRequestUuid: pickUpRequest.uuid,
            ...contractLine
          }

          items.push(item)
        }
      })
    })

    return items
  }
}
