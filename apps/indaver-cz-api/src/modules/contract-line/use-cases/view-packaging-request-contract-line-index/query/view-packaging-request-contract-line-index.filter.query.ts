import { ApiProperty } from '@nestjs/swagger'
import { FilterQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { IsString, IsNotEmpty, IsOptional, IsArray, ArrayUnique, IsBooleanString } from 'class-validator'

export class ViewPackagingRequestContractLineIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  customerId: string

  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  wasteProducerId: string

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsString({ each: true })
  deliveryAddressIds?: string[]

  @ApiProperty({ type: Boolean, required: false })
  @IsBooleanString()
  @IsUndefinable()
  isSales?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteMaterial?: string
}
