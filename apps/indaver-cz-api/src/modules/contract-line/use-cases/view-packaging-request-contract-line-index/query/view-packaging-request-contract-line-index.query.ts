import { Equals, IsNotEmpty, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ViewPackagingRequestContractLineIndexFilterQuery } from './view-packaging-request-contract-line-index.filter.query.js'

export class ViewPackagingRequestContractLineIndexQuery extends PaginatedOffsetSearchQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewPackagingRequestContractLineIndexFilterQuery })
  @Type(() => ViewPackagingRequestContractLineIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  filter: ViewPackagingRequestContractLineIndexFilterQuery

  @Equals(undefined)
  search?: never
}
