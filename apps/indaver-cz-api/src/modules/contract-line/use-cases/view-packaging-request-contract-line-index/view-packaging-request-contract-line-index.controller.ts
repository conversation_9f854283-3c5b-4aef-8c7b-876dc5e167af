import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewPackagingRequestContractLineIndexUseCase } from './view-packaging-request-contract-line-index.use-case.js'
import { ViewPackagingRequestContractLineIndexResponse } from './view-packaging-request-contract-line-index.response.js'
import { ViewPackagingRequestContractLineIndexQuery } from './query/view-packaging-request-contract-line-index.query.js'

@ApiTags('Contract line')
@ApiOAuth2([])
@Controller('contract-lines/packaging-requests')
export class ViewPackagingRequestContractLineIndexController {
  constructor (
    private readonly useCase: ViewPackagingRequestContractLineIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.PACKAGING_REQUEST_READ,
    Permission.PACKAGING_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE
  )
  @ApiOkResponse({ type: ViewPackagingRequestContractLineIndexResponse })
  public async viewPackagingRequestContractLineIndex (
    @Query() query: ViewPackagingRequestContractLineIndexQuery
  ): Promise<ViewPackagingRequestContractLineIndexResponse> {
    return await this.useCase.execute(query)
  }
}
