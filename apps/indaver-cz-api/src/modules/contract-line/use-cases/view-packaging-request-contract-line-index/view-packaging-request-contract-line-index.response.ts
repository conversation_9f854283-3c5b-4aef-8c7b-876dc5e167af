import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { ContractLine } from '../../types/contract-line.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'

export class PackagingRequestContractLineResponse {
  @ApiProperty({ type: String })
  contractLineId: string

  @ApiProperty({ type: String })
  contractNumber: string

  @ApiProperty({ type: String })
  contractItem: string

  @ApiProperty({ type: String, nullable: true })
  materialNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteMaterial: string | null

  @ApiProperty({ type: Boolean, nullable: true })
  isSales: boolean | null

  @ApiProperty({ type: String, nullable: true })
  imageUrl: string | null

  constructor (contractLine: ContractLine) {
    this.contractLineId = contractLine.contractLineId
    this.contractNumber = contractLine.contractNumber
    this.contractItem = contractLine.contractItem
    this.wasteMaterial = contractLine.wasteMaterial
    this.materialNumber = contractLine.materialNumber
    this.isSales = contractLine.isSales
    this.imageUrl = contractLine.imageUrl !== undefined && contractLine.imageUrl !== null
      ? contractLine.imageUrl
      : null
  }
}

export class ViewPackagingRequestContractLineIndexResponse extends PaginatedOffsetResponse<
  PackagingRequestContractLineResponse
> {
  @ApiProperty({ type: PackagingRequestContractLineResponse, isArray: true })
  declare items: PackagingRequestContractLineResponse[]

  constructor (items: ContractLine[], total: number | null, limit: number, offset: number) {
    const result = items.map(contact => new PackagingRequestContractLineResponse(contact))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(result, total, limit, offset)
  }
}
