import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import qs from 'qs'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { ViewPackagingRequestContractLineIndexQueryBuilder } from './view-packaging-request-contract-line-index.query.builder.js'
import { ViewPackagingRequestContractLineIndexFilterQueryBuilder } from './view-packaging-request-contract-line-index.filter.builder.js'

describe('View packaging request contract lines index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/contract-lines/packaging-requests`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/contract-lines/packaging-requests`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized and query with filters given', async () => {
    const user = await setup.authContext.getUser([Permission.PACKAGING_REQUEST_READ])

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    const query = new ViewPackagingRequestContractLineIndexQueryBuilder()
      .withFilter(
        new ViewPackagingRequestContractLineIndexFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withWasteProducerId(randomUUID())
          .build()
      )
      .build()

    const response = await request(setup.httpServer)
      .get(`/contract-lines/packaging-requests`)
      .set('Authorization', `Bearer ${user.token}`)
      .query(qs.stringify(query))

    expect(response).toHaveStatus(200)
  })
})
