import { randomUUID } from 'crypto'
import { ViewPackagingRequestContractLineIndexFilterQuery } from '../query/view-packaging-request-contract-line-index.filter.query.js'

export class ViewPackagingRequestContractLineIndexFilterQueryBuilder {
  private filter: ViewPackagingRequestContractLineIndexFilterQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.filter = new ViewPackagingRequestContractLineIndexFilterQuery()

    this.filter.customerId = randomUUID()
    this.filter.wasteProducerId = randomUUID()
    this.filter.deliveryAddressIds = undefined

    return this
  }

  withCustomerId (customerId: string): this {
    this.filter.customerId = customerId

    return this
  }

  withWasteProducerId (wasteProducerId: string): this {
    this.filter.wasteProducerId = wasteProducerId

    return this
  }

  withDeliveryAddressIds (deliveryAddressIds: string[]): this {
    this.filter.deliveryAddressIds = deliveryAddressIds

    return this
  }

  build (): ViewPackagingRequestContractLineIndexFilterQuery {
    const result = this.filter

    this.reset()

    return result
  }
}
