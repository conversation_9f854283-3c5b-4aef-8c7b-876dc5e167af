import { ViewPackagingRequestContractLineIndexFilterQuery } from '../query/view-packaging-request-contract-line-index.filter.query.js'
import { ViewPackagingRequestContractLineIndexQuery } from '../query/view-packaging-request-contract-line-index.query.js'

export class ViewPackagingRequestContractLineIndexQueryBuilder {
  private query: ViewPackagingRequestContractLineIndexQuery

  constructor () {
    this.reset()
  }

  reset (): this {
    this.query = new ViewPackagingRequestContractLineIndexQuery()

    return this
  }

  withFilter (filter: ViewPackagingRequestContractLineIndexFilterQuery): this {
    this.query.filter = filter

    return this
  }

  build (): ViewPackagingRequestContractLineIndexQuery {
    const result = this.query

    this.reset()

    return result
  }
}
