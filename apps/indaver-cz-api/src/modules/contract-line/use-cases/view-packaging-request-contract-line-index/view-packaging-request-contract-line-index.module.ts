import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewPackagingRequestContractLineIndexController } from './view-packaging-request-contract-line-index.controller.js'
import { ViewPackagingRequestContractLineIndexUseCase } from './view-packaging-request-contract-line-index.use-case.js'
import { ViewPackagingRequestContractLineIndexValidator } from './view-packaging-request-contract-line-index.validator.js'

@Module({
  imports: [
    SapModule,
    CustomerModule
  ],
  controllers: [
    ViewPackagingRequestContractLineIndexController
  ],
  providers: [
    ViewPackagingRequestContractLineIndexUseCase,
    ViewPackagingRequestContractLineIndexValidator
  ]
})
export class ViewPackagingRequestContractLineIndexModule {}
