import { ApiProperty } from '@nestjs/swagger'
import { SapGenerateContractLinesPdfResponse } from '../../../sap/use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.response.js'

export class GenerateContractLinesPdfResponse {
  @ApiProperty({ type: Number, description: 'Number of pages in the PDF' })
  pageCount: number

  @ApiProperty({ type: Number, description: 'File size in bytes' })
  size: number

  @ApiProperty({ type: String, example: 'file-name.pdf', description: 'File name' })
  name: string

  @ApiProperty({ type: String, example: 'application/pdf', description: 'File mime type' })
  mimeType: 'application/pdf'

  @ApiProperty({ type: String, format: 'base64', description: 'Base64 encoded PDF content' })
  content: string

  constructor (sapPdfResponse: SapGenerateContractLinesPdfResponse) {
    this.pageCount = sapPdfResponse.pdf.Pages
    this.size = sapPdfResponse.pdf.Size
    this.name = sapPdfResponse.pdf.Name
    this.mimeType = sapPdfResponse.pdf.Mimetype
    this.content = sapPdfResponse.pdf.Source
  }
}
