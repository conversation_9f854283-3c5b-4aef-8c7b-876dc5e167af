import { Modu<PERSON> } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ContractLineValidatorService } from '../../validators/contract-line-validator.service.js'
import { ViewContractLineIndexModule } from '../view-contract-line-index/view-contract-line-index.module.js'
import { GenerateContractLinesPdfUseCase } from './generate-contract-lines-pdf.use-case.js'
import { GenerateContractLinesPdfValidator } from './generate-contract-lines-pdf.validator.js'
import { GenerateContractLinesPdfController } from './generate-contract-lines-pdf.controller.js'

@Module({
  imports: [
    SapModule,
    ViewContractLineIndexModule
  ],
  controllers: [
    GenerateContractLinesPdfController
  ],
  providers: [
    GenerateContractLinesPdfUseCase,
    GenerateContractLinesPdfValidator,
    ContractLineValidatorService
  ]
})
export class GenerateContractLinesPdfModule {}
