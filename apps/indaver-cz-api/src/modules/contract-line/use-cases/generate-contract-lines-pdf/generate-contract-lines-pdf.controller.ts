import { Body, Controller, HttpCode, Post } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiBadRequestErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ContractLineNotAccessibleError } from '../../errors/contract-line-not-accessible.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { GenerateContractLinesPdfCommand } from './generate-contract-lines-pdf.command.js'
import { GenerateContractLinesPdfUseCase } from './generate-contract-lines-pdf.use-case.js'
import { GenerateContractLinesPdfResponse } from './generate-contract-lines-pdf.response.js'

@ApiTags('Contract line')
@ApiOAuth2([])
@Controller('contract-lines/generate-pdf')
export class GenerateContractLinesPdfController {
  constructor (
    private readonly useCase: GenerateContractLinesPdfUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.CONTRACT_LINE_READ)
  @ApiOkResponse({ type: GenerateContractLinesPdfResponse })
  @HttpCode(200)
  @ApiBadRequestErrorResponse(ContractLineNotAccessibleError)
  public async approveDraftInvoice (
    @Body() command: GenerateContractLinesPdfCommand
  ): Promise<GenerateContractLinesPdfResponse> {
    return await this.useCase.execute(command)
  }
}
