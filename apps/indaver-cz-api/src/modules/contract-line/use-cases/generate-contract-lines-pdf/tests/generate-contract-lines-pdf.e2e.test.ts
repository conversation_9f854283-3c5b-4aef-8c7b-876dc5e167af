import { after, before, describe, it, mock } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { GetContractLineIndexResponseBuilder } from '../../../../sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'
import { SapGetContractLineBatchUseCase } from '../../../../sap/use-cases/get-contract-line-batch/get-contract-line-batch.use-case.js'
import { GenerateContractLinesPdfSelectionCommandBuilder } from './generate-contract-lines-pdf-selection.command.builder.js'
import { GenerateContractLinesPdfQuerySelectionCommandBuilder } from './generate-contract-lines-pdf-query-selection.command.builder.js'

describe('GenerateContractLinesPdf E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('generates PDF of given contract lines', async () => {
    const user = await setup.authContext.getUser([Permission.CONTRACT_LINE_READ])
    const command = new GenerateContractLinesPdfSelectionCommandBuilder().build()

    mock.method(SapGetContractLineBatchUseCase.prototype, 'execute', () => {
      return command.selection!.map((selection) => {
        return new GetContractLineIndexResponseBuilder()
          .withVbeln(selection.contractNumber)
          .withPosnr(selection.contractItem)
          .build()
      })
    })

    const response = await request(setup.httpServer)
      .post(`/contract-lines/generate-pdf`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })

  it('generates PDF of contract lines with query selection', async () => {
    const user = await setup.authContext.getUser([Permission.CONTRACT_LINE_READ])
    const command = new GenerateContractLinesPdfQuerySelectionCommandBuilder()
      .build()

    mock.method(SapGetContractLineBatchUseCase.prototype, 'execute', () => {
      return [new GetContractLineIndexResponseBuilder()
        .withVbeln('123456')
        .withPosnr('000010')
        .build()]
    })

    const response = await request(setup.httpServer)
      .post(`/contract-lines/generate-pdf`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
