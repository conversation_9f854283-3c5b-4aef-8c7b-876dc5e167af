import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import qs from 'qs'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { ViewContractLineIndexFilterQueryBuilder } from './view-contract-line-index.filter.builder.js'
import { ViewContractLineIndexQueryBuilder } from './view-contract-line-index.query.builder.js'

describe('View contract line index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/contract-lines`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/contract-lines`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized and query with filters given', async () => {
    const user = await setup.authContext.getUser([Permission.CONTRACT_LINE_READ])

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    const query = new ViewContractLineIndexQueryBuilder()
      .withFilter(
        new ViewContractLineIndexFilterQueryBuilder()
          .withCustomerId(randomUUID())
          .withWasteProducerId(randomUUID())
          .withPickUpAddressIds([randomUUID()])
          .build()
      )
      .build()

    const response = await request(setup.httpServer)
      .get(`/contract-lines`)
      .set('Authorization', `Bearer ${user.token}`)
      .query(qs.stringify(query))

    expect(response).toHaveStatus(200)
  })
})
