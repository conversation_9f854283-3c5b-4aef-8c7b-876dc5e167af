import { ApiProperty } from '@nestjs/swagger'
import { SortQuery, SortDirectionApiProperty, SortDirection } from '@wisemen/pagination'
import { IsEnum } from 'class-validator'

export enum ViewContractLineIndexSortQueryKey {
  CONTRACT_NUMBER = 'contractNumber',
  CONTRACT_ITEM = 'contractItem',
  WASTE_MATERIAL = 'wasteMaterial',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  INSTALLATION_NAME = 'installationName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  ASN = 'asn',
  TC_NUMBER = 'tcNumber',
  EWC_CODE = 'ewcCode',
  END_TREATMENT_CENTER_ID = 'endTreatmentCenterId',
  END_TREATMENT_CENTER_NAME = 'endTreatmentCenterName',
  PROCESS_CODE = 'processCode',
  ESN_NUMBER = 'esnNumber'
}

export class ViewContractLineIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewContractLineIndexSortQueryKey, enumName: 'ViewContractLineIndexSortQueryKey' })
  @IsEnum(ViewContractLineIndexSortQueryKey)
  key: ViewContractLineIndexSortQueryKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}
