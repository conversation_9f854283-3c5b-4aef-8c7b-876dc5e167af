import { Controller, Get, Query } from '@nestjs/common'
import { ApiOAuth2, ApiOkResponse, ApiTags } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewContractLineIndexQuery } from './query/view-contract-line-index.query.js'
import { ViewContractLineIndexResponse } from './view-contract-line-index.response.js'
import { ViewContractLineIndexUseCase } from './view-contract-line-index.use-case.js'

@ApiTags('Contract line')
@ApiOAuth2([])
@Controller('contract-lines')
export class ViewContractLineIndexController {
  constructor (
    private readonly useCase: ViewContractLineIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(
    Permission.CONTRACT_LINE_READ,
    Permission.PICK_UP_REQUEST_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE
  )
  @ApiOkResponse({ type: ViewContractLineIndexResponse })
  public async viewContractLineIndex (
    @Query() query: ViewContractLineIndexQuery
  ): Promise<ViewContractLineIndexResponse> {
    return await this.useCase.execute(query)
  }
}
