import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewContractLineIndexController } from './view-contract-line-index.controller.js'
import { ViewContractLineIndexUseCase } from './view-contract-line-index.use-case.js'

@Module({
  imports: [
    SapModule,
    CustomerModule
  ],
  controllers: [
    ViewContractLineIndexController
  ],
  providers: [
    ViewContractLineIndexUseCase
  ],
  exports: [
    ViewContractLineIndexUseCase
  ]
})
export class ViewContractLineIndexModule {}
