import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../test/setup/test-bench.js'
import { ContractLineNotAccessibleError } from '../errors/contract-line-not-accessible.error.js'
import { GetContractLineIndexResponseBuilder } from '../../sap/use-cases/get-contract-line-index/tests/get-contract-line-index.response.builder.js'
import { ContractLineNotOfPickUpAddressesError } from '../errors/contract-line-not-of-pick-up-addresses.error.js'
import { ContractLineNotOfCustomerError } from '../errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfWasteProducersError } from '../errors/contract-line-not-of-waste-producers.error.js'
import { SapGetContractLineBatchUseCase } from '../../sap/use-cases/get-contract-line-batch/get-contract-line-batch.use-case.js'
import { ContractLineValidatorService } from './contract-line-validator.service.js'

describe('ContractLineValidatorService - Unit test', () => {
  let validator: ContractLineValidatorService

  let customerId: string
  let pickUpAddressId: string
  let contractNumber: string
  let contractItem: string

  let sapGetContractLineBatch: SinonStubbedInstance<SapGetContractLineBatchUseCase>

  before(() => {
    TestBench.setupUnitTest()

    customerId = randomUUID()
    pickUpAddressId = randomUUID()
    contractNumber = randomUUID()
    contractItem = randomUUID()

    sapGetContractLineBatch = createStubInstance(SapGetContractLineBatchUseCase)

    validator = new ContractLineValidatorService(
      sapGetContractLineBatch
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetContractLineBatch.execute.resolves([
      new GetContractLineIndexResponseBuilder()
        .withKunnr(customerId)
        .withKunnrWe(pickUpAddressId)
        .withVbeln(contractNumber)
        .withPosnr(contractItem)
        .build()
    ])
  }

  it('successfully checks user access to contract lines', async () => {
    await expect(validator.checkAccessContractLines(
      'pick-up',
      [{
        contractNumber,
        contractItem,
        tcNumber: null
      }]
    )).resolves.not.toThrow()
  })

  it('throws ContractLineNotAccessibleError error when no contracts found', async () => {
    sapGetContractLineBatch.execute.resolves([])

    await expect(validator.checkAccessContractLines(
      'pick-up',
      [{
        contractNumber,
        contractItem,
        tcNumber: null
      }]
    )).rejects.toThrow(ContractLineNotAccessibleError)
  })

  it('throws ContractLineNotOfCustomerError error when found contract line not of given customerId', async () => {
    await expect(validator.checkAccessContractLines(
      'pick-up',
      [{
        contractNumber,
        contractItem,
        tcNumber: null
      }],
      {
        customerId: randomUUID()
      }
    )).rejects.toThrow(ContractLineNotOfCustomerError)
  })

  it('throws ContractLineNotOfWasteProducersError error when found contract line not in given wasteProducerIds', async () => {
    await expect(validator.checkAccessContractLines(
      'pick-up',
      [{
        contractNumber,
        contractItem,
        tcNumber: null
      }],
      {
        wasteProducerIds: [randomUUID()]
      }
    )).rejects.toThrow(ContractLineNotOfWasteProducersError)
  })

  it('throws ContractLineNotOfPickUpAddressesError error when found contract line not in given pickUpAddressIds', async () => {
    await expect(validator.checkAccessContractLines(
      'pick-up',
      [{
        contractNumber,
        contractItem,
        tcNumber: null
      }],
      undefined,
      {
        pickUpAddressIds: [randomUUID()]
      }
    )).rejects.toThrow(ContractLineNotOfPickUpAddressesError)
  })
})
