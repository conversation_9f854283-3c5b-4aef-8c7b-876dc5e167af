import { Injectable } from '@nestjs/common'
import { ContractLineNotOfCustomerError } from '../errors/contract-line-not-of-customer.error.js'
import { ContractLineNotOfPickUpAddressesError } from '../errors/contract-line-not-of-pick-up-addresses.error.js'
import { ContractLineNotAccessibleError } from '../errors/contract-line-not-accessible.error.js'
import { ContractLineNotOfWasteProducersError } from '../errors/contract-line-not-of-waste-producers.error.js'
import { SapGetContractLineBatchUseCase } from '../../sap/use-cases/get-contract-line-batch/get-contract-line-batch.use-case.js'

@Injectable()
export class ContractLineValidatorService {
  constructor (
    private readonly sapGetContractLineBatch: SapGetContractLineBatchUseCase
  ) {}

  async checkAccessContractLines (
    type: 'packaging' | 'pick-up',
    contractLineIdentifiers: {
      contractNumber: string // Vbeln
      contractItem: string // Posnr
      tcNumber?: string | null // ActionNr
    }[],
    userRestrictions?: {
      customerId?: string
      wasteProducerIds?: string[]
    },
    options?: {
      pickUpAddressIds?: string[]
    }
  ): Promise<void> {
    const contractLineOptions = contractLineIdentifiers.map(material => ({
      Vbeln: material.contractNumber,
      Posnr: material.contractItem,
      ActionNr: material.tcNumber ?? undefined,
      MaterialTypes: type === 'packaging' ? ['YPAC', 'YSER'] : ['YWAS']
    }))
    const sapResult = await this.sapGetContractLineBatch.execute(contractLineOptions)

    for (const contractLineIdentifier of contractLineIdentifiers) {
      const foundContractLine = sapResult.find((contractItem) => {
        return contractItem.Vbeln === contractLineIdentifier.contractNumber
          && contractItem.Posnr === contractLineIdentifier.contractItem
          && (
            contractLineIdentifier.tcNumber == null
            || contractItem.ActionNr === contractLineIdentifier.tcNumber
          )
      })

      if (foundContractLine === undefined) {
        throw new ContractLineNotAccessibleError({
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        userRestrictions?.customerId != null
        && foundContractLine.Kunnr != null
        && foundContractLine.Kunnr !== ''
        && foundContractLine.Kunnr !== userRestrictions?.customerId
      ) {
        throw new ContractLineNotOfCustomerError({
          customerId: userRestrictions.customerId,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        userRestrictions?.wasteProducerIds != null
        && userRestrictions.wasteProducerIds.length > 0
        && foundContractLine.KunnrY2 != null
        && foundContractLine.KunnrY2 !== ''
        && !userRestrictions.wasteProducerIds.includes(foundContractLine.KunnrY2)
      ) {
        throw new ContractLineNotOfWasteProducersError({
          wasteProducerIds: userRestrictions.wasteProducerIds,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }

      if (
        options?.pickUpAddressIds != null
        && options.pickUpAddressIds.length > 0
        && foundContractLine.KunnrWe != null
        && foundContractLine.KunnrWe !== ''
        && !options.pickUpAddressIds.includes(foundContractLine.KunnrWe)
      ) {
        throw new ContractLineNotOfPickUpAddressesError({
          pickUpAddressIds: options.pickUpAddressIds,
          contractNumber: contractLineIdentifier.contractNumber,
          contractLineNumber: contractLineIdentifier.contractItem,
          tcNumber: contractLineIdentifier.tcNumber ?? null
        })
      }
    }
  }
}
