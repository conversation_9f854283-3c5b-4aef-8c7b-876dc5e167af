import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class ContractLineNotFoundErrorMeta {
  @ApiProperty({ type: String })
  readonly contractNumber: string

  @ApiProperty({ type: String })
  readonly contractItem: string

  @ApiProperty({ type: String, nullable: true })
  readonly tcNumber: string | null

  constructor (
    contractNumber: string,
    contractItem: string,
    tcNumber: string | null
  ) {
    this.contractNumber = contractNumber
    this.contractItem = contractItem
    this.tcNumber = tcNumber
  }
}

export class ContractLineNotFoundError extends BadRequestApiError {
  @ApiErrorCode('contract_line_not_found')
  readonly code = 'contract_line_not_found'

  @ApiErrorMeta()
  readonly meta: ContractLineNotFoundErrorMeta

  constructor (meta: ContractLineNotFoundErrorMeta, source?: ErrorSource) {
    super(translateCurrent('error.contract-line.contract_line_not_found'), source)
    this.meta = meta
  }
}
