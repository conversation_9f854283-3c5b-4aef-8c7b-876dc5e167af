import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class EmptyContractLinesSelectionError extends BadRequestApiError {
  @ApiErrorCode('empty_contract_lines_selection')
  readonly code = 'empty_contract_lines_selection'

  readonly meta: never

  constructor () {
    super(translateCurrent('error.contract-line.empty_contract_lines_selection'))
  }
}
