import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'

export class ContractLineNotOfPickUpAddressesErrorMeta {
  @ApiProperty({ type: String, isArray: true })
  readonly pickUpAddressIds: string[]

  @ApiProperty({ type: String })
  readonly contractNumber: string

  @ApiProperty({ type: String })
  readonly contractLineNumber: string

  @ApiProperty({ type: String, nullable: true })
  readonly tcNumber: string | null

  constructor (
    pickUpAddressIds: string[],
    contractNumber: string,
    contractLineNumber: string,
    tcNumber: string | null
  ) {
    this.pickUpAddressIds = pickUpAddressIds
    this.contractNumber = contractNumber
    this.contractLineNumber = contractLineNumber
    this.tcNumber = tcNumber
  }
}

export class ContractLineNotOfPickUpAddressesError extends BadRequestApiError {
  @ApiErrorCode('contract_line_not_of_pick_up_addresses')
  readonly code = 'contract_line_not_of_pick_up_addresses'

  @ApiErrorMeta()
  readonly meta: ContractLineNotOfPickUpAddressesErrorMeta

  constructor (meta: ContractLineNotOfPickUpAddressesErrorMeta) {
    super(translateCurrent('error.contract-line.contract_line_not_of_pick_up_addresses'))
    this.meta = meta
  }
}
