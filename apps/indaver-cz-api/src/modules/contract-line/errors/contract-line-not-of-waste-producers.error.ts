import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'

export class ContractLineNotOfWasteProducersErrorMeta {
  @ApiProperty({ type: String, isArray: true })
  readonly wasteProducerIds: string[]

  @ApiProperty({ type: String })
  readonly contractNumber: string

  @ApiProperty({ type: String })
  readonly contractLineNumber: string

  @ApiProperty({ type: String, nullable: true })
  readonly tcNumber: string | null

  constructor (
    wasteProducerIds: string[],
    contractNumber: string,
    contractLineNumber: string,
    tcNumber: string | null
  ) {
    this.wasteProducerIds = wasteProducerIds
    this.contractNumber = contractNumber
    this.contractLineNumber = contractLineNumber
    this.tcNumber = tcNumber
  }
}

export class ContractLineNotOfWasteProducersError extends BadRequestApiError {
  @ApiErrorCode('contract_line_not_of_waste_producers')
  readonly code = 'contract_line_not_of_waste_producers'

  @ApiErrorMeta()
  readonly meta: ContractLineNotOfWasteProducersErrorMeta

  constructor (meta: ContractLineNotOfWasteProducersErrorMeta) {
    super(translateCurrent('error.contract-line.contract_line_not_of_waste_producers'))
    this.meta = meta
  }
}
