import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'

export class ContractLineNotAccessibleErrorMeta {
  @ApiProperty({ type: String })
  readonly contractNumber: string

  @ApiProperty({ type: String })
  readonly contractLineNumber: string

  @ApiProperty({ type: String, nullable: true })
  readonly tcNumber: string | null

  constructor (
    contractNumber: string,
    contractLineNumber: string,
    tcNumber: string | null
  ) {
    this.contractNumber = contractNumber
    this.contractLineNumber = contractLineNumber
    this.tcNumber = tcNumber
  }
}

export class ContractLineNotAccessibleError extends BadRequestApiError {
  @ApiErrorCode('contract_line_not_accessible')
  readonly code = 'contract_line_not_accessible'

  @ApiErrorMeta()
  readonly meta: ContractLineNotAccessibleErrorMeta

  constructor (meta: ContractLineNotAccessibleErrorMeta) {
    super(translateCurrent('error.contract-line.contract_line_not_accessible'))
    this.meta = meta
  }
}
