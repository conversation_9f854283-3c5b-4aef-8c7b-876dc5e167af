import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'

export class ContractLineNotOfCustomerErrorMeta {
  @ApiProperty({ type: String })
  readonly customerId: string

  @ApiProperty({ type: String })
  readonly contractNumber: string

  @ApiProperty({ type: String })
  readonly contractLineNumber: string

  @ApiProperty({ type: String, nullable: true })
  readonly tcNumber: string | null

  constructor (
    customerId: string,
    contractNumber: string,
    contractLineNumber: string,
    tcNumber: string | null
  ) {
    this.customerId = customerId
    this.contractNumber = contractNumber
    this.contractLineNumber = contractLineNumber
    this.tcNumber = tcNumber
  }
}

export class ContractLineNotOfCustomerError extends BadRequestApiError {
  @ApiErrorCode('contract_line_not_of_customer')
  readonly code = 'contract_line_not_of_customer'

  @ApiErrorMeta()
  readonly meta: ContractLineNotOfCustomerErrorMeta

  constructor (meta: ContractLineNotOfCustomerErrorMeta) {
    super(translateCurrent('error.contract-line.contract_line_not_of_customer'))
    this.meta = meta
  }
}
