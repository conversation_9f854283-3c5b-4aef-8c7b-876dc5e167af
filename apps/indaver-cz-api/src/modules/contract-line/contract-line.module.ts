import { Module } from '@nestjs/common'
import { SapModule } from '../sap/sap.module.js'
import { ViewContractLineIndexModule } from './use-cases/view-contract-line-index/view-contract-line-index.module.js'
import { ContractLineValidatorService } from './validators/contract-line-validator.service.js'
import { ViewWprContractLineIndexModule } from './use-cases/view-wpr-contract-line-index/view-wpr-contract-line-index.module.js'
import { ViewPackagingRequestContractLineIndexModule } from './use-cases/view-packaging-request-contract-line-index/view-packaging-request-contract-line-index.module.js'
import { GenerateContractLinesPdfModule } from './use-cases/generate-contract-lines-pdf/generate-contract-lines-pdf.module.js'

@Module({
  imports: [
    ViewContractLineIndexModule,
    ViewWprContractLineIndexModule,
    ViewPackagingRequestContractLineIndexModule,
    GenerateContractLinesPdfModule,
    SapModule
  ],
  providers: [
    ContractLineValidatorService
  ],
  exports: [
    ContractLineValidatorService
  ]
})
export class ContractLineModule {}
