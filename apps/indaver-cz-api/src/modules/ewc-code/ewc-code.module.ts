import { Module } from '@nestjs/common'
import { SapModule } from '../sap/sap.module.js'
import { EwcValidatorService } from './services/ewc-validator.service.js'
import { ViewEwcCodeIndexModule } from './use-cases/view-ewc-code-index/view-ewc-code-index.module.js'

@Module({
  imports: [
    SapModule,

    ViewEwcCodeIndexModule
  ],
  providers: [
    EwcValidatorService
  ],
  exports: [
    EwcValidatorService
  ]
})
export class EwcCodeModule {}
