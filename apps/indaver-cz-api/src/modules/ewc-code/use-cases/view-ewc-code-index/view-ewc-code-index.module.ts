import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { MapEwcCodeSapService } from '../../services/map-ewc-code-sap.service.js'
import { ViewEwcCodeIndexController as ViewEwcCodeIndexController } from './view-ewc-code-index.controller.js'
import { ViewEwcCodeIndexUseCase as ViewEwcCodeIndexUseCase } from './view-ewc-code-index.use-case.js'

@Module({
  imports: [
    SapModule
  ],
  controllers: [
    ViewEwcCodeIndexController
  ],
  providers: [
    ViewEwcCodeIndexUseCase,

    MapEwcCodeSapService
  ]
})
export class ViewEwcCodeIndexModule {}
