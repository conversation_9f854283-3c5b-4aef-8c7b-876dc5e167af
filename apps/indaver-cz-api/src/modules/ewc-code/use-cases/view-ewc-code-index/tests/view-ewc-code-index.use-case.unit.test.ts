import { afterEach, before, describe, it } from 'node:test'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewEwcCodeIndexUseCase } from '../view-ewc-code-index.use-case.js'
import { EwcCode } from '../../../types/ewc-code.js'
import { SapGetEwcCodeIndexUseCase } from '../../../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.use-case.js'
import { GetEwcCodeIndexResponseBuilder } from '../../../../sap/use-cases/get-ewc-code-index/tests/get-ewc-code-index.response.builder.js'

describe('View EWC code index use-case unit test', () => {
  let useCase: ViewEwcCodeIndexUseCase

  let sapGetEwcCodeIndex: SinonStubbedInstance<SapGetEwcCodeIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    sapGetEwcCodeIndex = createStubInstance(SapGetEwcCodeIndexUseCase)

    useCase = new ViewEwcCodeIndexUseCase(
      sapGetEwcCodeIndex
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    sapGetEwcCodeIndex.execute.resolves([new GetEwcCodeIndexResponseBuilder().build()])
  }

  it('Returns parsed result from SAP', async () => {
    const ewcCodes: EwcCode[] = [{
      code: '01',
      description: 'wastes resulting from exploration, mining, quarrying, and physical and chemical treatment of minerals'
    }, {
      code: '01 01',
      description: 'wastes from mineral excavation'
    }, {
      code: '01 01 01',
      description: 'wastes from mineral metalliferous excavation'
    }]

    sapGetEwcCodeIndex.execute.resolves(
      ewcCodes.map(
        code => new GetEwcCodeIndexResponseBuilder()
          .withCode(code.code)
          .withName(code.description)
          .build()
      )
    )

    const result = await useCase.execute({})

    expect(result.items).toStrictEqual(expect.arrayContaining(ewcCodes))
  })
})
