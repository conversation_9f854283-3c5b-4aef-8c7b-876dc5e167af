import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('View EWC code index e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/ewc-codes`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .get(`/ewc-codes`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.WASTE_INQUIRY_MANAGE])

    const response = await request(setup.httpServer)
      .get(`/ewc-codes`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(200)
  })
})
