import { Controller, Get, Query } from '@nestjs/common'
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewEwcCodeIndexResponse as ViewEwcCodeIndexResponse } from './view-ewc-code-index.response.js'
import { ViewEwcCodeIndexUseCase } from './view-ewc-code-index.use-case.js'
import { ViewEwcCodeIndexQuery } from './view-ewc-code-index.query.js'

@ApiTags('EWC code')
@ApiOAuth2([])
@Controller('ewc-codes')
export class ViewEwcCodeIndexController {
  constructor (
    private readonly useCase: ViewEwcCodeIndexUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ
  )
  @ApiOkResponse({ type: ViewEwcCodeIndexResponse })
  public async viewEwcCodeIndex (
    @Query() query: ViewEwcCodeIndexQuery
  ): Promise<ViewEwcCodeIndexResponse> {
    return await this.useCase.execute(query)
  }
}
