import { ApiProperty } from '@nestjs/swagger'
import { EwcCode } from '../../types/ewc-code.js'

class EwcCodeResponse {
  @ApiProperty({ type: String, description: 'Code existing out of 1, 2 or 3 levels' })
  code: string

  @ApiProperty({ type: String })
  description: string

  constructor (ewcCode: EwcCode) {
    this.code = ewcCode.code
    this.description = ewcCode.description
  }
}

export class ViewEwcCodeIndexResponse {
  @ApiProperty({ type: EwcCodeResponse, isArray: true })
  items: EwcCodeResponse[]

  constructor (items: EwcCode[]) {
    this.items = items.map(ewcCode => new EwcCodeResponse(ewcCode))
  }
}
