import { Injectable } from '@nestjs/common'
import { GetSapEwcCodeIndexResponse } from '../../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.response.js'
import { MapEwcCodeSapService } from '../../services/map-ewc-code-sap.service.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { SapGetEwcCodeIndexUseCase } from '../../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { mapLocaleToSapLocale } from '../../../localization/enums/locale.enum.js'
import { ViewEwcCodeIndexResponse } from './view-ewc-code-index.response.js'
import { ViewEwcCodeIndexQuery } from './view-ewc-code-index.query.js'

@Injectable()
export class ViewEwcCodeIndexUseCase {
  constructor (
    private readonly sapGetEwcCodeIndex: SapGetEwcCodeIndexUseCase
  ) {}

  public async execute (
    query: ViewEwcCodeIndexQuery
  ): Promise<ViewEwcCodeIndexResponse> {
    const sapQuery = this.getSapQuery(query)
    const sapEwcCodes = await this.sapGetEwcCodeIndex.execute(sapQuery)

    const ewcCodes = MapEwcCodeSapService.mapResultsToEwcCodes(sapEwcCodes)

    return new ViewEwcCodeIndexResponse(ewcCodes)
  }

  private getSapQuery (
    query: ViewEwcCodeIndexQuery
  ): SapQuery<GetSapEwcCodeIndexResponse> {
    const sapQuery = new SapQuery<GetSapEwcCodeIndexResponse>(query)
      .where('Ident', 'EURAL')
      .andWhere('Spras', mapLocaleToSapLocale(getCurrentLanguage()))
      .addOrderBy('Code', 'asc')

    return sapQuery
  }
}
