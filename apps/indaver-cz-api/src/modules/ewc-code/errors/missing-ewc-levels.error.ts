import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class MissingEwcLevelsError extends BadRequestApiError {
  @ApiErrorCode('missing_ewc_levels')
  readonly code = 'missing_ewc_levels'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.ewc-code.missing_ewc_levels')
    super(detail, source)
  }
}
