import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class EwcCodeNotFound extends BadRequestApiError {
  @ApiErrorCode('ewc_code_not_found')
  readonly code = 'ewc_code_not_found'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.ewc-code.ewc_code_not_found')
    super(detail, source)
  }
}
