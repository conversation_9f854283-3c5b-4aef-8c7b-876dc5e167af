import { Injectable } from '@nestjs/common'
import { getCurrentLanguage } from '../../localization/helpers/translate.helper.js'
import { GetSapEwcCodeIndexResponse } from '../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.response.js'
import { SapGetEwcCodeIndexUseCase } from '../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.use-case.js'
import { SapQuery } from '../../sap/query/sap-query.js'
import { Locale, mapLocaleToSapLocale } from '../../localization/enums/locale.enum.js'

export interface EwcCombinationOptions {
  ewcLevel1?: EwcLevelOption
  ewcLevel2?: EwcLevelOption
  ewcLevel3?: EwcLevelOption
}

export interface EwcLevelOption {
  name: string
  errorPointer?: string
}

@Injectable()
export class EwcValidatorService {
  constructor (
    private readonly sapGetEwcCodeIndex: SapGetEwcCodeIndexUseCase
  ) {}

  hasRequiredLevels (
    options: EwcCombinationOptions
  ): { isValid: boolean, errorPointer?: string } {
    const {
      ewcLevel1,
      ewcLevel2,
      ewcLevel3
    } = options

    if (ewcLevel3 != null && (ewcLevel1 == null || ewcLevel2 == null)) {
      return { isValid: false, errorPointer: ewcLevel3.errorPointer }
    }

    if (ewcLevel2 != null && ewcLevel1 == null) {
      return { isValid: false, errorPointer: ewcLevel2.errorPointer }
    }

    return { isValid: true }
  }

  public async doesEwcCodeExists (
    options: EwcCombinationOptions
  ): Promise<boolean> {
    let ewcCode = ''

    if (options.ewcLevel1?.name != null) {
      ewcCode = ewcCode + options.ewcLevel1?.name

      if (options.ewcLevel2?.name != null) {
        ewcCode = ewcCode + ' ' + options.ewcLevel2?.name

        if (options.ewcLevel3?.name != null) {
          ewcCode = ewcCode + ' ' + options.ewcLevel3?.name
        }
      }
    }

    if (ewcCode === '') return false

    const sapEwcCode = await this.getEwcCodeByCode(ewcCode, getCurrentLanguage())

    return sapEwcCode !== null
  }

  private async getEwcCodeByCode (
    code: string,
    language: Locale
  ): Promise<GetSapEwcCodeIndexResponse | null> {
    const sapQuery = new SapQuery<GetSapEwcCodeIndexResponse>()
      .where('Ident', 'EURAL')
      .andWhere('Spras', mapLocaleToSapLocale(language))
      .andWhere('Code', code)

    const ewcCodes = await this.sapGetEwcCodeIndex.execute(sapQuery)

    return ewcCodes.length > 0 ? ewcCodes[0] : null
  }
}
