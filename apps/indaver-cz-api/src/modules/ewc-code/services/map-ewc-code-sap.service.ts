import { randomUUID } from 'crypto'
import { GetSapEwcCodeIndexResponse } from '../../sap/use-cases/get-ewc-code-index/get-ewc-code-index.response.js'
import { EwcCode } from '../types/ewc-code.js'
import { SAP_ENDPOINTS } from '../../sap/constants/endpoint.constants.js'

export class MapEwcCodeSapService {
  static mapResultToEwcCode (response: GetSapEwcCodeIndexResponse): EwcCode {
    return {
      code: response.Code,
      description: response.Name.toLowerCase()
    }
  }

  static mapResultsToEwcCodes (responses: GetSapEwcCodeIndexResponse[] = []): EwcCode[] {
    return responses.map(response => this.mapResultToEwcCode(response))
  }

  static mapEwcCodeToResult (ewcCode: EwcCode): GetSapEwcCodeIndexResponse {
    const id = randomUUID()

    return {
      __metadata: {
        id,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.EWC_CODE.INDEX}/${id}`,
        type: 'ZMDX_TAOF_SRV.ewc'
      },
      Spras: '',
      RecnKey: '',
      Ident: '',
      Code: ewcCode.code,
      Name: ewcCode.description,
      Codename: ''
    }
  }

  static mapEwcCodesToResults (ewcCodes: EwcCode[]): GetSapEwcCodeIndexResponse[] {
    return ewcCodes.map(ewcCode => this.mapEwcCodeToResult(ewcCode))
  }
}
