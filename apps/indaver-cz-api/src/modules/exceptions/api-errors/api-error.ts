import { ApiProperty } from '@nestjs/swagger'
import { ErrorSource } from '../types/json-api-error.type.js'

export abstract class ApiError extends Error {
  readonly abstract code: string
  readonly abstract status: string
  readonly abstract meta: unknown
  readonly source?: ErrorSource

  @ApiProperty({
    required: false,
    description: 'a human-readable explanation specific to this occurrence of the problem'
  })
  readonly detail?: string

  protected constructor (detail: string) {
    super()
    this.detail = detail
  }
}
