import { HttpStatus } from '@nestjs/common'
import { ApiProperty } from '@nestjs/swagger'
import { ErrorSource } from '../types/json-api-error.type.js'
import { ApiError } from './api-error.js'
import { ApiErrorStatus } from './api-error-status.decorator.js'

export abstract class BadRequestApiError extends ApiError {
  @ApiErrorStatus(HttpStatus.BAD_REQUEST)
  declare status: '400'

  @ApiProperty({ type: ErrorSource, required: false })
  source?: ErrorSource

  constructor (detail: string, source?: ErrorSource) {
    super(detail)
    this.status = '400'
    this.source = source
  }
}
