import dayjs from 'dayjs'
import { ApiErrorCode } from '../api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../api-errors/bad-request.api-error.js'
import { ErrorSource } from '../types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class DateMustBeAfterError extends BadRequestApiError {
  @ApiErrorCode('date_must_be_after')
  readonly code = 'date_must_be_after'

  meta: never

  constructor (date?: string, source?: ErrorSource) {
    const detail = translateCurrent('error.generic.date_must_be_after', {
      args: { date: date ?? dayjs().format('YYYY-MM-DD') }
    })
    super(detail, source)
  }
}
