import { HttpStatus } from '@nestjs/common'
import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorStatus } from '../../../modules/exceptions/api-errors/api-error-status.decorator.js'
import { ApiError } from '../../../modules/exceptions/api-errors/api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { I18nPath } from '../../localization/generated/i18n.generated.js'

export class UnauthorizedError extends ApiError {
  @ApiErrorCode('unauthorized')
  readonly code = 'unauthorized'

  meta: never

  @ApiErrorStatus(HttpStatus.UNAUTHORIZED)
  declare status: '401'

  constructor (detail?: I18nPath) {
    const translatedDetail = translateCurrent(detail ?? 'error.generic.unauthorized')
    super(translatedDetail)
    this.status = '401'
  }
}
