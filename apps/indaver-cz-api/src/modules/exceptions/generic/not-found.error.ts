import { TranslateOptions } from 'nestjs-i18n'
import { ApiErrorCode } from '../../../modules/exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../../modules/exceptions/api-errors/not-found.api-error.js'
import { I18nPath } from '../../localization/generated/i18n.generated.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class NotFoundError extends NotFoundApiError {
  @ApiErrorCode('not_found')
  readonly code = 'not_found'

  meta: never

  constructor (detail?: I18nPath, options?: TranslateOptions) {
    const translatedDetail = translateCurrent(detail ?? 'error.generic.not_found', options)

    super(translatedDetail)
  }
}
