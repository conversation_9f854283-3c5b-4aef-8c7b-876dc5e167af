import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorCode } from '../api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../api-errors/bad-request.api-error.js'
import { ErrorSource } from '../types/json-api-error.type.js'

export class FieldMustBeNullError extends BadRequestApiError {
  @ApiErrorCode('field_must_be_null')
  readonly code = 'field_must_be_null'

  meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.generic.field_must_be_null')
    super(detail, source)
  }
}
