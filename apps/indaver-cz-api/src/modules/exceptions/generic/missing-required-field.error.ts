import { translateCurrent } from '../../localization/helpers/translate.helper.js'
import { ApiErrorCode } from '../api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../api-errors/bad-request.api-error.js'
import { ErrorSource } from '../types/json-api-error.type.js'

export class MissingRequiredFieldError extends BadRequestApiError {
  @ApiErrorCode('missing_required_field')
  readonly code = 'missing_required_field'

  meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.generic.missing_required_field')
    super(detail, source)
  }
}
