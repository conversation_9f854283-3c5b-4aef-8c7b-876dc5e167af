import { ApiErrorCode } from '../../../../exceptions/api-errors/api-error-code.decorator.js'
import { ConflictApiError } from '../../../../exceptions/api-errors/conflict.api-error.js'

class AlreadySubscribedErrorMeta {
  email: string

  constructor (email: string) {
    this.email = email
  }
}

export class AlreadySubscribedError extends ConflictApiError {
  @ApiErrorCode('already_subscribed')
  readonly code = 'already_subscribed'

  readonly meta: AlreadySubscribedErrorMeta

  constructor (email: string) {
    super(
      'The given e-mail address is already subscribed.'
    )

    this.meta = new AlreadySubscribedErrorMeta(email)
  }
}
