import { ApiErrorCode } from '../../../../exceptions/api-errors/api-error-code.decorator.js'
import { ConflictApiError } from '../../../../exceptions/api-errors/conflict.api-error.js'

class OptInAlreadyRequestedErrorMeta {
  email: string

  constructor (email: string) {
    this.email = email
  }
}

export class OptInAlreadyRequestedError extends ConflictApiError {
  @ApiErrorCode('opt_in_already_requested')
  readonly code = 'opt_in_already_requested'

  readonly meta: OptInAlreadyRequestedErrorMeta

  constructor (email: string) {
    super(
      'The given e-mail address has already requested a newsletter subscription. Please check your mail inbox.'
    )
    this.meta = new OptInAlreadyRequestedErrorMeta(email)
  }
}
