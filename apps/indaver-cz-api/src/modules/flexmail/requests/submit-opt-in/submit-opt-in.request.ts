import { Inject, Injectable } from '@nestjs/common'
import { AxiosError, AxiosInstance } from 'axios'
import { SubmitOptInCommand } from './submit-opt-in.command.js'
import { AlreadySubscribedError } from './errors/already-subscribed.error.js'
import { OptInAlreadyRequestedError } from './errors/opt-in-already-requested.error.js'

@Injectable()
export class SubmitOptInRequest {
  constructor (
    @Inject('FLEXMAIL_CLIENT') private client: AxiosInstance
  ) {}

  async execute (command: SubmitOptInCommand): Promise<void> {
    return await this.client.post<void>(`/opt-ins`, command)
      .then((_response) => {
        return
      })
      .catch((error: AxiosError) => {
        if (error.status === 409) {
          const conflictError = error as AxiosError<{ detail?: string }>

          if (conflictError.response?.data.detail === 'An opt-in for this email address already exists') {
            throw new OptInAlreadyRequestedError(command.email)
          }
          if (conflictError.response?.data.detail === 'A contact with this email address already exists') {
            throw new AlreadySubscribedError(command.email)
          }
        }

        throw error
      })
  }
}
