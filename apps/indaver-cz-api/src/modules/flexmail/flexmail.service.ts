import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { SubmitOptInRequest } from './requests/submit-opt-in/submit-opt-in.request.js'
import { SupportedLanguages } from './enums/supported-languages.enum.js'

@Injectable()
export class FlexmailService {
  constructor (
    private readonly configService: ConfigService,
    private readonly submitOptInRequest: SubmitOptInRequest
  ) {}

  public async submitOptIn (
    email: string,
    options?: {
      firstName?: string
      lastName?: string
      language?: SupportedLanguages
      customFields?: object
    }
  ): Promise<void> {
    await this.submitOptInRequest.execute({
      email: email,
      opt_in_form_id: Number(this.configService.getOrThrow<number>('FLEXMAIL_FORM_ID')),
      first_name: options?.firstName,
      name: options?.lastName,
      language: options?.language,
      custom_fields: options?.customFields
    })
  }
}
