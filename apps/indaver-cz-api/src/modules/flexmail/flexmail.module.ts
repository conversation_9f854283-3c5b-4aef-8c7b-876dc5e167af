import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import Joi from 'joi'
import { createFlexmailClient } from './flexmail.client.js'
import { FlexmailService } from './flexmail.service.js'
import { SubmitOptInRequest } from './requests/submit-opt-in/submit-opt-in.request.js'

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE,
      validationSchema: Joi.object({
        FLEXMAIL_BASE_URL: Joi.string().uri().required(),
        FLEXMAIL_ACCOUNT_ID: Joi.string().required(),
        FLEXMAIL_ACCESS_TOKEN: Joi.string().required(),
        FLEXMAIL_FORM_ID: Joi.number().required()
      })
    })
  ],
  providers: [
    {
      provide: 'FLEXMAIL_CLIENT',
      useFactory: (configService: ConfigService) => createFlexmailClient(configService),
      inject: [ConfigService]
    },
    FlexmailService,

    SubmitOptInRequest
  ],
  exports: [
    FlexmailService
  ]
})
export class FlexmailModule {}
