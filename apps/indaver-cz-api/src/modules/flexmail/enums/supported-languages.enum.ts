import { Locale } from '../../localization/enums/locale.enum.js'

export enum SupportedLanguages {
  NL = 'nl',
  FR = 'fr',
  DE = 'de',
  IT = 'it',
  ES = 'es',
  RU = 'ru',
  DA = 'da',
  SE = 'se',
  ZH = 'zh',
  PT = 'pt',
  PL = 'pl',
  EN = 'en',
  HU = 'hu',
  BG = 'bg',
  HR = 'hr',
  CS = 'cs',
  ET = 'et',
  FI = 'fi',
  EL = 'el',
  GA = 'ga',
  LV = 'lv',
  LT = 'lt',
  MT = 'mt',
  RO = 'ro',
  SK = 'sk',
  SL = 'sl',
  UK = 'uk'
}

export function mapLocaleToSupportedLanguage (locale: Locale): SupportedLanguages {
  switch (locale) {
    case Locale.EN_GB:
      return SupportedLanguages.EN
    case Locale.NL_BE:
      return SupportedLanguages.NL
    case Locale.FR_FR:
      return SupportedLanguages.FR
    case Locale.ES_ES:
      return SupportedLanguages.ES
    case Locale.DE_DE:
      return SupportedLanguages.DE
    default:
      return SupportedLanguages.EN
  }
}
