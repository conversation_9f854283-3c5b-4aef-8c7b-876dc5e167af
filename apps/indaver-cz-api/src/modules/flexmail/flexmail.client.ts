import { ConfigService } from '@nestjs/config'
import { captureException } from '@sentry/nestjs'
import axios, { AxiosError, AxiosInstance } from 'axios'

export function createFlexmailClient (configService: ConfigService): AxiosInstance {
  const instance = axios.create({
    baseURL: configService.getOrThrow<string>('FLEXMAIL_BASE_URL'),
    headers: {
      'content-type': 'application/json',
      'accept': 'application/json'
    },
    auth: {
      username: configService.getOrThrow<string>('FLEXMAIL_ACCOUNT_ID'),
      password: configService.getOrThrow<string>('FLEXMAIL_ACCESS_TOKEN')
    }
  })

  instance.interceptors.response.use(
    undefined,
    (error: AxiosError) => {
      if (error.status === 500) {
        captureException(error)
      }

      throw error
    }
  )

  return instance
}
