import { CertificateDocType } from '../enums/certificate-doc-type.enum.js'

export class CertificateDocTypeMapper {
  static fromResponse (response: string): CertificateDocType {
    switch (response) {
      case 'Confirmation of Receipt':
        return CertificateDocType.RECEIPT_CONFIRMATION
      case 'Confirmation of blending':
        return CertificateDocType.BLENDING_CONFIRMATION
      case 'Certificate of treatment':
        return CertificateDocType.CERTIFICATE_OF_TREATMENT
      case 'Treatment certificates':
        return CertificateDocType.TREATMENT_CERTIFICATES
      case 'TFS certificates':
        return CertificateDocType.TFS_CERTIFICATES
      default:
        throw new Error(`Unknown SAP document type: ${response}`)
    }
  }

  static toSap (docType: CertificateDocType): string {
    switch (docType) {
      case CertificateDocType.RECEIPT_CONFIRMATION:
        return 'Confirmation of Receipt'
      case CertificateDocType.BLENDING_CONFIRMATION:
        return 'Confirmation of blending'
      case CertificateDocType.CERTIFICATE_OF_TREATMENT:
        return 'Certificate of treatment'
      case CertificateDocType.TREATMENT_CERTIFICATES:
        return 'Treatment certificates'
      case CertificateDocType.TFS_CERTIFICATES:
        return 'TFS certificates'
      default:
        throw new Error(`Unknown certificate document type: ${docType as string}`)
    }
  }
}
