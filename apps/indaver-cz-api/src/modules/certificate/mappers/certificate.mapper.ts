import assert from 'assert'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'
import { SapGetCertificateIndexResponse } from '../../sap/use-cases/get-certificate-index/get-certificate-index.response.js'
import { Certificate } from '../types/certificate.type.js'
import { CertificateDocTypeMapper } from './sap-doc-type.mapper.js'

export class CertificateMapper {
  static fromResponse (response: SapGetCertificateIndexResponse): Certificate {
    assert(response.InvoiceUrl !== undefined, 'InvoiceUrl not selected in certificate')

    return {
      docType: response.DocType != null
        ? CertificateDocTypeMapper.fromResponse(response.DocType)
        : null,
      salesOrder: this.isNotEmptyValue(response.SalesOrder) ? response.SalesOrder : null,
      salesOrderLine: this.isNotEmptyValue(response.SalesOrderPos) ? response.SalesOrderPos : null,
      description: this.isNotEmptyValue(response.Arktx) ? response.Arktx : null,
      collectionDate: this.isNotEmptyValue(response.Yycolldate)
        ? SapDateFormatterService.parseDate(response.Yycolldate)
        : null,
      deliveryDate: this.isNotEmptyValue(response.Ketdat)
        ? SapDateFormatterService.parseDate(response.Ketdat)
        : null,
      dispositionPickUpDate: this.isNotEmptyValue(response.Dareg)
        ? SapDateFormatterService.parseDate(response.Dareg)
        : null,
      dispositionDeliveryDate: this.isNotEmptyValue(response.Dalbg)
        ? SapDateFormatterService.parseDate(response.Dalbg)
        : null,
      treatmentCentre: this.isNotEmptyValue(response.NameY0) ? response.NameY0 : null,
      endTreatmentCentre: this.isNotEmptyValue(response.NameYe)
        ? response.NameYe
        : null,
      ewcCode: this.isNotEmptyValue(response.Yeural) ? response.Yeural : null,
      wtfForm: this.isNotEmptyValue(response.Yc1Form) ? response.Yc1Form : null,
      tfs: this.isNotEmptyValue(response.TfsKey) ? response.TfsKey : null,
      disposalDate: this.isNotEmptyValue(response.Yvdatdes)
        ? SapDateFormatterService.parseDate(response.Yvdatdes)
        : null,
      printDate: this.isNotEmptyValue(response.Erdat)
        ? SapDateFormatterService.parseDate(response.Erdat)
        : null,
      wasteProducerName: this.isNotEmptyValue(response.AddressY2) ? response.AddressY2 : null,
      wasteProducerId: this.isNotEmptyValue(response.KunnrY2) ? response.KunnrY2 : null,
      contract: this.isNotEmptyValue(response.contract) ? response.contract : null,
      contractItem: this.isNotEmptyValue(response.ContractPos) ? response.ContractPos : null,
      fileName: response.InvoiceUrl,
      pickUpAddressName: this.isNotEmptyValue(response.AddressWe) ? response.AddressWe : null,
      pickUpAddressId: this.isNotEmptyValue(response.KunnrWe) ? response.KunnrWe : null,
      invoice: this.isNotEmptyValue(response.Invoice) ? response.Invoice : null,
      customerId: this.isNotEmptyValue(response.Kunnr) ? response.Kunnr : null,
      customerName: this.isNotEmptyValue(response.Name1) ? response.Name1 : null
    }
  }

  static fromResponses (responses: SapGetCertificateIndexResponse[]): Certificate[] {
    return responses.map(response => this.fromResponse(response))
  }

  private static isNotEmptyValue (value: string | null | undefined): value is string {
    return value != null && value !== ''
  }
}
