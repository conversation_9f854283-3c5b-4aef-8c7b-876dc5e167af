import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { SapGetCertificateIndexResponseBuilder } from '../../../sap/use-cases/get-certificate-index/get-certificate-index.response.builder.js'
import { CertificateMapper } from '../certificate.mapper.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { CertificateDocTypeMapper } from '../sap-doc-type.mapper.js'

describe('CertificateMapper - Unit Tests', () => {
  before(() => TestBench.setupUnitTest())

  it('maps the response correctly to a Certificate type', () => {
    const response = new SapGetCertificateIndexResponseBuilder().build()

    const certificate = CertificateMapper.fromResponse(response)

    expect(certificate).toMatchObject({
      docType: response.DocType != null
        ? CertificateDocTypeMapper.fromResponse(response.DocType)
        : null,
      salesOrder: response.SalesOrder,
      salesOrderLine: response.SalesOrderPos,
      description: response.Arktx,
      collectionDate: SapDateFormatterService.parseDate(response.Yycolldate!),
      deliveryDate: SapDateFormatterService.parseDate(response.Ketdat!),
      dispositionPickUpDate: SapDateFormatterService.parseDate(response.Dareg!),
      dispositionDeliveryDate: SapDateFormatterService.parseDate(response.Dalbg!),
      treatmentCentre: response.NameY0,
      endTreatmentCentre: response.NameYe,
      ewcCode: null,
      wtfForm: response.Yc1Form,
      tfs: response.TfsKey,
      disposalDate: SapDateFormatterService.parseDate(response.Yvdatdes!),
      printDate: SapDateFormatterService.parseDate(response.Erdat!),
      wasteProducerName: response.AddressY2,
      wasteProducerId: response.KunnrY2,
      contract: response.contract,
      contractItem: response.DocumentItem,
      invoice: response.Invoice,
      pickUpAddressName: response.AddressWe,
      pickUpAddressId: response.KunnrWe,
      customerId: response.Kunnr,
      customerName: response.Name1
    })
  })
})
