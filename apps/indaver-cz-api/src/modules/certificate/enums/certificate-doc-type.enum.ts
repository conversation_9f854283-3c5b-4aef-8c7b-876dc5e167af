import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'

export enum CertificateDocType {
  RECEIPT_CONFIRMATION = 'receipt-confirmation',
  BLENDING_CONFIRMATION = 'blending-confirmation',
  TREATMENT_CERTIFICATES = 'treatment-certificates',
  CERTIFICATE_OF_TREATMENT = 'certificate-of-treatment',
  TFS_CERTIFICATES = 'tf-certificates'
}

export function CertificateDocTypeApiProperty (
  options?: ApiPropertyOptions
): PropertyDecorator {
  return ApiProperty({
    ...options,
    enumName: 'CertificateDocType',
    enum: CertificateDocType
  })
}
