import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { Certificate } from '../../types/certificate.type.js'
import { calculatePageSize } from '../../../../utils/helpers/unknown-page-size.helper.js'
import { CertificateDynamicTableFields } from '../../types/certificate.dynamic-table-fields.type.js'
import { CertificateDocType, CertificateDocTypeApiProperty } from '../../enums/certificate-doc-type.enum.js'

export class CertificateIndexResponse implements CertificateDynamicTableFields {
  @CertificateDocTypeApiProperty({ nullable: true })
  docType: CertificateDocType | null

  @ApiProperty({ type: String, nullable: true })
  salesOrder: string | null

  @ApiProperty({ type: String, nullable: true })
  salesOrderLine: string | null

  @ApiProperty({ type: String, nullable: true })
  description: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  collectionDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  deliveryDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  dispositionPickUpDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  dispositionDeliveryDate: string | null

  @ApiProperty({ type: String, nullable: true })
  treatmentCentre: string | null

  @ApiProperty({ type: String, nullable: true })
  endTreatmentCentre: string | null

  @ApiProperty({ type: String, nullable: true })
  ewcCode: string | null

  @ApiProperty({ type: String, nullable: true })
  wtfForm: string | null

  @ApiProperty({ type: String, nullable: true })
  tfs: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  disposalDate: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date-time' })
  printDate: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerId: string | null

  @ApiProperty({ type: String, nullable: true })
  wasteProducerName: string | null

  @ApiProperty({ type: String, nullable: true })
  contract: string | null

  @ApiProperty({ type: String, nullable: true })
  contractItem: string | null

  @ApiProperty({ type: String })
  fileName: string

  @ApiProperty({ type: String, nullable: true })
  invoice: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressId: string | null

  @ApiProperty({ type: String, nullable: true })
  pickUpAddressName: string | null

  @ApiProperty({ type: String, nullable: true })
  customerId: string | null

  @ApiProperty({ type: String, nullable: true })
  customerName: string | null

  constructor (certificate: Certificate) {
    this.docType = certificate.docType
    this.salesOrder = certificate.salesOrder
    this.salesOrderLine = certificate.salesOrderLine
    this.description = certificate.description
    this.collectionDate = certificate.collectionDate?.toISOString?.() ?? null
    this.deliveryDate = certificate.deliveryDate?.toISOString?.() ?? null
    this.dispositionPickUpDate = certificate.dispositionPickUpDate?.toISOString?.() ?? null
    this.dispositionDeliveryDate = certificate.dispositionDeliveryDate?.toISOString?.() ?? null
    this.treatmentCentre = certificate.treatmentCentre
    this.endTreatmentCentre = certificate.endTreatmentCentre
    this.ewcCode = certificate.ewcCode
    this.wtfForm = certificate.wtfForm
    this.tfs = certificate.tfs
    this.disposalDate = certificate.disposalDate?.toISOString?.() ?? null
    this.printDate = certificate.printDate?.toISOString?.() ?? null
    this.wasteProducerId = certificate.wasteProducerId
    this.wasteProducerName = certificate.wasteProducerName
    this.contract = certificate.contract
    this.contractItem = certificate.contractItem
    this.fileName = certificate.fileName
    this.invoice = certificate.invoice
    this.pickUpAddressId = certificate.pickUpAddressId
    this.pickUpAddressName = certificate.pickUpAddressName
    this.customerId = certificate.customerId
    this.customerName = certificate.customerName
  }
}

export class ViewCertificateIndexResponse
  extends PaginatedOffsetResponse<CertificateIndexResponse> {
  @ApiProperty({ type: CertificateIndexResponse, isArray: true })
  declare items: CertificateIndexResponse[]

  constructor (certificates: Certificate[], total: number | null, limit: number, offset: number) {
    const items = certificates.map(certificate => new CertificateIndexResponse(certificate))

    if (total === null) {
      total = calculatePageSize(items, offset, limit)
    }

    super(items, total, limit, offset)
  }
}
