import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ViewCertificateIndexUseCase } from './view-certificate-index.use-case.js'
import { ViewCertificateIndexQuery } from './view-certificate-index.query.js'
import { ViewCertificateIndexResponse } from './view-certificate-index.response.js'

@ApiTags('Certificate')
@ApiOAuth2([])
@Controller('certificates')
export class ViewCertificateIndexController {
  constructor (
    private readonly useCase: ViewCertificateIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.CERTIFICATE_READ, Permission.CERTIFICATE_MANAGE)
  @ApiOkResponse({ type: ViewCertificateIndexResponse })
  public async viewCertificateIndex (
     @Query() query: ViewCertificateIndexQuery
  ): Promise<ViewCertificateIndexResponse> {
    return this.useCase.execute(query)
  }
}
