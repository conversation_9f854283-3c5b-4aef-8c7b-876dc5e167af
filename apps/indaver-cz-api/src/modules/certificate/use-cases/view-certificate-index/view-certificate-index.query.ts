import { FilterQuery, PaginatedOffsetSearchQuery, SortDirection, SortDirectionApiProperty, SortQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, Equals, IsArray, IsEnum, IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { IsUndefinable } from '@wisemen/validators'
import { Type } from 'class-transformer'
import { CertificateDocType, CertificateDocTypeApiProperty } from '../../enums/certificate-doc-type.enum.js'
import { DateRange } from '../../../../utils/types/date-filter-range.js'

export enum ViewCertificateIndexSortKey {
  SALES_ORDER = 'salesOrder',
  SALES_ORDER_LINE = 'salesOrderLine',
  COLLECTION_DATE = 'collectionDate',
  DELIVERY_DATE = 'deliveryDate',
  DISPOSITION_PICK_UP_DATE = 'dispositionPickUpDate',
  DISPOSITION_DELIVERY_DATE = 'dispositionDeliveryDate',
  TREATMENT_CENTRE = 'treatmentCentre',
  END_TREATMENT_CENTRE = 'endTreatmentCentre',
  EWC = 'ewc',
  WTF_FORM = 'wtfForm',
  TFS = 'tfs',
  DISPOSAL_DATE = 'disposalDate',
  PRINT_DATE = 'printDate',
  PICK_UP_ADDRESS_ID = 'pickUpAddress',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  CONTRACT_ITEM = 'contractItem',
  CONTRACT = 'contract',
  INVOICE = 'invoice'
}

export class ViewCertificateIndexSortQuery extends SortQuery {
  @ApiProperty({ enum: ViewCertificateIndexSortKey, enumName: 'ViewCertificateIndexSortKey' })
  @IsEnum(ViewCertificateIndexSortKey)
  key: ViewCertificateIndexSortKey

  @SortDirectionApiProperty()
  @IsEnum(SortDirection)
  order: SortDirection
}

export class ViewCertificateIndexFilterQuery extends FilterQuery {
  @CertificateDocTypeApiProperty({ required: false, isArray: true })
  @IsUndefinable()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsEnum(CertificateDocType, { each: true })
  docTypes?: CertificateDocType[]

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  salesOrder?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  salesOrderLine?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  description?: string

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  collectionDate?: DateRange

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  deliveryDate?: DateRange

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  dispositionPickUpDate?: DateRange

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  dispositionDeliveryDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  treatmentCentre?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  endTreatmentCentre?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  ewc?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  wtfForm?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  tfs?: string

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  disposalDate?: DateRange

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  printDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  wasteProducerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  contract?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  contractItem?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  invoice?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsNotEmpty()
  @IsString()
  pickUpAddressId?: string
}

export class ViewCertificateIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewCertificateIndexSortQuery, isArray: true })
  @IsUndefinable()
  @ArrayMinSize(1)
  @ArrayUnique()
  @Type(() => ViewCertificateIndexSortQuery)
  @ValidateNested({ each: true })
  sort?: ViewCertificateIndexSortQuery[]

  @ApiProperty({ type: ViewCertificateIndexFilterQuery, required: false })
  @IsUndefinable()
  @Type(() => ViewCertificateIndexFilterQuery)
  @ValidateNested()
  @IsObject()
  filter?: ViewCertificateIndexFilterQuery

  @Equals(undefined)
  search?: never
}
