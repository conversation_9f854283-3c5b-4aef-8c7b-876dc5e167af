import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { tc } from '../../localization/helpers/translate.helper.js'

export class CertificateFileNotFoundError extends NotFoundApiError {
  @ApiErrorCode('certificate_file_not_found')
  code = 'certificate_file_not_found'

  meta: never

  constructor () {
    super(tc('error.certificate.file_not_found'))
  }
}
