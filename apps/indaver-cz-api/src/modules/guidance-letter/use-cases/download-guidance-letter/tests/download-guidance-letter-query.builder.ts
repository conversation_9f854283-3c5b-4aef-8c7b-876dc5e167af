import { DownloadGuidanceLetterQuery } from '../download-guidance-letter.query.js'
import { DownloadGuidanceLetterType } from '../download-guidance-letter.enum.js'

export class DownloadGuidanceLetterQueryBuilder {
  private query: DownloadGuidanceLetterQuery

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.query = new DownloadGuidanceLetterQuery()

    this.query.type = DownloadGuidanceLetterType.PRINT

    return this
  }

  withType (type: DownloadGuidanceLetterType) {
    this.query.type = type

    return this
  }

  build (): DownloadGuidanceLetterQuery {
    const query = this.query

    this.reset()

    return query
  }
}
