import { before, describe, it, beforeEach } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DownloadGuidanceLetterSapUseCase } from '../download-guidance-letter.use-case.js'
import { SapGetGuidanceLetterIndexUseCase } from '../../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UserWasteProducerAuthService } from '../../../../auth/services/user-waste-producer-auth.service.js'
import { GuidanceLetterNotFoundError } from '../../../errors/guidance-letter-not-found.error.js'
import { GuidanceLetterDownloadTypeNotFoundError } from '../../../errors/guidance-letter-download-type-not-found.error.js'
import { SapGetGuidanceLetterIndexResponseBuilder } from '../../../../sap/use-cases/get-guidance-letter-index/tests/get-guidance-letter-index.response.builder.js'
import { DownloadGuidanceLetterQuery } from '../download-guidance-letter.query.js'
import { DownloadGuidanceLetterType } from '../download-guidance-letter.enum.js'
import { SapDownloadGuidanceLetterUseCase } from '../../../../sap/use-cases/download-guidance-letter/download-guidance-letter.use.case.js'
import { DownloadGuidanceLetterQueryBuilder } from './download-guidance-letter-query.builder.js'

describe('Download guidance letter unit tests', () => {
  let useCase: DownloadGuidanceLetterSapUseCase
  let shipmentId: string

  let downloadUseCase: SinonStubbedInstance<SapDownloadGuidanceLetterUseCase>
  let authContext: SinonStubbedInstance<AuthContext>
  let userWasteProducerAuthService: SinonStubbedInstance<UserWasteProducerAuthService>
  let sapGetGuidanceLetterIndex: SinonStubbedInstance<SapGetGuidanceLetterIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    shipmentId = randomUUID()

    downloadUseCase = createStubInstance(SapDownloadGuidanceLetterUseCase)
    authContext = createStubInstance(AuthContext)
    userWasteProducerAuthService = createStubInstance(UserWasteProducerAuthService)
    sapGetGuidanceLetterIndex = createStubInstance(SapGetGuidanceLetterIndexUseCase)

    useCase = new DownloadGuidanceLetterSapUseCase(
      authContext,
      userWasteProducerAuthService,
      sapGetGuidanceLetterIndex,
      downloadUseCase
    )
  })

  beforeEach(() => {
    authContext.getSelectedCustomerId.returns(randomUUID())
    authContext.getAzureEntraUpn.returns(randomUUID())
    userWasteProducerAuthService.getRestrictedWasteProducerIds.resolves(undefined)
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYdgContent('attachment-content')
        .withPdfYbegContent('print-content')
        .build()
    ])
  })

  it('throws GuidanceLetterNotFoundError when no guidance letter found', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([])

    const query = new DownloadGuidanceLetterQueryBuilder()
      .withType(DownloadGuidanceLetterType.PRINT)
      .build()

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterNotFoundError)
  })

  it('throws GuidanceLetterDownloadTypeNotFoundError when print content is empty', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYbegContent('')
        .build()
    ])

    const query = new DownloadGuidanceLetterQueryBuilder()
      .withType(DownloadGuidanceLetterType.PRINT)
      .build()

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterDownloadTypeNotFoundError)
  })

  it('throws GuidanceLetterDownloadTypeNotFoundError when print content is null', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYbegContent(null!)
        .build()
    ])

    const query = new DownloadGuidanceLetterQueryBuilder()
      .withType(DownloadGuidanceLetterType.PRINT)
      .build()

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterDownloadTypeNotFoundError)
  })

  it('throws GuidanceLetterDownloadTypeNotFoundError when preview content is empty', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYbegContent('')
        .build()
    ])

    const query = new DownloadGuidanceLetterQuery()
    query.type = DownloadGuidanceLetterType.PREVIEW

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterDownloadTypeNotFoundError)
  })

  it('throws GuidanceLetterDownloadTypeNotFoundError when attachment content is empty', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYdgContent('')
        .build()
    ])

    const query = new DownloadGuidanceLetterQuery()
    query.type = DownloadGuidanceLetterType.ATTACHMENT

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterDownloadTypeNotFoundError)
  })

  it('throws GuidanceLetterDownloadTypeNotFoundError when attachment content is null', async () => {
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYdgContent(null!)
        .build()
    ])

    const query = new DownloadGuidanceLetterQuery()
    query.type = DownloadGuidanceLetterType.ATTACHMENT

    await expect(useCase.execute(shipmentId, query))
      .rejects
      .toThrow(GuidanceLetterDownloadTypeNotFoundError)
  })

  it('returns a buffer', async () => {
    downloadUseCase.execute.resolves(Buffer.from('123'))

    const attachmentContent = 'attachment-content-url'
    sapGetGuidanceLetterIndex.execute.resolves([
      new SapGetGuidanceLetterIndexResponseBuilder()
        .withTorId(shipmentId)
        .withPdfYdgContent(attachmentContent)
        .build()
    ])

    const query = new DownloadGuidanceLetterQuery()
    query.type = DownloadGuidanceLetterType.ATTACHMENT

    const buffer = await useCase.execute(shipmentId, query)

    expect(Buffer.isBuffer(buffer)).toBe(true)
  })
})
