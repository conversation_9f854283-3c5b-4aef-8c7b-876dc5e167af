import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { randUrl } from '@ngneat/falso'
import { stringify } from 'qs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { SapGetGuidanceLetterIndexUseCase } from '../../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { DownloadGuidanceLetterType } from '../download-guidance-letter.enum.js'
import { SapGetGuidanceLetterIndexResponseBuilder } from '../../../../sap/use-cases/get-guidance-letter-index/tests/get-guidance-letter-index.response.builder.js'
import { DownloadGuidanceLetterQueryBuilder } from './download-guidance-letter-query.builder.js'

describe('Download guidance letter e2e tests', () => {
  let setup: EndToEndTestSetup

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      setup.authContext.getUser([]),
      setup.authContext.getUser([Permission.GUIDANCE_LETTER_READ])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/guidance-letters/${randomUUID()}/download`)
      .query(stringify({ type: DownloadGuidanceLetterType.PRINT }))

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .get(`/guidance-letters/${randomUUID()}/download`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)
      .query(stringify({ type: DownloadGuidanceLetterType.PRINT }))

    expect(response).toHaveStatus(403)
  })

  it('returns 404 when guidance letter not found', async () => {
    mock.method(SapGetGuidanceLetterIndexUseCase.prototype, 'execute', () => {
      return []
    })

    const response = await request(setup.httpServer)
      .get(`/guidance-letters/${randomUUID()}/download`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .query(stringify({ type: DownloadGuidanceLetterType.PRINT }))

    expect(response).toHaveStatus(HttpStatus.NOT_FOUND)
  })

  it('downloads the waste inquiry document', async () => {
    const shipmentId = randomUUID()

    const query = new DownloadGuidanceLetterQueryBuilder().build()

    mock.method(SapGetGuidanceLetterIndexUseCase.prototype, 'execute', () => {
      return [
        new SapGetGuidanceLetterIndexResponseBuilder()
          .withTorId(shipmentId)
          .withPdfYbegContent(randUrl())
          .build()
      ]
    })

    const response = await request(setup.httpServer)
      .get(`/guidance-letters/${shipmentId}/download`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .query(stringify(query))

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
