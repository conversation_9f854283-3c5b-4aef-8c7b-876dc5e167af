import { ApiProperty } from '@nestjs/swagger'
import { IsEnum } from 'class-validator'
import { FilterQuery } from '@wisemen/pagination'
import { DownloadGuidanceLetterType } from './download-guidance-letter.enum.js'

export class DownloadGuidanceLetterQuery extends FilterQuery {
  @ApiProperty({ type: String, enum: DownloadGuidanceLetterType, enumName: 'DownloadGuidanceLetterType' })
  @IsEnum(DownloadGuidanceLetterType)
  type: DownloadGuidanceLetterType
}
