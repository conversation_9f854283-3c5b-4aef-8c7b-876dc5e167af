import { <PERSON>, Get, Param, Query, Res } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import type { Response } from 'express'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GuidanceLetterDownloadTypeNotFoundError } from '../../errors/guidance-letter-download-type-not-found.error.js'
import { GuidanceLetterNotFoundError } from '../../errors/guidance-letter-not-found.error.js'
import { streamFileResponse } from '../../../../utils/streams/stream-file-response.js'
import { MimeType } from '../../../files/enums/mime-type.enum.js'
import { DownloadGuidanceLetterSapUseCase } from './download-guidance-letter.use-case.js'
import { DownloadGuidanceLetterQuery } from './download-guidance-letter.query.js'

@ApiTags('Guidance Letter')
@ApiOAuth2([])
@Controller('guidance-letters/:shipmentId/download')
export class DownloadGuidanceLetterSapController {
  constructor (
    private readonly useCase: DownloadGuidanceLetterSapUseCase
  ) {}

  @Get()
  @Permissions(Permission.GUIDANCE_LETTER_READ)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(
    GuidanceLetterDownloadTypeNotFoundError,
    GuidanceLetterNotFoundError
  )
  public async downloadGuidanceLetterSap (
    @Param('shipmentId') shipmentId: string,
    @Query() query: DownloadGuidanceLetterQuery,
    @Res() res: Response
  ): Promise<void> {
    const buffer = await this.useCase.execute(
      shipmentId,
      query
    )

    streamFileResponse(buffer, MimeType.PDF, `${shipmentId}.pdf`, res)
  }
}
