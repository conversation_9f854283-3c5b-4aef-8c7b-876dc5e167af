import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import dayjs from 'dayjs'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapGetGuidanceLetterIndexResponse } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.response.js'
import { SapGetGuidanceLetterIndexUseCase } from '../../../sap/use-cases/get-guidance-letter-index/get-guidance-letter-index.use-case.js'
import { UserWasteProducerAuthService } from '../../../auth/services/user-waste-producer-auth.service.js'
import { FilterOperator } from '../../../sap/enums/odata-filter-operator.enum.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { ViewGuidanceLetterIndexQuery } from './query/view-guidance-letter-index.query.js'
import { ViewGuidanceLetterIndexResponse } from './view-guidance-letter-index.response.js'
import { MapGuidanceLetterSapService } from './map-quidance-letter-index.mapper.js'
import { ViewGuidanceLetterIndexSortQueryKey } from './query/view-guidance-letter-index.sort-query.js'

@Injectable()
export class ViewGuidanceLetterIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly userWasteProducerAuthService: UserWasteProducerAuthService,
    private readonly sapGetGuidanceLetterIndex: SapGetGuidanceLetterIndexUseCase
  ) {}

  public async execute (
    query: ViewGuidanceLetterIndexQuery
  ): Promise<ViewGuidanceLetterIndexResponse> {
    const sapQuery = await this.getSapQuery(query)
    const sapResult = await this.sapGetGuidanceLetterIndex.execute(sapQuery)

    const guidanceLetters = MapGuidanceLetterSapService.mapResultsToGuidanceLetters(sapResult)

    const pagination = typeormPagination(query.pagination)
    return new ViewGuidanceLetterIndexResponse(
      guidanceLetters,
      null,
      pagination.take,
      pagination.skip
    )
  }

  private async getSapQuery (
    query: ViewGuidanceLetterIndexQuery
  ): Promise<SapQuery<SapGetGuidanceLetterIndexResponse>> {
    const sapQuery = new SapQuery<SapGetGuidanceLetterIndexResponse>(
      query,
      {
        defaultOrderBy: {
          column: 'TorId',
          direction: 'desc'
        },
        keyMapper: (key: string) =>
          this.mapSortKeyToSapKey(key as ViewGuidanceLetterIndexSortQueryKey)
      }
    )
      .addSelect([
        'TorId',
        'PdfYbeg',
        'PdfYdg',
        'Reqno',
        'Vbeln',
        'Arktx',
        'KunnrWe',
        'ZcsKwmeng',
        'Vrkme',
        'Vdatu',
        'Kunnr',
        'KunnrY2',
        'KunnrWe',
        'KunnrDisplayName',
        'KunnrWeDisplayName',
        'KunnrY2DisplayName',
        'PdfYdgContent',
        'PdfYbegContent'
      ])

    // Add filters based on selected customer and accessible waste producers
    const selectedCustomerId = this.authContext.getSelectedCustomerId() ?? query.filter?.customerId
    if (selectedCustomerId != null) {
      sapQuery.where('Kunnr', selectedCustomerId)

      const restrictedWasteProducerIds = await this.userWasteProducerAuthService
        .getRestrictedWasteProducerIds(
          this.authContext.getAzureEntraUpn(),
          selectedCustomerId
        )
      if (restrictedWasteProducerIds !== undefined && restrictedWasteProducerIds.length > 0) {
        sapQuery.andWhere((qb) => {
          for (let i = 0; i < restrictedWasteProducerIds.length; i++) {
            if (i === 0) {
              qb.where('KunnrY2', restrictedWasteProducerIds[i])
            } else {
              qb.orWhere('KunnrY2', restrictedWasteProducerIds[i])
            }
          }
          return qb
        })
      }
    }

    this.applyFilters(query, sapQuery)

    return sapQuery
  }

  private applyFilters (
    query: ViewGuidanceLetterIndexQuery,
    sapQuery: SapQuery<SapGetGuidanceLetterIndexResponse>
  ): void {
    if (query.filter?.shipmentId !== undefined) {
      sapQuery.andWhere('TorId', query.filter.shipmentId)
    }

    if (query.filter?.requestNumber !== undefined) {
      sapQuery.andWhere('Reqno', query.filter.requestNumber)
    }

    if (query.filter?.salesDoc !== undefined) {
      sapQuery.andWhere('Vbeln', query.filter.salesDoc)
    }

    if (query.filter?.wasteMaterial !== undefined) {
      sapQuery.andWhere('Arktx', query.filter.wasteMaterial, FilterOperator.SUBSTRING_OF)
    }

    if (query.filter?.transportDate !== undefined) {
      const from = dayjs(query.filter?.transportDate.from).startOf('day').toDate()
      const to = dayjs(query.filter?.transportDate.to).add(1, 'day').startOf('day').toDate()
      sapQuery.andWhere((qb) => {
        return qb.andWhere('Vdatu', SapDateFormatterService.toSapFilterDate(from), FilterOperator.GREATER_THAN_OR_EQUAL)
          .andWhere('Vdatu', SapDateFormatterService.toSapFilterDate(to), FilterOperator.LESS_THAN)
      })
    }

    if (query.filter?.wasteProducerId !== undefined) {
      sapQuery.andWhere('KunnrY2', query.filter.wasteProducerId)
    }

    if (query.filter?.pickUpAddressId !== undefined) {
      sapQuery.andWhere('KunnrWe', query.filter?.pickUpAddressId)
    }
  }

  private mapSortKeyToSapKey (
    key: ViewGuidanceLetterIndexSortQueryKey
  ): keyof SapGetGuidanceLetterIndexResponse {
    const mapping: Record<
      ViewGuidanceLetterIndexSortQueryKey,
      keyof SapGetGuidanceLetterIndexResponse
    > = {
      [ViewGuidanceLetterIndexSortQueryKey.SHIPMENT_ID]: 'TorId',
      [ViewGuidanceLetterIndexSortQueryKey.SALES_DOC]: 'Vbeln',
      [ViewGuidanceLetterIndexSortQueryKey.REQUEST_NUMBER]: 'Reqno',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_MATERIAL]: 'Arktx',
      [ViewGuidanceLetterIndexSortQueryKey.TRANSPORT_DATE]: 'Vdatu',
      [ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_ID]: 'Kunnr',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_ID]: 'KunnrY2',
      [ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_ID]: 'KunnrWe',
      [ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_NAME]: 'KunnrDisplayName',
      [ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_NAME]: 'KunnrY2DisplayName',
      [ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_NAME]: 'KunnrWeDisplayName'
    }

    return mapping[key]
  }
}
