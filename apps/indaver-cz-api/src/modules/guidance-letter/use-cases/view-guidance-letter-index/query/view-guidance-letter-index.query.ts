import { ArrayUnique, Equals, <PERSON><PERSON><PERSON>y, IsObject, ValidateNested } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { IsUndefinable } from '@wisemen/validators'
import { ViewGuidanceLetterIndexSortQuery } from './view-guidance-letter-index.sort-query.js'
import { ViewGuidanceLetterIndexFilterQuery } from './view-guidance-letter-index.filter.query.js'

export class ViewGuidanceLetterIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewGuidanceLetterIndexSortQuery, required: false, isArray: true })
  @Type(() => ViewGuidanceLetterIndexSortQuery)
  @ValidateNested({ each: true })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  sort?: ViewGuidanceLetterIndexSortQuery[]

  @ApiProperty({ type: ViewGuidanceLetterIndexFilterQuery, required: false })
  @IsUndefinable()
  @IsObject()
  @Type(() => ViewGuidanceLetterIndexFilterQuery)
  @ValidateNested()
  filter?: ViewGuidanceLetterIndexFilterQuery

  @Equals(undefined)
  search?: never
}
