import { ApiProperty } from '@nestjs/swagger'
import { IsUndefinable } from '@wisemen/validators'
import { IsNotEmpty, IsObject, IsString, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { FilterQuery } from '@wisemen/pagination'
import { DateRange } from '../../../../../utils/types/date-filter-range.js'

export class ViewGuidanceLetterIndexFilterQuery extends FilterQuery {
  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  shipmentId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  requestNumber?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  salesDoc?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteMaterial?: string

  @ApiProperty({ type: DateRange, required: false })
  @IsUndefinable()
  @Type(() => DateRange)
  @ValidateNested()
  @IsObject()
  transportDate?: DateRange

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  customerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  wasteProducerId?: string

  @ApiProperty({ type: String, required: false })
  @IsUndefinable()
  @IsString()
  @IsNotEmpty()
  pickUpAddressId?: string
}
