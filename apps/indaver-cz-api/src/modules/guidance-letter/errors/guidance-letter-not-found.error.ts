import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class GuidanceLetterNotFoundErrorMeta {
  @ApiProperty({ type: String })
  id: string

  constructor (id: string) {
    this.id = id
  }
}

export class GuidanceLetterNotFoundError extends NotFoundApiError {
  @ApiErrorCode('guidance_letter_not_found')
  code = 'guidance_letter_not_found'

  @ApiErrorMeta()
  meta: GuidanceLetterNotFoundErrorMeta

  constructor (meta: GuidanceLetterNotFoundErrorMeta) {
    super(translateCurrent('error.guidance_letter.not_found'))
    this.meta = meta
  }
}
