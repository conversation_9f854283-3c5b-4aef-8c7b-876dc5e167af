import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { DownloadGuidanceLetterType } from '../use-cases/download-guidance-letter/download-guidance-letter.enum.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class GuidanceLetterDownloadTypeNotFoundErrorMeta {
  @ApiProperty({ type: String, enum: DownloadGuidanceLetterType, enumName: 'DownloadGuidanceLetterType' })
  type: DownloadGuidanceLetterType

  constructor (type: DownloadGuidanceLetterType) {
    this.type = type
  }
}

export class GuidanceLetterDownloadTypeNotFoundError extends NotFoundApiError {
  @ApiErrorCode('guidance_letter_download_type_not_found')
  code = 'guidance_letter_download_type_not_found'

  @ApiErrorMeta()
  meta: GuidanceLetterDownloadTypeNotFoundErrorMeta

  constructor (meta: GuidanceLetterDownloadTypeNotFoundErrorMeta) {
    super(translateCurrent('error.guidance_letter.download_type_not_found'))
    this.meta = meta
  }
}
