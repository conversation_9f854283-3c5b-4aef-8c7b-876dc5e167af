import { Injectable } from '@nestjs/common'
import { SapGetContainerTypeIndexResponse } from '../../../sap/use-cases/get-container-type-index/get-container-type-index.response.js'
import { MapContainerTypeSapService } from '../../services/map-container-type-sap.service.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { SapGetContainerTypeIndexUseCase } from '../../../sap/use-cases/get-container-type-index/get-container-type-index.use-case.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { mapLocaleToSapLocale } from '../../../localization/enums/locale.enum.js'
import { ViewContainerTypeIndexResponse } from './view-container-type-index.response.js'
import { ViewContainerTypeIndexQuery } from './query/view-container-type-index.query.js'
import { ViewContainerTypeIndexValidator } from './view-container-type-index.validator.js'

@Injectable()
export class ViewContainerTypeIndexUseCase {
  constructor (
    private readonly validator: ViewContainerTypeIndexValidator,
    private readonly getCustomerSalesOrganisationId: GetCustomerDefaultSalesOrganisationIdUseCase,
    private readonly sapGetContainerTypeIndex: SapGetContainerTypeIndexUseCase
  ) {}

  public async execute (
    query: ViewContainerTypeIndexQuery
  ): Promise<ViewContainerTypeIndexResponse> {
    await this.validator.execute(query)

    const customerDefaultSalesOrganisationId = await this.getCustomerSalesOrganisationId
      .getOrganisationIdOrFail(
        query.filter.customerId
      )

    const sapQuery = this.getSapQuery(query, customerDefaultSalesOrganisationId)
    const sapResponse = await this.sapGetContainerTypeIndex.execute(
      sapQuery
    )

    const containerTypes = MapContainerTypeSapService.mapResultsToContainerTypes(sapResponse)

    return new ViewContainerTypeIndexResponse(containerTypes)
  }

  private getSapQuery (
    query: ViewContainerTypeIndexQuery,
    customerDefaultSalesOrganisationId: string
  ): SapQuery<SapGetContainerTypeIndexResponse> {
    const sapQuery = new SapQuery<SapGetContainerTypeIndexResponse>(query)
      .addSelect(['containerType', 'description'])
      .where('language', mapLocaleToSapLocale(getCurrentLanguage()))
      .andWhere('salesOrganization', customerDefaultSalesOrganisationId)
      .addOrderBy('description', 'asc')

    return sapQuery
  }
}
