import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewContainerTypeIndexUseCase } from './view-container-type-index.use-case.js'
import { ViewContainerTypeIndexController } from './view-container-type-index.controller.js'
import { ViewContainerTypeIndexValidator } from './view-container-type-index.validator.js'

@Module({
  imports: [
    SapModule,
    CustomerModule
  ],
  controllers: [
    ViewContainerTypeIndexController
  ],
  providers: [
    ViewContainerTypeIndexUseCase,
    ViewContainerTypeIndexValidator
  ]
})
export class ViewContainerTypeIndexModule {}
