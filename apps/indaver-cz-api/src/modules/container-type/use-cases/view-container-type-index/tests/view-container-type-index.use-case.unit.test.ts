import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewContainerTypeIndexUseCase } from '../view-container-type-index.use-case.js'
import { MapContainerTypeSapService } from '../../../services/map-container-type-sap.service.js'
import { ContainerType } from '../../../types/container-type.js'
import { SapGetContainerTypeIndexUseCase } from '../../../../sap/use-cases/get-container-type-index/get-container-type-index.use-case.js'
import { GetContainerTypeIndexResponseBuilder } from '../../../../sap/use-cases/get-container-type-index/tests/get-container-type-index.response.builder.js'
import { ViewContainerTypeIndexQuery } from '../query/view-container-type-index.query.js'
import { ViewContainerTypeIndexValidator } from '../view-container-type-index.validator.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewContainerTypeIndexQueryBuilder } from './view-container-type-index.query.builder.js'

describe('View container type index use-case unit test', () => {
  let useCase: ViewContainerTypeIndexUseCase

  let query: ViewContainerTypeIndexQuery

  let validator: SinonStubbedInstance<ViewContainerTypeIndexValidator>
  let salesOrganisationUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>
  let sapGetContainerTypeIndex: SinonStubbedInstance<SapGetContainerTypeIndexUseCase>

  before(() => {
    TestBench.setupUnitTest()

    query = new ViewContainerTypeIndexQueryBuilder().build()

    validator = createStubInstance(ViewContainerTypeIndexValidator)
    salesOrganisationUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)
    sapGetContainerTypeIndex = createStubInstance(SapGetContainerTypeIndexUseCase)

    useCase = new ViewContainerTypeIndexUseCase(
      validator,
      salesOrganisationUseCase,
      sapGetContainerTypeIndex
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.execute.resolves()
    salesOrganisationUseCase.getOrganisationIdOrFail.resolves(randomUUID())
    sapGetContainerTypeIndex.execute.resolves([
      new GetContainerTypeIndexResponseBuilder().build()
    ])
  }

  it('Returns parsed result from SAP', async () => {
    const containerTypes: ContainerType[] = [{
      id: '1',
      name: 'Ro-Ro open container'
    }, {
      id: '2',
      name: 'Ro-Ro closed container'
    }]

    sapGetContainerTypeIndex.execute.resolves(
      MapContainerTypeSapService.mapContainerTypesToResults(containerTypes)
    )

    const result = await useCase.execute(query)

    expect(result.items).toStrictEqual(expect.arrayContaining(containerTypes))
  })
})
