import { randomUUID } from 'crypto'
import { ViewContainerTypeIndexQuery } from '../query/view-container-type-index.query.js'
import { ViewContainerTypeIndexFilterQuery } from '../query/view-container-type-index.filter.query.js'

export class ViewContainerTypeIndexQueryBuilder {
  private query: ViewContainerTypeIndexQuery

  constructor () {
    this.query = new ViewContainerTypeIndexQuery()
    this.query.filter = new ViewContainerTypeIndexFilterQuery()
    this.query.filter.customerId = randomUUID()
  }

  withFilter (filter: ViewContainerTypeIndexFilterQuery): this {
    this.query.filter = filter

    return this
  }

  build (): ViewContainerTypeIndexQuery {
    return this.query
  }
}
