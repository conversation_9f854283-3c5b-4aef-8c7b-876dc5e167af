import { Controller, Get, Query } from '@nestjs/common'
import { <PERSON>piT<PERSON><PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewContainerTypeIndexUseCase } from './view-container-type-index.use-case.js'
import { ViewContainerTypeIndexResponse } from './view-container-type-index.response.js'
import { ViewContainerTypeIndexQuery } from './query/view-container-type-index.query.js'

@ApiTags('Container type')
@ApiOAuth2([])
@Controller('container-types')
export class ViewContainerTypeIndexController {
  constructor (
    private readonly useCase: ViewContainerTypeIndexUseCase
  ) { }

  @Get()
  @Permissions(
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ
  )
  @ApiOkResponse({ type: ViewContainerTypeIndexResponse })
  public async viewContainerTypeIndex (
    @Query() query: ViewContainerTypeIndexQuery
  ): Promise<ViewContainerTypeIndexResponse> {
    return await this.useCase.execute(query)
  }
}
