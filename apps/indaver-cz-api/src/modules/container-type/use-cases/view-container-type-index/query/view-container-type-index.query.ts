import { ApiProperty } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { Equals, IsNotEmpty, IsObject, IsOptional, ValidateNested } from 'class-validator'
import { ViewContainerTypeIndexFilterQuery } from './view-container-type-index.filter.query.js'

export class ViewContainerTypeIndexQuery {
  @Equals(undefined)
  sort?: never

  @ApiProperty({ type: ViewContainerTypeIndexFilterQuery })
  @Type(() => ViewContainerTypeIndexFilterQuery)
  @ValidateNested()
  @IsNotEmpty()
  @IsObject()
  filter: ViewContainerTypeIndexFilterQuery

  @ApiProperty({ type: String, required: false })
  @IsOptional()
  search?: string
}
