import { ApiProperty } from '@nestjs/swagger'
import { ContainerType } from '../../types/container-type.js'

class ContainerTypeResponse {
  @ApiProperty({ type: String })
  id: string

  @ApiProperty({ type: String })
  name: string

  constructor (containerType: ContainerType) {
    this.id = containerType.id
    this.name = containerType.name
  }
}

export class ViewContainerTypeIndexResponse {
  @ApiProperty({ type: ContainerTypeResponse, isArray: true })
  items: ContainerTypeResponse[]

  constructor (items: ContainerType[]) {
    this.items = items.map(containerType => new ContainerTypeResponse(containerType))
  }
}
