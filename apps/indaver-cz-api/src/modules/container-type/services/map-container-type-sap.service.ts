import { randomUUID } from 'crypto'
import { SAP_ENDPOINTS } from '../../sap/constants/endpoint.constants.js'
import { SapGetContainerTypeIndexResponse } from '../../sap/use-cases/get-container-type-index/get-container-type-index.response.js'
import { ContainerType } from '../types/container-type.js'

export class MapContainerTypeSapService {
  static mapResultToContainerType (response: SapGetContainerTypeIndexResponse): ContainerType {
    return {
      id: response.containerType,
      name: response.description
    }
  }

  static mapResultsToContainerTypes (
    responses: SapGetContainerTypeIndexResponse[] = []
  ): ContainerType[] {
    return responses.map(response => this.mapResultToContainerType(response))
  }

  static mapContainerTypeToResult (containerType: ContainerType): SapGetContainerTypeIndexResponse {
    const id = randomUUID()

    return {
      __metadata: {
        id,
        uri: `https://services.odata.org/OData${SAP_ENDPOINTS.CONTAINER_TYPE.INDEX}/${id}`,
        type: 'ZMDX_TAOF_SRV.un'
      },
      salesOrganization: '',
      containerType: containerType.id,
      language: '',
      description: containerType.name
    }
  }

  static mapContainerTypesToResults (
    containerTypes: ContainerType[]
  ): SapGetContainerTypeIndexResponse[] {
    return containerTypes.map(containerType => this.mapContainerTypeToResult(containerType))
  }
}
