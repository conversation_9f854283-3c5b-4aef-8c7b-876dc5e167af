import { DynamicTableViewFilterCommand } from '../commands/dynamic-table-view-filter.command.js'
import { DynamicTableViewSortCommand } from '../commands/dynamic-table-view-sort.command.js'
import { DynamicTableViewVisibleColumnsCommand } from '../commands/dynamic-table-view-visible-columns.command.js'
import { DynamicTableColumn } from '../entities/dynamic-table-column.entity.js'
import { ColumnNotFilterableError } from '../errors/column-not-filterable.error.js'
import { ColumnNotFoundError } from '../errors/column-not-found.error.js'
import { ColumnNotSortableError } from '../errors/column-not-sortable.error.js'
import { DuplicateColumnError } from '../errors/duplicate-column.error.js'
import { FilterConfiguration, SortConfiguration, VisibilityConfiguration } from './dynamic-table-view-configuration.js'

export class CreateTableViewConfiguration {
  static createFilterConfigurations (
    filters: DynamicTableViewFilterCommand[],
    columns: DynamicTableColumn[]
  ): FilterConfiguration[] {
    const configurations = filters.map((filter, index) => {
      const column = columns.find(column => column.uuid === filter.columnUuid)

      if (column === undefined) {
        throw new ColumnNotFoundError({ pointer: `$.filter[${index}]` })
      }

      if (column.filterableField === null) {
        throw new ColumnNotFilterableError({ pointer: `$.filter[${index}]` })
      }

      return {
        uuid: column.uuid,
        value: filter.value
      }
    })

    return configurations
  }

  static createSortConfigurations (
    sorts: DynamicTableViewSortCommand[],
    columns: DynamicTableColumn[]
  ): SortConfiguration[] {
    const configurations = sorts.map((sort, index) => {
      const column = columns.find(column => column.uuid === sort.columnUuid)

      if (column === undefined) {
        throw new ColumnNotFoundError({ pointer: `$.sorts[${index}]` })
      }

      if (column.sortableFields.length === 0) {
        throw new ColumnNotSortableError({ pointer: `$.sorts[${index}]` })
      }

      return {
        uuid: column.uuid,
        order: index,
        direction: sort.direction
      }
    })

    return configurations
  }

  static createVisibleColumnsConfigurations (
    visibleColumns: DynamicTableViewVisibleColumnsCommand[],
    columns: DynamicTableColumn[]
  ): VisibilityConfiguration[] {
    const seenColumnUuids = new Set<string>()

    const configurations = visibleColumns.map((visibleColumn, index) => {
      const column = columns.find(column => column.uuid === visibleColumn.columnUuid)

      if (column === undefined) {
        throw new ColumnNotFoundError({ pointer: `$.visibleColumns[${index}]` })
      }

      if (seenColumnUuids.has(column.uuid)) {
        throw new DuplicateColumnError({ pointer: `$.visibleColumns[${index}]` })
      }

      seenColumnUuids.add(column.uuid)

      return {
        uuid: column.uuid,
        order: index
      }
    })

    return configurations
  }
}
