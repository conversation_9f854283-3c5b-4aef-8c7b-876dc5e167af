import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DynamicTable } from './entities/dynamic-table.entity.js'
import { DynamicTableColumn } from './entities/dynamic-table-column.entity.js'
import { ViewDynamicTableColumnIndexModule } from './use-cases/view-dynamic-table-columns-index/view-dynamic-table-columns-index.module.js'
import { CreateDynamicTableViewModule } from './use-cases/create-dynamic-table-view/create-dynamic-table-view.module.js'
import { DynamicTableView } from './entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from './entities/user-default-dynamic-table-view.entity.js'
import { ViewDefaultDynamicTableViewModule } from './use-cases/view-default-dynamic-table-view/view-default-dynamic-table-view.module.js'
import { UpdateDynamicTableViewModule } from './use-cases/update-dynamic-table-view/update-dynamic-table.module.js'
import { ViewDynamicTableViewIndexModule } from './use-cases/view-dynamic-table-view-index/view-dynamic-table-view-index.module.js'
import { DeleteDynamicTableViewModule } from './use-cases/delete-dynamic-table-view/delete-dynamic-table-view.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DynamicTable,
      DynamicTableColumn,
      DynamicTableView,
      UserDefaultDynamicTableView
    ]),
    ViewDynamicTableColumnIndexModule,
    CreateDynamicTableViewModule,
    ViewDefaultDynamicTableViewModule,
    UpdateDynamicTableViewModule,
    ViewDynamicTableViewIndexModule,
    DeleteDynamicTableViewModule
  ]
})
export class DynamicTableModule {}
