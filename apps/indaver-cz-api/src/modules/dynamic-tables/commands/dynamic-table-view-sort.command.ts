import { ApiProperty } from '@nestjs/swagger'
import { SortDirection } from '@wisemen/pagination'
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator'

export class DynamicTableViewSortCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  columnUuid: string

  @ApiProperty({ enum: SortDirection, enumName: 'SortDirection' })
  @IsEnum(SortDirection)
  @IsNotEmpty()
  direction: SortDirection
}
