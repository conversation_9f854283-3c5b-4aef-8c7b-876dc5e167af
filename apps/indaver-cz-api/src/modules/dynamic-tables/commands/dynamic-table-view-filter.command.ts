import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsUUID } from 'class-validator'
import { FilterWithKeyValue } from '../types/filter-with-key-value.type.js'

export class DynamicTableViewFilterCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  columnUuid: string

  @ApiProperty({
    oneOf: [
      { type: 'string' },
      { type: 'array', items: { type: 'string' } },
      { type: 'boolean' },
      { type: 'object', properties: { key: { type: 'string' }, value: { type: 'string' } } },
      { type: 'array', items: { type: 'object', properties: { key: { type: 'string' }, value: { type: 'string' } } } }
    ],
    required: true
  })
  @IsNotEmpty()
  value: string | string[] | boolean | FilterWithKeyValue | FilterWithKeyValue[]
}
