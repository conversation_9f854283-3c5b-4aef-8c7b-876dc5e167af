export enum PickUpRequestColumnName {
  REQUEST_NUMBER = 'requestNumber',
  STATUS = 'status',
  WASTE_MATERIAL = 'wasteMaterial',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  CUSTOMER_REFERENCE = 'customerReference',
  CONTRACT_NUMBER = 'contractNumber',
  CONTRACT_ITEM = 'contractItem',
  TRANSPORT_MODE = 'transportMode',
  DATE_OF_REQUEST = 'dateOfRequest',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  ACCOUNT_MANAGER = 'accountManager',
  COST_CENTER = 'costCenter',
  IS_TRANSPORT_BY_INDAVER = 'isTransportByIndaver',
  REQUESTED_START_DATE = 'requestedStartDate',
  REQUESTED_END_DATE = 'requestedEndDate',
  CONFIRMED_TRANSPORT_DATE = 'confirmedTransportDate',
  SALES_ORDER = 'salesOrder',
  IS_HAZARDOUS = 'isHazardous',
  NAME_OF_APPLICANT = 'nameOfApplicant',
  ORDER_NUMBER = 'orderNumber',
  CONTAINER_NUMBER = 'containerNumber',
  MATERIAL_ANALYSIS = 'materialAnalysis',
  DELIVERY_INFO = 'deliveryInfo',
  NAME_INSTALLATION = 'nameInstallation',
  DISPOSAL_CERTIFICATE_NUMBER = 'disposalCertificateNumber',
  EWC = 'ewc',
  TFS_NUMBER = 'tfsNumber'
}
