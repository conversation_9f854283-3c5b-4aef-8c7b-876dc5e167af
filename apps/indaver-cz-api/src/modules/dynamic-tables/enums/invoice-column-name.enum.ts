import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'

export enum InvoiceColumnName {
  INVOICE_NUMBER = 'invoiceNumber',
  STATUS = 'status',
  ISSUED_ON = 'issuedOn',
  CUSTOMER_NAME = 'customerName',
  DUE_ON = 'dueOn',
  CUSTOMER_REFERENCE = 'customerReference',
  TYPE = 'type',
  PAYER_ID = 'payerId',
  PAYER_NAME = 'payerName',
  NET_AMOUNT = 'netAmount',
  VAT_AMOUNT = 'vatAmount',
  CURRENCY = 'currency',
  ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
  ACCOUNT_MANAGER_NAME = 'accountManagerName',
  COMPANY_NAME = 'companyName'
}

export function InvoiceColumnNameApiProperty (options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enum: InvoiceColumnName,
    enumName: 'InvoiceColumnName'
  })
}
