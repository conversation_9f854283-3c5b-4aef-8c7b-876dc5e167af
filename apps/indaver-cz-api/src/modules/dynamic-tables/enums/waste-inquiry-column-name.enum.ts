export enum WasteInquiryColumnName {
  INQUIRY_NUMBER = 'inquiryNumber',
  WASTE_STREAM_NAME = 'wasteStreamName',
  DATE = 'date',
  CONTRACT_ID = 'contractId',
  CONTRACT_ITEM = 'contractItem',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  SALES_ORGANISATION_ID = 'salesOrganisationId',
  SALES_ORGANISATION_NAME = 'salesOrganisationName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  REQUESTOR_NAME = 'requestorName',
  STATUS = 'status',
  EWC_CODE = 'ewcCode',
  CONFORMITY_CHECK = 'conformityCheck'
}
