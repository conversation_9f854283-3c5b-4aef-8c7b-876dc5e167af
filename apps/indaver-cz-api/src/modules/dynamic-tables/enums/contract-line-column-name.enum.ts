export enum ContractLineColumnName {
  CONTRACT_NUMBER = 'contractNumber',
  CONTRACT_ITEM = 'contractItem',
  CUSTOMER_REFERENCE = 'customerReference',
  WASTE_MATERIAL = 'wasteMaterial',
  MATERIAL_NUMBER = 'materialNumber',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  INSTALLATION_NAME = 'installationName',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  ASN = 'asn',
  TFS = 'tfs',
  IS_HAZARDOUS = 'isHazardous',
  PACKAGED = 'packaged',
  TC_NUMBER = 'tcNumber',
  MATERIAL_ANALYSIS = 'materialAnalysis',
  EWC_CODE = 'ewcCode',
  END_TREATMENT_CENTER_ID = 'endTreatmentCenterId',
  END_TREATMENT_CENTER_NAME = 'endTreatmentCenterName',
  REMARKS = 'remarks',
  PROCESS_CODE = 'processCode',
  ESN_NUMBER = 'esnNumber',
  DELIVERY_INFO = 'deliveryInfo',
  MATERIAL_TYPE = 'materialType',
  PACKAGING_INDICATOR = 'packagingIndicator'
}
