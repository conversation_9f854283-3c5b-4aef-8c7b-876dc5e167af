export enum DraftInvoiceColumnName {
  INVOICE_NUMBER = 'invoiceNumber',
  STATUS = 'status',
  PAYER_ID = 'payerId',
  PAYER_NAME = 'payerName',
  ISSUED_ON = 'issuedOn',
  FIRST_REMINDER_MAIL_STATUS = 'firstReminderMailStatus',
  FIRST_REMINDER_ON = 'firstReminderOn',
  SECOND_REMINDER_MAIL_STATUS = 'secondReminderMailStatus',
  SECOND_REMINDER_ON = 'secondReminderOn',
  THIRD_REMINDER_MAIL_STATUS = 'thirdReminderMailStatus',
  AUTO_APPROVED_ON = 'autoApprovedOn',
  NET_AMOUNT = 'netAmount',
  VAT_AMOUNT = 'vatAmount',
  CURRENCY = 'currency',
  CUSTOMER_APPROVAL_BY = 'customerApprovalBy',
  CUSTOMER_APPROVAL_DATE = 'customerApprovalDate',
  PO_NUMBER = 'poNumber',
  ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
  CUSTOMER_ID = 'customerId'
}
