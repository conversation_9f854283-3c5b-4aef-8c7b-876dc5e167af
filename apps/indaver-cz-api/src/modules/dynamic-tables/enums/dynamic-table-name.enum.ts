export enum DynamicTableName {
  CERTIFICATE = 'certificate',
  CONTRACT_LINE = 'contract-line', // Used in pick-up request and weekly planning request to show available contract lines
  CONTRACT_OVERVIEW = 'contract-overview', // Used in contract overview to show all contract lines of a contract
  DRAFT_INVOICE = 'draft-invoice',
  FORM_PICK_UP_REQUEST_CONTRACT_LINE_PACKAGED = 'form-pick-up-request-contract-line-packaged', // Used in pick-up request form to show selected contract lines for transport mode packaged curtain sider truck
  FORM_PICK_UP_REQUEST_CONTRACT_LINE_BULK_SKIPS = 'form-pick-up-request-contract-line-bulk-skips', // Used in pick-up request form to show selected contract lines for transport mode bulk skips/container
  FORM_PICK_UP_REQUEST_CONTRACT_LINE_BULK_VACUUM = 'form-pick-up-request-contract-line-bulk-vacuum', // Used in pick-up request form to show selected contract lines for transport mode bulk vacuum tankers/road tankers
  FORM_PICK_UP_REQUEST_CONTRACT_LINE_BULK_ISO_TANK = 'form-pick-up-request-contract-line-bulk-iso-tank', // Used in pick-up request form to show selected contract lines for transport mode bulk iso tank
  INVOICE = 'invoice',
  PICK_UP_REQUEST = 'pick-up-request',
  WASTE_INQUIRY = 'waste-inquiry',
  GUIDANCE_LETTER = 'guidance-letter'
}
