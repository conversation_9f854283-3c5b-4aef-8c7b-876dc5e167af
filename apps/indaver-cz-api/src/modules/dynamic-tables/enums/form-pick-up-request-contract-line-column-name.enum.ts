export enum FormPickUpRequestContractLineColumnName {
  ESTIMATED_WEIGHT_OR_VOLUME_VALUE = 'estimatedWeightOrVolumeValue',
  ESTIMATED_WEIGHT_OR_VOLUME_UNIT = 'estimatedWeightOrVolumeUnit',
  PACKAGING_TYPE = 'packagingType',
  QUANTITY_PACKAGES = 'quantityPackages',
  QUANTITY_LABELS = 'quantityLabels',
  QUANTITY_PALLETS = 'quantityPallets',
  UN_NUMBER = 'unNumber',
  PACKING_GROUP = 'packingGroup',
  DANGER_LABEL1 = 'dangerLabel1',
  DANGER_LABEL2 = 'dangerLabel2',
  DANGER_LABEL3 = 'dangerLabel3',
  COST_CENTER = 'costCenter',
  PO_NUMBER = 'poNumber',
  CONTAINER_TYPE = 'containerType',
  CONTAINER_VOLUME_SIZE = 'containerVolumeSize',
  CONTAINER_NUMBER = 'containerNumber',
  CONTAINER_TRANSPORT_TYPE = 'containerTransportType',
  IS_CONTAINER_COVERED = 'isContainerCovered',
  TANKER_TYPE = 'tankerType',
  TOTAL_QUANTITY_PALLETS = 'totalQuantityPallets',
  IS_RETURN_PACKAGING = 'isReturnPackaging',
  PACKAGING_REMARK = 'packagingRemark',
  RECONCILIATION_NUMBER = 'reconciliationNumber',
  HAZARD_INDUCERS = 'hazardInducers',
  QUANTITY_CONTAINERS = 'quantityContainers',
  TFS_NUMBER = 'tfsNumber',
  SERIAL_NUMBER = 'serialNumber'
}
