{"columns": [{"name": "invoiceNumber", "applicableFields": ["invoiceNumber"], "searchableFields": [], "filterableField": "invoiceNumber", "sortableFields": [], "isHidable": true}, {"name": "status", "applicableFields": ["status"], "searchableFields": [], "filterableField": "statuses", "sortableFields": [], "isHidable": true}, {"name": "payerId", "applicableFields": ["payerId"], "searchableFields": [], "filterableField": "payerId", "sortableFields": [], "isHidable": true}, {"name": "payerName", "applicableFields": ["payerName"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "issuedOn", "applicableFields": ["issuedOn"], "searchableFields": [], "filterableField": "issuedOn", "sortableFields": [], "isHidable": true}, {"name": "firstReminderMailStatus", "applicableFields": ["firstReminderMailStatus"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "firstReminderOn", "applicableFields": ["firstReminderOn"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "secondReminderMailStatus", "applicableFields": ["secondReminderMailStatus"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "secondReminderOn", "applicableFields": ["secondReminderOn"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "thirdReminderMailStatus", "applicableFields": ["thirdReminderMailStatus"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "autoApprovedOn", "applicableFields": ["autoApprovedOn"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "netAmount", "applicableFields": ["netAmount"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "vatAmount", "applicableFields": ["vatAmount"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "currency", "applicableFields": ["currency"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "customerApprovalBy", "applicableFields": ["customerApprovalBy"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "customerApprovalDate", "applicableFields": ["customerApprovalDate"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "poNumber", "applicableFields": ["poNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "accountDocumentNumber", "applicableFields": ["accountDocumentNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": "customerId", "sortableFields": [], "isHidable": true}]}