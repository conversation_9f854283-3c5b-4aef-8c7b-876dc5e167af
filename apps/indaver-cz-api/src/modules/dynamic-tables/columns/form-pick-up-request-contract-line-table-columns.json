{"columns": [{"name": "estimatedWeightOrVolumeValue", "applicableFields": ["estimatedWeightOrVolumeValue"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "estimatedWeightOrVolumeUnit", "applicableFields": ["estimatedWeightOrVolumeUnit"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "packagingType", "applicableFields": ["packagingType"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "quantityPackages", "applicableFields": ["quantityPackages"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "quantityLabels", "applicableFields": ["quantityLabels"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "quantityPallets", "applicableFields": ["quantityPallets"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "unNumber", "applicableFields": ["unNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "packingGroup", "applicableFields": ["packingGroup"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "dangerLabel1", "applicableFields": ["dangerLabel1"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "dangerLabel2", "applicableFields": ["dangerLabel2"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "dangerLabel3", "applicableFields": ["dangerLabel3"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": false}, {"name": "costCenter", "applicableFields": ["costCenter"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "poNumber", "applicableFields": ["poNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "containerType", "applicableFields": ["containerType"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "containerVolumeSize", "applicableFields": ["containerVolumeSize"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "containerNumber", "applicableFields": ["containerNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "containerTransportType", "applicableFields": ["containerTransportType"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "isContainerCovered", "applicableFields": ["isContainerCovered"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "tankerType", "applicableFields": ["tankerType"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "reconciliationNumber", "applicableFields": ["reconciliationNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "hazardInducers", "applicableFields": ["hazardInducers"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "quantityContainers", "applicableFields": ["quantityContainers"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "tfsNumber", "applicableFields": ["tfsNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "serialNumber", "applicableFields": ["serialNumber"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}]}