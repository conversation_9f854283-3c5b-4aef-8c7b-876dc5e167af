{"columns": [{"name": "docType", "applicableFields": ["docType"], "searchableFields": [], "filterableField": "docType", "sortableFields": [], "isHidable": true}, {"name": "salesOrder", "applicableFields": ["salesOrder"], "searchableFields": [], "filterableField": "salesOrder", "sortableFields": ["salesOrder"], "isHidable": true}, {"name": "salesOrderLine", "applicableFields": ["salesOrderLine"], "searchableFields": [], "filterableField": "salesOrderLine", "sortableFields": ["salesOrderLine"], "isHidable": true}, {"name": "description", "applicableFields": ["description"], "searchableFields": [], "filterableField": "description", "sortableFields": [], "isHidable": true}, {"name": "collectionDate", "applicableFields": ["collectionDate"], "searchableFields": [], "filterableField": "collectionDate", "sortableFields": ["collectionDate"], "isHidable": true}, {"name": "deliveryDate", "applicableFields": ["deliveryDate"], "searchableFields": [], "filterableField": "deliveryDate", "sortableFields": ["deliveryDate"], "isHidable": true}, {"name": "dispositionPickUpDate", "applicableFields": ["dispositionPickUpDate"], "searchableFields": [], "filterableField": "dispositionPickUpDate", "sortableFields": ["dispositionPickUpDate"], "isHidable": true}, {"name": "dispositionDeliveryDate", "applicableFields": ["dispositionDeliveryDate"], "searchableFields": [], "filterableField": "dispositionDeliveryDate", "sortableFields": ["dispositionDeliveryDate"], "isHidable": true}, {"name": "treatmentCentre", "applicableFields": ["treatmentCentre"], "searchableFields": [], "filterableField": "treatmentCentre", "sortableFields": ["treatmentCentre"], "isHidable": true}, {"name": "endTreatmentCentre", "applicableFields": ["endTreatmentCentre"], "searchableFields": [], "filterableField": "endTreatmentCentre", "sortableFields": ["endTreatmentCentre"], "isHidable": true}, {"name": "ewcCode", "applicableFields": ["ewcCode"], "searchableFields": [], "filterableField": "ewcCode", "sortableFields": ["ewcCode"], "isHidable": true}, {"name": "wtfForm", "applicableFields": ["wtfForm"], "searchableFields": [], "filterableField": "wtfForm", "sortableFields": ["wtfForm"], "isHidable": true}, {"name": "tfs", "applicableFields": ["tfs"], "searchableFields": [], "filterableField": "tfs", "sortableFields": ["tfs"], "isHidable": true}, {"name": "disposalDate", "applicableFields": ["disposalDate"], "searchableFields": [], "filterableField": "disposalDate", "sortableFields": ["disposalDate"], "isHidable": true}, {"name": "printDate", "applicableFields": ["printDate"], "searchableFields": [], "filterableField": "printDate", "sortableFields": ["printDate"], "isHidable": true}, {"name": "wasteProducerName", "applicableFields": ["wasteProducerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["wasteProducerName"], "isHidable": true}, {"name": "contract", "applicableFields": ["contract"], "searchableFields": [], "filterableField": "contract", "sortableFields": ["contract"], "isHidable": true}, {"name": "contractItem", "applicableFields": ["contractItem"], "searchableFields": [], "filterableField": "contractItem", "sortableFields": ["contractItem"], "isHidable": true}, {"name": "wasteProducerId", "applicableFields": ["wasteProducerId"], "searchableFields": [], "filterableField": "wasteProducerId", "sortableFields": ["wasteProducerId"], "isHidable": true}, {"name": "invoice", "applicableFields": ["invoice"], "searchableFields": [], "filterableField": "invoice", "sortableFields": ["invoice"], "isHidable": true}, {"name": "pickUpAddressName", "applicableFields": ["pickUpAddressName"], "searchableFields": [], "filterableField": null, "sortableFields": ["pickUpAddressName"], "isHidable": true}, {"name": "pickUpAddressId", "applicableFields": ["pickUpAddressId"], "searchableFields": [], "filterableField": "pickUpAddressId", "sortableFields": ["pickUpAddressId"], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": "customerId", "sortableFields": ["customerId"], "isHidable": true}, {"name": "customerName", "applicableFields": ["customerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["customerName"], "isHidable": true}]}