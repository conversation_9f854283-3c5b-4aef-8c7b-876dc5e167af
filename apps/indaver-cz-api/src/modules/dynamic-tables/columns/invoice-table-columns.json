{"columns": [{"name": "invoiceNumber", "applicableFields": ["invoiceNumber"], "searchableFields": [], "filterableField": "invoiceNumber", "sortableFields": ["invoiceNumber"], "isHidable": true}, {"name": "status", "applicableFields": ["status"], "searchableFields": [], "filterableField": "statuses", "sortableFields": [], "isHidable": true}, {"name": "issuedOn", "applicableFields": ["issuedOn"], "searchableFields": [], "filterableField": "issueDate", "sortableFields": ["issueDate"], "isHidable": true}, {"name": "dueOn", "applicableFields": ["dueOn"], "searchableFields": [], "filterableField": "dueDate", "sortableFields": ["dueDate"], "isHidable": true}, {"name": "customerId", "applicableFields": ["customerId"], "searchableFields": [], "filterableField": "customerId", "sortableFields": ["customerId"], "isHidable": true}, {"name": "customerName", "applicableFields": ["customerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["customerName"], "isHidable": true}, {"name": "customerReference", "applicableFields": ["customerReference"], "searchableFields": [], "filterableField": "customerReference", "sortableFields": ["customerReference"], "isHidable": true}, {"name": "type", "applicableFields": ["type"], "searchableFields": [], "filterableField": "type", "sortableFields": [], "isHidable": true}, {"name": "payerId", "applicableFields": ["payerId"], "searchableFields": [], "filterableField": "payerId", "sortableFields": ["payerId"], "isHidable": true}, {"name": "payerName", "applicableFields": ["payerName"], "searchableFields": [], "filterableField": null, "sortableFields": ["payerName"], "isHidable": true}, {"name": "netAmount", "applicableFields": ["netAmount"], "searchableFields": [], "filterableField": null, "sortableFields": ["netAmount"], "isHidable": true}, {"name": "vatAmount", "applicableFields": ["vatAmount"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "currency", "applicableFields": ["currency"], "searchableFields": [], "filterableField": null, "sortableFields": [], "isHidable": true}, {"name": "accountDocumentNumber", "applicableFields": ["accountDocumentNumber"], "searchableFields": [], "filterableField": "accountDocumentNumber", "sortableFields": ["accountDocumentNumber"], "isHidable": true}, {"name": "accountManagerName", "applicableFields": ["accountManagerName"], "searchableFields": [], "filterableField": "accountManagerName", "sortableFields": [], "isHidable": true}, {"name": "companyName", "applicableFields": ["companyName"], "searchableFields": [], "filterableField": "companyName", "sortableFields": ["companyName"], "isHidable": true}]}