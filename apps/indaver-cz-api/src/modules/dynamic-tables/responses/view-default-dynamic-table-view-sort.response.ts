import { ApiProperty } from '@nestjs/swagger'
import { SortDirection } from '@wisemen/pagination'

export class SortConfigurationResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: Number })
  order: number

  @ApiProperty({ type: String, enum: SortDirection, enumName: 'SortDirection' })
  direction: SortDirection

  constructor (uuid: string, order: number, direction: SortDirection) {
    this.uuid = uuid
    this.order = order
    this.direction = direction
  }
}
