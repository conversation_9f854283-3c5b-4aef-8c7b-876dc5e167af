import { Injectable } from '@nestjs/common'
import { Not, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { DynamicTableView } from '../entities/dynamic-table-view.entity.js'
import { DuplicateViewNameError } from '../errors/duplicate-view-name.error.js'
import { DynamicTableColumn } from '../entities/dynamic-table-column.entity.js'
import { ColumnNotHidableError } from '../errors/column-not-hidable.error.js'
import { InvalidGlobalDefaultError } from '../errors/invalid-global-default.error.js'
import { UnauthorizedError } from '../../exceptions/generic/unauthorized.error.js'
import { PermissionGuardService } from '../../permission/guards/permission.guard.service.js'
import { Permission } from '../../permission/permission.enum.js'

@Injectable()
export class DynamicTableViewValidatorService {
  constructor (
    @InjectRepository(DynamicTableColumn)
    private readonly dynamicTableColumnRepository: Repository<DynamicTableColumn>,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    private readonly permissionService: PermissionGuardService
  ) {}

  async validateHiddenColumns (
    dynamicTableUuid: string,
    visibleColumnUuids: string[]
  ): Promise<void> {
    const nonHidableColumns = await this.dynamicTableColumnRepository.find({
      where: {
        dynamicTableUuid,
        isHidable: false
      }
    })

    for (const nonHidableColumn of nonHidableColumns) {
      if (!visibleColumnUuids.includes(nonHidableColumn.uuid)) {
        throw new ColumnNotHidableError(
          nonHidableColumn.name.toString(),
          { pointer: '$.visibleColumns' }
        )
      }
    }
  }

  async validateViewNameNonExistent (
    userUuid: string,
    name: string,
    dynamicTableUuid: string,
    excludeViewUuid?: string
  ): Promise<void> {
    const viewWithNameExists = await this.dynamicTableViewRepository.exists({
      where: [
        {
          createdByUserUuid: userUuid,
          name,
          dynamicTableUuid,
          uuid: excludeViewUuid !== undefined ? Not(excludeViewUuid) : undefined
        },
        {
          isGlobal: true,
          name,
          dynamicTableUuid,
          uuid: excludeViewUuid !== undefined ? Not(excludeViewUuid) : undefined
        }
      ]
    })

    if (viewWithNameExists) {
      throw new DuplicateViewNameError({ pointer: '$.viewName' })
    }
  }

  async validateGlobalView (
    userUuid: string,
    isGlobal: boolean | undefined,
    isGlobalDefault: boolean | undefined
  ): Promise<void> {
    if (isGlobal !== undefined && isGlobal) {
      const isSystemAdmin = await this.permissionService.hasPermissions(userUuid, [
        Permission.DYNAMIC_TABLE_VIEW_MANAGE
      ])

      if (!isSystemAdmin) {
        throw new UnauthorizedError('error.dynamic-tables.unauthorized_manage_global_view')
      }
    }

    if (isGlobalDefault !== undefined && isGlobalDefault) {
      if (isGlobal === undefined || !isGlobal) {
        throw new InvalidGlobalDefaultError({ pointer: '$.isGlobalDefault' })
      }
    }
  }
}
