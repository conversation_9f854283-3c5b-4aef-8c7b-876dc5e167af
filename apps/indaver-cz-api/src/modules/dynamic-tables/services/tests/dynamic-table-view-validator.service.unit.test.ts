import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { Repository } from 'typeorm'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { DynamicTableViewValidatorService } from '../dynamic-table-view-validator.service.js'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { ColumnNotHidableError } from '../../errors/column-not-hidable.error.js'
import { DynamicTableColumnEntityBuilder } from '../../tests/dynamic-table-column-enitity.builder.js'
import { UnauthorizedError } from '../../../exceptions/generic/unauthorized.error.js'
import { InvalidGlobalDefaultError } from '../../errors/invalid-global-default.error.js'
import { DynamicColumnNames } from '../../helpers/dynamic-table-column-name.type.js'
import { DuplicateViewNameError } from '../../errors/duplicate-view-name.error.js'
import { PermissionGuardService } from '../../../permission/guards/permission.guard.service.js'

describe('use-case unit test', () => {
  let validator: DynamicTableViewValidatorService

  let dynamicTableColumnRepository: SinonStubbedInstance<Repository<DynamicTableColumn>>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let permissionService: SinonStubbedInstance<PermissionGuardService>

  before(() => {
    TestBench.setupUnitTest()

    dynamicTableColumnRepository = createStubInstance<Repository<DynamicTableColumn>>(
        Repository<DynamicTableColumn>
    )
    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
        Repository<DynamicTableView>
    )
    permissionService = createStubInstance(PermissionGuardService)

    validator = new DynamicTableViewValidatorService(
      dynamicTableColumnRepository,
      dynamicTableViewRepository,
      permissionService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    dynamicTableColumnRepository.find.resolves([])
    dynamicTableViewRepository.exists.resolves(false)
    permissionService.hasPermissions.resolves(true)
  }

  describe('validateHiddenColumns', () => {
    it('should throw an error when non hidable column not given', async () => {
      const nonHidableColumn = new DynamicTableColumnEntityBuilder()
        .withName(DynamicColumnNames.STATUS)
        .withIsHidable(false)
        .build()

      dynamicTableColumnRepository.find.resolves([
        nonHidableColumn
      ])

      await expect(
        validator.validateHiddenColumns(
          randomUUID(),
          []
        )
      ).rejects.toThrow(new ColumnNotHidableError(nonHidableColumn.name.toString()))
    })

    it('should not throw an error when non hidable column is given', async () => {
      const nonHidableColumn = new DynamicTableColumnEntityBuilder()
        .withIsHidable(false)
        .build()

      dynamicTableColumnRepository.find.resolves([
        nonHidableColumn
      ])

      await expect(
        validator.validateHiddenColumns(
          randomUUID(),
          [nonHidableColumn.uuid]
        )
      ).resolves.not.toThrow()
    })
  })

  describe('validateViewNameNonExistent', () => {
    it('should throw an error when view name already exists', async () => {
      const command = {
        viewName: 'name'
      }

      dynamicTableViewRepository.exists.resolves(true)

      await expect(
        validator.validateViewNameNonExistent(
          randomUUID(),
          command.viewName,
          randomUUID(),
          randomUUID()
        )
      ).rejects.toThrow(new DuplicateViewNameError())
    })

    it('should not throw an error when view name does not exist', async () => {
      const command = {
        viewName: 'name'
      }

      dynamicTableViewRepository.exists.resolves(false)

      await expect(
        validator.validateViewNameNonExistent(
          randomUUID(),
          command.viewName,
          randomUUID(),
          randomUUID()
        )
      ).resolves.not.toThrow()
    })
  })

  describe('validateGlobalView', () => {
    it('should throw an error when user doesn\'t have dynamic table view manage permission', async () => {
      permissionService.hasPermissions.resolves(false)

      await expect(
        validator.validateGlobalView(
          randomUUID(),
          true,
          undefined
        )
      ).rejects.toThrow(new UnauthorizedError())
    })

    it('should not throw an error when user has dynamic table view manage permission', async () => {
      permissionService.hasPermissions.resolves(true)

      await expect(
        validator.validateGlobalView(
          randomUUID(),
          true,
          undefined
        )
      ).resolves.not.toThrow()
    })

    it('should throw an error when isGlobalDefault is true and isGlobal is false', async () => {
      await expect(
        validator.validateGlobalView(
          randomUUID(),
          false,
          true
        )
      ).rejects.toThrow(new InvalidGlobalDefaultError())
    })

    it('should not throw an error when isGlobalDefault is true and isGlobal is true', async () => {
      await expect(
        validator.validateGlobalView(
          randomUUID(),
          true,
          true
        )
      ).resolves.not.toThrow()
    })
  })
})
