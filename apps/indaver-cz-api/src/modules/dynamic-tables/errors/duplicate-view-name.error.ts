import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class DuplicateViewNameError extends BadRequestApiError {
  @ApiErrorCode('duplicate_view_name')
  readonly code = 'duplicate_view_name'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.duplicate_view_name')
    super(detail, source)
  }
}
