import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class LastGlobalViewNotDeletable extends BadRequestApiError {
  @ApiErrorCode('last_global_view_not_deletable')
  readonly code = 'last_global_view_not_deletable'

  readonly meta: never

  constructor () {
    const detail = translateCurrent('error.dynamic-tables.last_global_view_not_deletable')
    super(detail)
  }
}
