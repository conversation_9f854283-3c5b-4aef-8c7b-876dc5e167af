import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class ColumnNotFoundError extends BadRequestApiError {
  @ApiErrorCode('column_not_found')
  readonly code = 'column_not_found'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.column_not_found')
    super(detail, source)
  }
}
