import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class GlobalDefaultViewNotDeletable extends BadRequestApiError {
  @ApiErrorCode('global_default_view_not_deletable')
  readonly code = 'global_default_view_not_deletable'

  readonly meta: never

  constructor () {
    const detail = translateCurrent('error.dynamic-tables.global_default_view_not_deletable')
    super(detail)
  }
}
