import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class ColumnNotHidableError extends BadRequestApiError {
  @ApiErrorCode('column_not_hidable')
  readonly code = 'column_not_hidable'

  readonly meta: never

  constructor (columnName: string, source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.column_not_hidable', {
      args: { columnName: columnName }
    })
    super(detail, source)
  }
}
