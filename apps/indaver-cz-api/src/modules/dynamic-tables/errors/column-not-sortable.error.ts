import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class ColumnNotSortableError extends BadRequestApiError {
  @ApiErrorCode('column_not_sortable')
  readonly code = 'column_not_sortable'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.column_not_sortable')
    super(detail, source)
  }
}
