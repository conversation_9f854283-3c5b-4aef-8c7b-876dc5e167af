import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class DuplicateColumnError extends BadRequestApiError {
  @ApiErrorCode('duplicate_column')
  readonly code = 'duplicate_column'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.duplicate_column')
    super(detail, source)
  }
}
