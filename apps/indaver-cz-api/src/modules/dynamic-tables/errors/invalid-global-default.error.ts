import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class InvalidGlobalDefaultError extends BadRequestApiError {
  @ApiErrorCode('invalid_global_default')
  readonly code = 'invalid_global_default'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.dynamic-tables.invalid_global_default')
    super(detail, source)
  }
}
