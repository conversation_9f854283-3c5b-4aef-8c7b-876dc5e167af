import { randomUUID } from 'crypto'
import { UserDefaultDynamicTableView } from '../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableView } from '../entities/dynamic-table-view.entity.js'

export class UserDefaultDynamicTableViewEntityBuilder {
  private userDefaultDynamicTableView: UserDefaultDynamicTableView

  constructor () {
    this.reset()
  }

  reset (): this {
    this.userDefaultDynamicTableView = new UserDefaultDynamicTableView()

    this.userDefaultDynamicTableView.userUuid = randomUUID()
    this.userDefaultDynamicTableView.dynamicTableUuid = randomUUID()
    this.userDefaultDynamicTableView.dynamicTableViewUuid = randomUUID()

    return this
  }

  withUserUuid (userUuid: string): this {
    this.userDefaultDynamicTableView.userUuid = userUuid

    return this
  }

  withDynamicTableUuid (tableUuid: string): this {
    this.userDefaultDynamicTableView.dynamicTableUuid = tableUuid

    return this
  }

  withDynamicTableViewUuid (dynamicTableViewUuid: string): this {
    this.userDefaultDynamicTableView.dynamicTableViewUuid = dynamicTableViewUuid

    return this
  }

  withDynamicTableView (tableView: DynamicTableView): this {
    this.userDefaultDynamicTableView.dynamicTableView = tableView

    return this
  }

  build (): UserDefaultDynamicTableView {
    const response = this.userDefaultDynamicTableView

    this.reset()

    return response
  }
}
