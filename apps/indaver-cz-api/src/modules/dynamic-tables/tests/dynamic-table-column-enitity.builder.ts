import { randomUUID } from 'crypto'
import { DynamicTableColumn } from '../entities/dynamic-table-column.entity.js'
import { DynamicColumnName } from '../helpers/dynamic-table-column-name.type.js'
import { DynamicTableColumnField } from '../helpers/dynamic-table-column-fields.type.js'

export class DynamicTableColumnEntityBuilder {
  private dynamicTableColumn: DynamicTableColumn

  constructor () {
    this.reset()
  }

  reset (): this {
    this.dynamicTableColumn = new DynamicTableColumn()

    this.dynamicTableColumn.uuid = randomUUID()
    this.dynamicTableColumn.createdAt = new Date()
    this.dynamicTableColumn.updatedAt = new Date()
    this.dynamicTableColumn.filterableField = null
    this.dynamicTableColumn.isHidable = false
    this.dynamicTableColumn.sortableFields = []
    this.dynamicTableColumn.searchableFields = []
    this.dynamicTableColumn.applicableFields = []

    return this
  }

  withDynamicTableUuid (dynamicTableUuid: string) {
    this.dynamicTableColumn.dynamicTableUuid = dynamicTableUuid

    return this
  }

  withName (name: DynamicColumnName): this {
    this.dynamicTableColumn.name = name

    return this
  }

  withSortableFields (sortableFields: DynamicTableColumnField[]): this {
    this.dynamicTableColumn.sortableFields = sortableFields

    return this
  }

  withFilterableFields (filterableField: DynamicTableColumnField): this {
    this.dynamicTableColumn.filterableField = filterableField

    return this
  }

  withIsHidable (isHidable: boolean): this {
    this.dynamicTableColumn.isHidable = isHidable

    return this
  }

  withSearchableFields (searchableFields: DynamicTableColumnField[]): this {
    this.dynamicTableColumn.searchableFields = searchableFields

    return this
  }

  withApplicableFields (fields: DynamicTableColumnField[]) {
    this.dynamicTableColumn.applicableFields = fields

    return this
  }

  build (): DynamicTableColumn {
    const result = this.dynamicTableColumn

    this.reset()

    return result
  }
}
