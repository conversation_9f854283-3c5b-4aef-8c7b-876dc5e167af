import { randomUUID } from 'crypto'
import { DynamicTableViewVisibleColumnsCommand } from '../commands/dynamic-table-view-visible-columns.command.js'

export class DynamicTableViewVisibleColumnsCommandBuilder {
  private command: DynamicTableViewVisibleColumnsCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new DynamicTableViewVisibleColumnsCommand()
    this.command.columnUuid = randomUUID()

    return this
  }

  withColumnUuid (uuid: string): this {
    this.command.columnUuid = uuid

    return this
  }

  build (): DynamicTableViewVisibleColumnsCommand {
    const result = this.command

    this.reset()

    return result
  }
}
