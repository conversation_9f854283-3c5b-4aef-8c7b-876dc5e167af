import { randomUUID } from 'crypto'
import { DynamicTableViewFilterCommand } from '../commands/dynamic-table-view-filter.command.js'

export class DynamicTableViewFiltersCommandBuilder {
  private command: DynamicTableViewFilterCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new DynamicTableViewFilterCommand()
    this.command.columnUuid = randomUUID()
    this.command.value = 'Test'

    return this
  }

  withColumnUuid (uuid: string): this {
    this.command.columnUuid = uuid

    return this
  }

  withValue (value: string | string[]): this {
    this.command.value = value

    return this
  }

  build (): DynamicTableViewFilterCommand {
    const result = this.command

    this.reset()

    return result
  }
}
