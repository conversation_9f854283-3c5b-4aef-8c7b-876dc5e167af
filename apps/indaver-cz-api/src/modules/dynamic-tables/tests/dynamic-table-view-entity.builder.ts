import { randomUUID } from 'crypto'
import { DynamicTableView } from '../entities/dynamic-table-view.entity.js'
import { FilterConfiguration, SortConfiguration, VisibilityConfiguration } from '../helpers/dynamic-table-view-configuration.js'
import { DynamicTable } from '../entities/dynamic-table.entity.js'

export class DynamicTableViewEntityBuilder {
  private dynamicTableView: DynamicTableView

  constructor () {
    this.reset()
  }

  reset (): this {
    this.dynamicTableView = new DynamicTableView()

    this.dynamicTableView.uuid = randomUUID()
    this.dynamicTableView.dynamicTableUuid = randomUUID()
    this.dynamicTableView.createdByUserUuid = randomUUID()
    this.dynamicTableView.createdAt = new Date()
    this.dynamicTableView.updatedAt = new Date()
    this.dynamicTableView.name = randomUUID()
    this.dynamicTableView.isDefaultGlobal = false
    this.dynamicTableView.isGlobal = false
    this.dynamicTableView.configuration = {
      sorting: [],
      filters: [],
      visibleColumns: []
    }

    return this
  }

  withUuid (uuid: string): this {
    this.dynamicTableView.uuid = uuid

    return this
  }

  withDynamicTableUuid (tableUuid: string): this {
    this.dynamicTableView.dynamicTableUuid = tableUuid

    return this
  }

  withDynamicTable (dynamicTable: DynamicTable): this {
    this.dynamicTableView.dynamicTable = dynamicTable

    return this
  }

  withCreatedByUserUuid (userUuid: string): this {
    this.dynamicTableView.createdByUserUuid = userUuid

    return this
  }

  withName (name: string): this {
    this.dynamicTableView.name = name

    return this
  }

  withFilters (filters: FilterConfiguration[]): this {
    this.dynamicTableView.configuration.filters = filters

    return this
  }

  withSorts (sorting: SortConfiguration[]): this {
    this.dynamicTableView.configuration.sorting = sorting

    return this
  }

  withVisibleColumns (visibleColums: VisibilityConfiguration[]): this {
    this.dynamicTableView.configuration.visibleColumns = visibleColums

    return this
  }

  withIsGlobal (isGlobal: boolean): this {
    this.dynamicTableView.isGlobal = isGlobal

    return this
  }

  withIsDefaultGlobal (isDefaultGlobal: boolean): this {
    this.dynamicTableView.isDefaultGlobal = isDefaultGlobal

    return this
  }

  build (): DynamicTableView {
    const response = this.dynamicTableView

    this.reset()

    return response
  }
}
