import { randomUUID } from 'crypto'
import { DynamicTable } from '../entities/dynamic-table.entity.js'
import { DynamicTableName } from '../enums/dynamic-table-name.enum.js'
import { DynamicTableColumn } from '../entities/dynamic-table-column.entity.js'

export class DynamicTableEntityBuilder {
  private dynamicTable: DynamicTable

  constructor () {
    this.reset()
  }

  reset (): this {
    this.dynamicTable = new DynamicTable()

    this.dynamicTable.uuid = randomUUID()
    this.dynamicTable.createdAt = new Date()
    this.dynamicTable.updatedAt = new Date()
    this.dynamicTable.name = DynamicTableName.WASTE_INQUIRY

    return this
  }

  withName (name: DynamicTableName): this {
    this.dynamicTable.name = name

    return this
  }

  withColumns (colums: DynamicTableColumn[]): this {
    this.dynamicTable.columns = colums

    return this
  }

  build (): DynamicTable {
    const result = this.dynamicTable

    this.reset()

    return result
  }
}
