import { randomUUID } from 'crypto'
import { SortDirection } from '@wisemen/pagination'
import { DynamicTableViewSortCommand } from '../commands/dynamic-table-view-sort.command.js'

export class DynamicTableViewSortsCommandBuilder {
  private command: DynamicTableViewSortCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new DynamicTableViewSortCommand()
    this.command.columnUuid = randomUUID()
    this.command.direction = SortDirection.ASC

    return this
  }

  withColumnUuid (uuid: string): this {
    this.command.columnUuid = uuid

    return this
  }

  withDirection (direction: SortDirection): this {
    this.command.direction = direction

    return this
  }

  build (): DynamicTableViewSortCommand {
    const result = this.command

    this.reset()

    return result
  }
}
