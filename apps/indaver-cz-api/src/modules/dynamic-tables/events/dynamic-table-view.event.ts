import { DomainEventSubjectType } from '../../domain-events/domain-event-subject-type.enum.js'
import { DomainEvent, DomainEventOptions } from '../../domain-events/domain-event.js'

export class DynamicTableViewEvent<Content extends object> extends DomainEvent<Content> {
  constructor (
    options: Omit<DomainEventOptions<Content>, 'subjectType' | 'subjectId'> & { dynamicTableViewUuid: string }
  ) {
    super({
      ...options,
      subjectId: options.dynamicTableViewUuid,
      subjectType: DomainEventSubjectType.DYNAMIC_TABLE_VIEW
    })
  }
}
