import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { DeleteDynamicTableViewValidator } from './delete-dynamic-table-view.validator.js'
import { DynamicTableViewDeletedEvent } from './dynamic-table-view-deleted.event.js'

@Injectable()
export class DeleteDynamicTableViewUseCase {
  constructor (
    private readonly validator: DeleteDynamicTableViewValidator,
    private readonly dataSource: DataSource,
    @InjectRepository(UserDefaultDynamicTableView)
    private readonly userDefaultViewRepository: Repository<UserDefaultDynamicTableView>,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    tableName: DynamicTableName,
    dynamicTableViewUuid: string
  ): Promise<void> {
    await this.validator.validate(tableName, dynamicTableViewUuid)

    await transaction(this.dataSource, async () => {
      await this.userDefaultViewRepository.delete({
        dynamicTableViewUuid
      })

      const deleteResult = await this.dynamicTableViewRepository.delete({
        uuid: dynamicTableViewUuid
      })

      if (deleteResult.affected === 0) {
        throw new NotFoundError('error.dynamic-tables.dynamic_table_view_not_found')
      }

      await this.eventEmitter.emit([new DynamicTableViewDeletedEvent(dynamicTableViewUuid)])
    })
  }
}
