import { Injectable } from '@nestjs/common'
import { Not, Repository } from 'typeorm'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { UnauthorizedError } from '../../../exceptions/generic/unauthorized.error.js'
import { LastGlobalViewNotDeletable } from '../../errors/last-global-view-not-deletable.js'
import { GlobalDefaultViewNotDeletable } from '../../errors/global-default-view-not-deletable.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'

@Injectable()
export class DeleteDynamicTableViewValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    private readonly validatorService: DynamicTableViewValidatorService
  ) {}

  async validate (
    tableName: DynamicTableName,
    dynamicTableUuid: string
  ): Promise<void> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const dynamicTableView = await this.dynamicTableViewRepository.findOneByOrFail({
      uuid: dynamicTableUuid,
      dynamicTable: {
        name: tableName
      }
    })

    await this.validateDeleteGlobalView(
      userUuid,
      dynamicTableView,
      tableName
    )

    this.canUserDeleteView(
      userUuid,
      dynamicTableView
    )
  }

  private async validateDeleteGlobalView (
    userUuid: string,
    dynamicTableView: DynamicTableView,
    tableName: DynamicTableName
  ): Promise<void> {
    if (dynamicTableView.isGlobal) {
      if (dynamicTableView.isDefaultGlobal) {
        throw new GlobalDefaultViewNotDeletable()
      }

      const otherGlobalDynamicTableViewExists = await this.dynamicTableViewRepository.existsBy({
        uuid: Not(dynamicTableView.uuid),
        dynamicTable: {
          name: tableName
        },
        isGlobal: true
      })

      if (!otherGlobalDynamicTableViewExists) {
        throw new LastGlobalViewNotDeletable()
      }

      await this.validatorService.validateGlobalView(
        userUuid,
        dynamicTableView.isGlobal,
        dynamicTableView.isDefaultGlobal
      )
    }
  }

  private canUserDeleteView (
    userUuid: string,
    dynamicTableView: DynamicTableView
  ): void {
    if (!dynamicTableView.isGlobal && dynamicTableView.createdByUserUuid !== userUuid) {
      throw new UnauthorizedError('error.dynamic-tables.unauthorized_delete_view')
    }
  }
}
