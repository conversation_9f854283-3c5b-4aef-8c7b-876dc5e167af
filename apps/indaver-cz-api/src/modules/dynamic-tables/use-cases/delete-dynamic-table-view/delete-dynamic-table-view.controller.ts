import { Controller, Delete, HttpCode, Param, ParseEnumPipe } from '@nestjs/common'
import { ApiT<PERSON>s, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiBadRequestErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { LastGlobalViewNotDeletable } from '../../errors/last-global-view-not-deletable.js'
import { GlobalDefaultViewNotDeletable } from '../../errors/global-default-view-not-deletable.js'
import { DeleteDynamicTableViewUseCase } from './delete-dynamic-table-view.use-case.js'

@ApiTags('Dynamic table')
@Controller('dynamic-tables/:name/views/:uuid')
@ApiOAuth2([])
export class DeleteDynamicTableViewController {
  constructor (
    private readonly useCase: DeleteDynamicTableViewUseCase
  ) {}

  @Delete()
  @HttpCode(204)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiBadRequestErrorResponse(GlobalDefaultViewNotDeletable, LastGlobalViewNotDeletable)
  async deleteDynamicTableView (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName,
    @UuidParam('uuid') dynamicTableViewUuid: string
  ): Promise<void> {
    return await this.useCase.execute(tableName, dynamicTableViewUuid)
  }
}
