import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { DynamicTableViewEvent } from '../../events/dynamic-table-view.event.js'

export class DynamicTableViewDeletedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly dynamicTableViewUuid: string

  constructor (dynamicTableViewUuid: string) {
    this.dynamicTableViewUuid = dynamicTableViewUuid
  }
}

@RegisterDomainEvent(DomainEventType.DYNAMIC_TABLE_VIEW_DELETED, 1)
export class DynamicTableViewDeletedEvent
  extends DynamicTableViewEvent<DynamicTableViewDeletedEventContent> {
  constructor (dynamicTableViewUuid: string) {
    super({
      dynamicTableViewUuid: dynamicTableViewUuid,
      content: new DynamicTableViewDeletedEventContent(dynamicTableViewUuid)
    })
  }
}
