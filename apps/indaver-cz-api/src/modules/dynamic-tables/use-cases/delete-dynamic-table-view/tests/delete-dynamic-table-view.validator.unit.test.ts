import { afterEach, before, describe, it } from 'node:test'
import { Repository } from 'typeorm'
import { SinonStubbedInstance, createStubInstance } from 'sinon'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DeleteDynamicTableViewValidator } from '../delete-dynamic-table-view.validator.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { RoleEntityBuilder } from '../../../../../app/roles/tests/builders/entities/role-entity.builder.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { UnauthorizedError } from '../../../../exceptions/generic/unauthorized.error.js'
import { GlobalDefaultViewNotDeletable } from '../../../errors/global-default-view-not-deletable.js'
import { LastGlobalViewNotDeletable } from '../../../errors/last-global-view-not-deletable.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DynamicTableViewValidatorService } from '../../../services/dynamic-table-view-validator.service.js'

describe('Delete dynamic table view validator unit test', () => {
  let validator: DeleteDynamicTableViewValidator

  let systemAdmin: User
  let user: User

  let authContext: SinonStubbedInstance<AuthContext>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let validatorService: SinonStubbedInstance<DynamicTableViewValidatorService>

  before(() => {
    TestBench.setupUnitTest()

    const systemAdminRole = new RoleEntityBuilder()
      .withIsSystemAdmin(true)
      .build()

    systemAdmin = new UserEntityBuilder().addRole(systemAdminRole).build()
    user = new UserEntityBuilder().build()

    authContext = createStubInstance(AuthContext)
    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
      Repository<DynamicTableView>
    )
    validatorService = createStubInstance(DynamicTableViewValidatorService)

    validator = new DeleteDynamicTableViewValidator(
      authContext,
      dynamicTableViewRepository,
      validatorService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    dynamicTableViewRepository.existsBy.resolves(true)
    validatorService.validateGlobalView.resolves()
  }

  describe('Delete global view', () => {
    it('throws an error when deleting default global view', async () => {
      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(true)
        .withIsDefaultGlobal(true)
        .build()

      authContext.getUserUuidOrFail.returns(systemAdmin.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).rejects.toThrow(new GlobalDefaultViewNotDeletable())
    })

    it('throws an error when deleting last global view', async () => {
      dynamicTableViewRepository.existsBy.resolves(false)

      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(true)
        .build()

      authContext.getUserUuidOrFail.returns(user.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).rejects.toThrow(new LastGlobalViewNotDeletable())
    })

    it('throws an error when deleting global view unauthorized', async () => {
      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(true)
        .build()

      authContext.getUserUuidOrFail.returns(user.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)
      validatorService.validateGlobalView.throws(new UnauthorizedError())

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).rejects.toThrow(new UnauthorizedError())
    })

    it('doesn\'t throw an error when deleting global view authorized', async () => {
      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(true)
        .build()

      authContext.getUserUuidOrFail.returns(systemAdmin.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).resolves.not.toThrow()
    })
  })

  describe('Delete private view', () => {
    it('throws an error when deleting other user\'s view', async () => {
      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(false)
        .withCreatedByUserUuid(systemAdmin.uuid)
        .build()

      authContext.getUserUuidOrFail.returns(user.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).rejects.toThrow(new UnauthorizedError())
    })

    it('doesn\'t throw an error when deleting own user\'s view', async () => {
      const dynamicTableView = new DynamicTableViewEntityBuilder()
        .withIsGlobal(false)
        .withCreatedByUserUuid(user.uuid)
        .build()

      authContext.getUserUuidOrFail.returns(user.uuid)
      dynamicTableViewRepository.findOneByOrFail.resolves(dynamicTableView)

      await expect(validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid
      )).resolves.not.toThrow()
    })
  })
})
