import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'

describe('Delete dynamic table view e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableViewRepository: Repository<DynamicTableView>

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableViewRepository = setup.dataSource.getRepository(DynamicTableView)
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .delete(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 204 when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.DYNAMIC_TABLE_VIEW_MANAGE])

    const dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.WASTE_INQUIRY
      },
      relations: {
        columns: true
      }
    })

    const tableView = await dynamicTableViewRepository.save(
      new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.user.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .delete(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views/${tableView.uuid}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(204)
  })
})
