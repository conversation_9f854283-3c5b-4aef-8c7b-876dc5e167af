import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { SinonStubbedInstance, createStubInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DeleteDynamicTableViewUseCase } from '../delete-dynamic-table-view.use-case.js'
import { DeleteDynamicTableViewValidator } from '../delete-dynamic-table-view.validator.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { DynamicTableViewDeletedEvent } from '../dynamic-table-view-deleted.event.js'

describe('use-case unit test', () => {
  let useCase: DeleteDynamicTableViewUseCase

  let validator: SinonStubbedInstance<DeleteDynamicTableViewValidator>
  let userDefaultViewRepository: SinonStubbedInstance<Repository<UserDefaultDynamicTableView>>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(DeleteDynamicTableViewValidator)
    userDefaultViewRepository = createStubInstance<Repository<UserDefaultDynamicTableView>>(
      Repository<UserDefaultDynamicTableView>
    )
    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
        Repository<DynamicTableView>
    )
    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new DeleteDynamicTableViewUseCase(
      validator,
      stubDataSource(),
      userDefaultViewRepository,
      dynamicTableViewRepository,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    dynamicTableViewRepository.delete.resolves({ affected: 1, raw: {} })
  }

  it('Calls all methods once', async () => {
    await useCase.execute(DynamicTableName.WASTE_INQUIRY, randomUUID())

    assert.calledOnce(validator.validate)
    assert.calledOnce(userDefaultViewRepository.delete)
    assert.calledOnce(dynamicTableViewRepository.delete)
  })

  it('Emits a DynamicTableViewDeletedEvent event', async () => {
    const dynamicTableViewUuid = randomUUID()

    await useCase.execute(DynamicTableName.WASTE_INQUIRY, dynamicTableViewUuid)

    expect(eventEmitter).toHaveEmitted(new DynamicTableViewDeletedEvent(dynamicTableViewUuid))
  })
})
