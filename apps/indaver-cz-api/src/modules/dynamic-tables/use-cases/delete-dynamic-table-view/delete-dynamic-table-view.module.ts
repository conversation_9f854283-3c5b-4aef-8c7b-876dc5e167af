import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { PermissionModule } from '../../../permission/permission.module.js'
import { DeleteDynamicTableViewController } from './delete-dynamic-table-view.controller.js'
import { DeleteDynamicTableViewUseCase } from './delete-dynamic-table-view.use-case.js'
import { DeleteDynamicTableViewValidator } from './delete-dynamic-table-view.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserDefaultDynamicTableView,
      DynamicTableColumn,
      DynamicTableView
    ]),
    DomainEventEmitterModule,
    PermissionModule
  ],
  controllers: [
    DeleteDynamicTableViewController
  ],
  providers: [
    DeleteDynamicTableViewUseCase,
    DeleteDynamicTableViewValidator,
    DynamicTableViewValidatorService
  ]
})
export class DeleteDynamicTableViewModule {}
