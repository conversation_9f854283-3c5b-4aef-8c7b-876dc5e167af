import { <PERSON>, Get, Param, ParseEnumPipe } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { ViewDynamicTableColumnIndexUseCase } from './view-dynamic-table-columns-index.use-case.js'
import { DynamicTableIndexColumnResponse } from './view-dynamic-table-columns-index.response.js'

@ApiTags('Dynamic table')
@ApiOAuth2([])
@Controller('dynamic-tables/:name/columns')
export class ViewDynamicTableColumnIndexController {
  constructor (
    private readonly useCase: ViewDynamicTableColumnIndexUseCase
  ) {}

  @Get()
  @ApiOkResponse({ type: DynamicTableIndexColumnResponse })
  public async viewDynamicTableColumnIndex (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName
  ): Promise<DynamicTableIndexColumnResponse> {
    return await this.useCase.execute(tableName)
  }
}
