import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { DynamicTableIndexColumnResponse } from './view-dynamic-table-columns-index.response.js'

@Injectable()
export class ViewDynamicTableColumnIndexUseCase {
  constructor (
    @InjectRepository(DynamicTableColumn)
    private readonly dynamicTableColumnRepository: Repository<DynamicTableColumn>
  ) {}

  async execute (tableName: DynamicTableName): Promise<DynamicTableIndexColumnResponse> {
    const columns = await this.dynamicTableColumnRepository.find({
      where: {
        dynamicTable: {
          name: tableName
        }
      }
    })

    return new DynamicTableIndexColumnResponse(
      columns
    )
  }
}
