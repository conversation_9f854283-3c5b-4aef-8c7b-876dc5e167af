import { ApiProperty } from '@nestjs/swagger'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { DynamicTableColumnField } from '../../helpers/dynamic-table-column-fields.type.js'
import { DynamicColumnName, DynamicColumnNames } from '../../helpers/dynamic-table-column-name.type.js'

class DynamicTableColumnIndexView {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, enum: DynamicColumnNames, enumName: 'DynamicColumnNames' })
  name: DynamicColumnName

  @ApiProperty({ type: Boolean })
  isHidable: boolean

  @ApiProperty({ type: String, isArray: true })
  applicableFields: DynamicTableColumnField[]

  @ApiProperty({ type: String, nullable: true })
  filterableField: DynamicTableColumnField | null

  @ApiProperty({ type: String, isArray: true })
  sortableFields: DynamicTableColumnField[]

  @ApiProperty({ type: String, isArray: true })
  searchableFields: DynamicTableColumnField[]

  constructor (
    column: DynamicTableColumn
  ) {
    this.uuid = column.uuid
    this.createdAt = column.createdAt.toISOString()
    this.updatedAt = column.updatedAt.toISOString()
    this.name = column.name
    this.isHidable = column.isHidable
    this.filterableField = column.filterableField
    this.sortableFields = column.sortableFields
    this.applicableFields = column.applicableFields
    this.searchableFields = column.searchableFields
  }
}

export class DynamicTableIndexColumnResponse {
  @ApiProperty({ type: DynamicTableColumnIndexView, isArray: true })
  items: DynamicTableColumnIndexView[]

  constructor (items: DynamicTableColumn[]) {
    this.items = items.map(column => new DynamicTableColumnIndexView(column))
  }
}
