import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DynamicTable } from '../../entities/dynamic-table.entity.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { ViewDynamicTableColumnIndexController } from './view-dynamic-table-columns-index.controller.js'
import { ViewDynamicTableColumnIndexUseCase } from './view-dynamic-table-columns-index.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DynamicTable,
      DynamicTableColumn
    ])
  ],
  controllers: [
    ViewDynamicTableColumnIndexController
  ],
  providers: [
    ViewDynamicTableColumnIndexUseCase
  ]
})
export class ViewDynamicTableColumnIndexModule {}
