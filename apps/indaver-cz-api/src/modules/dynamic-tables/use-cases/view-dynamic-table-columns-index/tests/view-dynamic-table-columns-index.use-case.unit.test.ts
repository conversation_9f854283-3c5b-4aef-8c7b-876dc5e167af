import { before, describe, it } from 'node:test'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewDynamicTableColumnIndexUseCase } from '../view-dynamic-table-columns-index.use-case.js'
import { DynamicTableColumn } from '../../../entities/dynamic-table-column.entity.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTableColumnEntityBuilder } from '../../../tests/dynamic-table-column-enitity.builder.js'
import { WasteInquiryColumnName } from '../../../enums/waste-inquiry-column-name.enum.js'

describe('View dynamic table columns use-case unit test', () => {
  let useCase: ViewDynamicTableColumnIndexUseCase

  let dynamicTableColumnRepository: SinonStubbedInstance<Repository<DynamicTableColumn>>

  before(() => {
    TestBench.setupUnitTest()

    const dynamicTable = new DynamicTableEntityBuilder()
      .withName(DynamicTableName.WASTE_INQUIRY)
      .build()

    const dynamicTableColumn = new DynamicTableColumnEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withName(WasteInquiryColumnName.DATE)
      .build()

    dynamicTableColumnRepository = createStubInstance<Repository<DynamicTableColumn>>(
      Repository<DynamicTableColumn>, {
        find: Promise.resolve([dynamicTableColumn])
      }
    )

    useCase = new ViewDynamicTableColumnIndexUseCase(
      dynamicTableColumnRepository
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(DynamicTableName.WASTE_INQUIRY)

    assert.calledOnce(dynamicTableColumnRepository.find)
  })
})
