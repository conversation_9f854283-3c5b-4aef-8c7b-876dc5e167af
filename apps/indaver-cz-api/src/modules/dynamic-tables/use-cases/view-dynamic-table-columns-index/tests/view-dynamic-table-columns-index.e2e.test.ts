import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { DynamicTableColumn } from '../../../entities/dynamic-table-column.entity.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { DynamicTableColumnEntityBuilder } from '../../../tests/dynamic-table-column-enitity.builder.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { WasteInquiryColumnName } from '../../../enums/waste-inquiry-column-name.enum.js'

describe('View dynamic table columns index e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableColumnRepository: Repository<DynamicTableColumn>

  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableColumnRepository = setup.dataSource.getRepository(DynamicTableColumn)

    authorizedUser = await setup.authContext.getUser([])
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/columns`)

    expect(response).toHaveStatus(401)
  })

  it('returns dynamic tables columns', async () => {
    let dynamicTable = await dynamicTableRepository.findOne({
      where: {
        name: DynamicTableName.PICK_UP_REQUEST
      }
    })

    if (dynamicTable === null) {
      dynamicTable = await dynamicTableRepository.save(
        new DynamicTableEntityBuilder()
          .withName(DynamicTableName.PICK_UP_REQUEST)
          .build()
      )
    }

    await dynamicTableColumnRepository.delete({
      dynamicTableUuid: dynamicTable.uuid
    })

    await dynamicTableColumnRepository.upsert(
      [
        new DynamicTableColumnEntityBuilder()
          .withDynamicTableUuid(dynamicTable.uuid)
          .withName(WasteInquiryColumnName.EWC_CODE)
          .withApplicableFields(['ewcLevel1', 'ewcLevel2', 'ewcLevel3'])
          .build(),
        new DynamicTableColumnEntityBuilder()
          .withDynamicTableUuid(dynamicTable.uuid)
          .withName(WasteInquiryColumnName.WASTE_PRODUCER_NAME)
          .withApplicableFields(['wasteProducerName'])
          .build()
      ],
      {
        conflictPaths: ['dynamicTableUuid', 'name'],
        skipUpdateIfNoValuesChanged: false
      }
    )

    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${dynamicTable.name}/columns`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body.items.length).toBe(2)
  })
})
