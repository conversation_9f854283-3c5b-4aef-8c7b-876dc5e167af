import { ApiProperty } from '@nestjs/swagger'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'

export class UpdateDynamicTableViewResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  constructor (tableView: DynamicTableView) {
    this.uuid = tableView.uuid
    this.createdAt = tableView.createdAt.toISOString()
    this.updatedAt = tableView.updatedAt.toISOString()
  }
}
