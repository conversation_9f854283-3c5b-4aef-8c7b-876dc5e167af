import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { randWord } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { UpdateDynamicTableViewValidator } from '../update-dynamic-table-view.validator.js'
import { DynamicTableViewValidatorService } from '../../../services/dynamic-table-view-validator.service.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { UnauthorizedError } from '../../../../exceptions/generic/unauthorized.error.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { UpdateDynamicTableViewCommandBuilder } from './update-dynamic-table-view.command.builder.js'

describe('Update dynamic table view validator unit test', () => {
  let validator: UpdateDynamicTableViewValidator

  let userUuid: string
  let dynamicTableView: DynamicTableView

  let authContext: SinonStubbedInstance<AuthContext>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let validatorService: SinonStubbedInstance<DynamicTableViewValidatorService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    dynamicTableView = new DynamicTableViewEntityBuilder()
      .withCreatedByUserUuid(userUuid)
      .withDynamicTable(new DynamicTableEntityBuilder().build())
      .build()

    authContext = createStubInstance(AuthContext)
    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
      Repository<DynamicTableView>
    )
    validatorService = createStubInstance(DynamicTableViewValidatorService)

    validator = new UpdateDynamicTableViewValidator(
      authContext,
      dynamicTableViewRepository,
      validatorService
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.returns(userUuid)
    dynamicTableViewRepository.findOneOrFail.resolves(dynamicTableView)
    validatorService.validateGlobalView.resolves()
  }

  it('Throws an error when updating global view unauthorized', async () => {
    dynamicTableViewRepository.findOneOrFail.resolves({ ...dynamicTableView, isGlobal: true })
    validatorService.validateGlobalView.throws(new UnauthorizedError())

    await expect(
      validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid,
        new UpdateDynamicTableViewCommandBuilder().build()
      )
    ).rejects.toThrow(new UnauthorizedError())
  })

  it('Doesn\'t throw error when updating own private view', async () => {
    dynamicTableViewRepository.findOneOrFail.resolves({ ...dynamicTableView, isGlobal: false })

    await expect(
      validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid,
        new UpdateDynamicTableViewCommandBuilder().build()
      )
    ).resolves.not.toThrow()
  })

  it('Throws an error when updating private view from other user', async () => {
    dynamicTableViewRepository.findOneOrFail.resolves({
      ...dynamicTableView,
      createdByUserUuid: randomUUID()
    })

    await expect(
      validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid,
        new UpdateDynamicTableViewCommandBuilder().build()
      )
    ).rejects.toThrow(new UnauthorizedError())
  })

  it('Doesn\'t throw error when updating global view authorized', async () => {
    dynamicTableViewRepository.findOneOrFail.resolves({ ...dynamicTableView, isGlobal: true })

    await expect(
      validator.validate(
        DynamicTableName.WASTE_INQUIRY,
        dynamicTableView.uuid,
        new UpdateDynamicTableViewCommandBuilder().build()
      )
    ).resolves.not.toThrow()
  })

  it('Calls all the validators', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder()
      .withVisibleColumns([])
      .withViewName(randWord())
      .withIsGlobal(true)
      .withIsGlobalDefault(true)
      .build()

    await validator.validate(
      DynamicTableName.WASTE_INQUIRY,
      dynamicTableView.uuid,
      command
    )

    assert.calledOnce(validatorService.validateHiddenColumns)
    assert.calledOnce(validatorService.validateViewNameNonExistent)
    assert.calledTwice(validatorService.validateGlobalView)
  })
})
