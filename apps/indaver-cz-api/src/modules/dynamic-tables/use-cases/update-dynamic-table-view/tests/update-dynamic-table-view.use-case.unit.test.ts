import { before, afterEach, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { UpdateDynamicTableViewUseCase } from '../update-dynamic-table-view.use-case.js'
import { UpdateDynamicTableViewValidator } from '../update-dynamic-table-view.validator.js'
import { DynamicTableViewUpdatedEvent } from '../dynamic-table-view-updated.event.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { UpdateDynamicTableViewCommandBuilder } from './update-dynamic-table-view.command.builder.js'

describe('Update dynamic table view use case unit test', () => {
  let useCase: UpdateDynamicTableViewUseCase

  let userUuid: string
  let tableView: DynamicTableView

  let validator: SinonStubbedInstance<UpdateDynamicTableViewValidator>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let userDefaultDynamicTableViewRepository: SinonStubbedInstance<
    Repository<UserDefaultDynamicTableView
    >>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    tableView = new DynamicTableViewEntityBuilder()
      .withDynamicTable(
        new DynamicTableEntityBuilder()
          .withColumns([])
          .build()
      )
      .build()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    validator = createStubInstance(UpdateDynamicTableViewValidator)

    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
      Repository <DynamicTableView>
    )

    userDefaultDynamicTableViewRepository = createStubInstance<
      Repository<UserDefaultDynamicTableView>
    >(
      Repository<DynamicTable>
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new UpdateDynamicTableViewUseCase(
      stubDataSource(),
      authContext,
      validator,
      dynamicTableViewRepository,
      userDefaultDynamicTableViewRepository,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves(tableView)
  }

  it('Calls update', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder()
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      randomUUID(),
      command
    )

    assert.calledOnce(dynamicTableViewRepository.update)
  })

  it('Updates existing global default view when isGlobalDefault = true', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder()
      .withIsGlobal(true)
      .withIsGlobalDefault(true)
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      randomUUID(),
      command
    )

    assert.calledTwice(dynamicTableViewRepository.update)
  })

  it('Upserts user default view when isDefault = true', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder()
      .withIsDefault(true)
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      randomUUID(),
      command
    )

    assert.calledOnce(userDefaultDynamicTableViewRepository.upsert)
  })

  it('Deletes user default view when isDefault = false', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder()
      .withIsDefault(false)
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      randomUUID(),
      command
    )

    assert.calledOnce(userDefaultDynamicTableViewRepository.delete)
  })

  it('Emits a DynamicTableViewCreatedEvent event', async () => {
    const command = new UpdateDynamicTableViewCommandBuilder().build()

    const response = await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      tableView.uuid,
      command
    )

    expect(eventEmitter).toHaveEmitted(new DynamicTableViewUpdatedEvent(response.uuid))
  })
})
