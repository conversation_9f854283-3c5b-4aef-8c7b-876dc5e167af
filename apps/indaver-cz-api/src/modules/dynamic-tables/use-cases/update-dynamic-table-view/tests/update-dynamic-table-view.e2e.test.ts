import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { SortDirection } from '@wisemen/pagination'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { DynamicTableViewVisibleColumnsCommandBuilder } from '../../../tests/dynamic-table-view-visisble-columns.command.builder.js'
import { DynamicTableViewFiltersCommandBuilder } from '../../../tests/dynamic-table-view-filter.command.builder.js'
import { DynamicTableViewSortsCommandBuilder } from '../../../tests/dynamic-table-view-sorts.command.builder.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { UpdateDynamicTableViewCommandBuilder } from './update-dynamic-table-view.command.builder.js'

describe('Update dynamic table view e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableViewRepository: Repository<DynamicTableView>

  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableViewRepository = setup.dataSource.getRepository(DynamicTableView)

    authorizedUser = await setup.authContext.getUser([Permission.DYNAMIC_TABLE_VIEW_MANAGE])
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .patch(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 201 when authorized', async () => {
    const dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.WASTE_INQUIRY
      },
      relations: {
        columns: true
      }
    })

    const tableView = await dynamicTableViewRepository.save(
      new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(authorizedUser.user.uuid)
        .build()
    )

    const filterCommands = dynamicTable.columns!
      .filter(column => column.filterableField !== null)
      .map((column) => {
        return new DynamicTableViewFiltersCommandBuilder()
          .withColumnUuid(column.uuid)
          .withValue('Test')
          .build()
      })

    const sortCommands = dynamicTable.columns!
      .filter(column => column.sortableFields?.length !== 0)
      .map((column) => {
        return new DynamicTableViewSortsCommandBuilder()
          .withColumnUuid(column.uuid)
          .withDirection(SortDirection.ASC)
          .build()
      })

    const visibleColumnsCommands = dynamicTable.columns!
      .filter(column => !column.isHidable)
      .map((column) => {
        return new DynamicTableViewVisibleColumnsCommandBuilder()
          .withColumnUuid(column.uuid)
          .build()
      })

    const command = new UpdateDynamicTableViewCommandBuilder()
      .withFilters(filterCommands)
      .withSorts(sortCommands)
      .withVisibleColumns(visibleColumnsCommands)
      .build()

    const response = await request(setup.httpServer)
      .patch(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views/${tableView.uuid}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    expect(response).toHaveStatus(200)
    expect(response.body).toMatchObject({
      uuid: tableView.uuid,
      createdAt: tableView.createdAt.toISOString(),
      updatedAt: expect.any(String)
    })
  })
})
