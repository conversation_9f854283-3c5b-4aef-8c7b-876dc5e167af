import { DynamicTableViewVisibleColumnsCommand } from '../../../commands/dynamic-table-view-visible-columns.command.js'
import { DynamicTableViewSortCommand } from '../../../commands/dynamic-table-view-sort.command.js'
import { DynamicTableViewFilterCommand } from '../../../commands/dynamic-table-view-filter.command.js'
import { UpdateDynamicTableViewCommand } from '../update-dynamic-table-view.command.js'

export class UpdateDynamicTableViewCommandBuilder {
  private command: UpdateDynamicTableViewCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdateDynamicTableViewCommand()

    return this
  }

  withIsGlobalDefault (isGlobalDefault: boolean): this {
    this.command.isGlobalDefault = isGlobalDefault

    return this
  }

  withIsGlobal (isGlobal: boolean): this {
    this.command.isGlobal = isGlobal

    return this
  }

  withIsDefault (isDefault: boolean): this {
    this.command.isDefault = isDefault

    return this
  }

  withViewName (viewName: string): this {
    this.command.viewName = viewName

    return this
  }

  withVisibleColumns (visibleColumns: DynamicTableViewVisibleColumnsCommand[]): this {
    this.command.visibleColumns = visibleColumns

    return this
  }

  withSorts (sorts: DynamicTableViewSortCommand[]): this {
    this.command.sorts = sorts

    return this
  }

  withFilters (filters: DynamicTableViewFilterCommand[]): this {
    this.command.filters = filters

    return this
  }

  build (): UpdateDynamicTableViewCommand {
    const result = this.command

    this.reset()

    return result
  }
}
