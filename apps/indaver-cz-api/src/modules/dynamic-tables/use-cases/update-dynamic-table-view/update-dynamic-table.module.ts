import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Module } from '@nestjs/common'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { PermissionModule } from '../../../permission/permission.module.js'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { UpdateDynamicTableViewUseCase } from './update-dynamic-table-view.use-case.js'
import { UpdateDynamicTableViewController } from './update-dynamic-table.controller.js'
import { UpdateDynamicTableViewValidator } from './update-dynamic-table-view.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserDefaultDynamicTableView,
      DynamicTableColumn,
      DynamicTableView,
      User
    ]),
    DomainEventEmitterModule,
    PermissionModule
  ],
  controllers: [
    UpdateDynamicTableViewController
  ],
  providers: [
    UpdateDynamicTableViewUseCase,
    UpdateDynamicTableViewValidator,
    DynamicTableViewValidatorService
  ]
})
export class UpdateDynamicTableViewModule {}
