import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { UnauthorizedError } from '../../../exceptions/generic/unauthorized.error.js'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { UpdateDynamicTableViewCommand } from './update-dynamic-table-view.command.js'

@Injectable()
export class UpdateDynamicTableViewValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    private readonly validatorService: DynamicTableViewValidatorService
  ) {}

  async validate (
    tableName: DynamicTableName,
    dynamicTableViewUuid: string,
    command: UpdateDynamicTableViewCommand
  ): Promise<DynamicTableView> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const dynamicTableView = await this.getDynamicTableViewUuid(
      tableName,
      dynamicTableViewUuid
    )

    await this.canUserUpdateView(
      userUuid,
      dynamicTableView
    )

    await this.validateCommand(
      userUuid,
      command,
      dynamicTableView
    )

    return dynamicTableView
  }

  private async getDynamicTableViewUuid (
    tableName: DynamicTableName,
    dynamicTableViewUuid: string
  ): Promise<DynamicTableView> {
    return await this.dynamicTableViewRepository.findOneOrFail({
      where: {
        uuid: dynamicTableViewUuid,
        dynamicTable: {
          name: tableName
        }
      },
      relations: {
        dynamicTable: {
          columns: true
        }
      }
    })
  }

  private async canUserUpdateView (
    userUuid: string,
    dynamicTableView: DynamicTableView
  ): Promise<void> {
    await this.validatorService.validateGlobalView(
      userUuid,
      dynamicTableView.isGlobal,
      dynamicTableView.isDefaultGlobal
    )

    if (!dynamicTableView.isGlobal && dynamicTableView.createdByUserUuid !== userUuid) {
      throw new UnauthorizedError('error.dynamic-tables.unauthorized_update_view')
    }
  }

  private async validateCommand (
    userUuid: string,
    command: UpdateDynamicTableViewCommand,
    dynamicTableView: DynamicTableView
  ): Promise<void> {
    assert(dynamicTableView.dynamicTable !== undefined)

    if (command.visibleColumns !== undefined) {
      await this.validatorService.validateHiddenColumns(
        dynamicTableView.dynamicTable.uuid,
        command.visibleColumns.map(column => column.columnUuid)
      )
    }

    if (command.viewName !== undefined) {
      await this.validatorService.validateViewNameNonExistent(
        userUuid,
        command.viewName,
        dynamicTableView.dynamicTable.uuid,
        dynamicTableView.uuid
      )
    }

    if (command.isGlobal !== undefined || command.isGlobalDefault !== undefined) {
      await this.validatorService.validateGlobalView(
        userUuid,
        command.isGlobal ?? dynamicTableView.isGlobal,
        command.isGlobalDefault ?? dynamicTableView.isDefaultGlobal
      )
    }
  }
}
