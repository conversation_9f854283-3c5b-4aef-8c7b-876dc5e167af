import { <PERSON>, Controller, Param, ParseEnum<PERSON>ip<PERSON>, <PERSON> } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { ApiBadRequestErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ColumnNotFoundError } from '../../errors/column-not-found.error.js'
import { ColumnNotFilterableError } from '../../errors/column-not-filterable.error.js'
import { ColumnNotSortableError } from '../../errors/column-not-sortable.error.js'
import { DuplicateColumnError } from '../../errors/duplicate-column.error.js'
import { UpdateDynamicTableViewCommand } from './update-dynamic-table-view.command.js'
import { UpdateDynamicTableViewResponse } from './update-dynamic-table.response.js'
import { UpdateDynamicTableViewUseCase } from './update-dynamic-table-view.use-case.js'

@ApiTags('Dynamic table')
@Controller('dynamic-tables/:name/views/:uuid')
@ApiOAuth2([])
export class UpdateDynamicTableViewController {
  constructor (
    private readonly useCase: UpdateDynamicTableViewUseCase
  ) {}

  @Patch()
  @ApiOkResponse({ type: UpdateDynamicTableViewResponse })
  @ApiBadRequestErrorResponse(
    ColumnNotFoundError,
    ColumnNotFilterableError,
    ColumnNotSortableError,
    DuplicateColumnError
  )
  async updateDynamicTableView (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName,
    @UuidParam('uuid') dynamicTableViewUuid: string,
    @Body() command: UpdateDynamicTableViewCommand
  ): Promise<UpdateDynamicTableViewResponse> {
    return await this.useCase.execute(tableName, dynamicTableViewUuid, command)
  }
}
