import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, ValidateNested, IsArray, IsBoolean, IsNotEmpty, IsString } from 'class-validator'
import { Type } from 'class-transformer'
import { DynamicTableViewFilterCommand } from '../../commands/dynamic-table-view-filter.command.js'
import { DynamicTableViewSortCommand } from '../../commands/dynamic-table-view-sort.command.js'
import { DynamicTableViewVisibleColumnsCommand } from '../../commands/dynamic-table-view-visible-columns.command.js'

export class UpdateDynamicTableViewCommand {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  viewName?: string

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isGlobalDefault?: boolean

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean

  @ApiProperty({ type: DynamicTableViewFilterCommand, isArray: true, required: false })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewFilterCommand)
  @IsArray()
  @IsNotEmpty()
  @IsOptional()
  filters?: DynamicTableViewFilterCommand[]

  @ApiProperty({ type: DynamicTableViewSortCommand, isArray: true, required: false })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewSortCommand)
  @IsArray()
  @IsNotEmpty()
  @IsOptional()
  sorts?: DynamicTableViewSortCommand[]

  @ApiProperty({
    type: DynamicTableViewVisibleColumnsCommand,
    isArray: true,
    required: false
  })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewVisibleColumnsCommand)
  @IsArray()
  @IsNotEmpty()
  @IsOptional()
  visibleColumns?: DynamicTableViewVisibleColumnsCommand[]
}
