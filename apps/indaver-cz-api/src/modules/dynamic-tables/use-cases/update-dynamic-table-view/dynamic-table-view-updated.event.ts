import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { DynamicTableViewEvent } from '../../events/dynamic-table-view.event.js'

export class DynamicTableViewUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly dynamicTableViewUuid: string

  constructor (dynamicTableViewUuid: string) {
    this.dynamicTableViewUuid = dynamicTableViewUuid
  }
}

@RegisterDomainEvent(DomainEventType.DYNAMIC_TABLE_VIEW_UPDATED, 1)
export class DynamicTableViewUpdatedEvent
  extends DynamicTableViewEvent<DynamicTableViewUpdatedEventContent> {
  constructor (dynamicTableViewUuid: string) {
    super({
      dynamicTableViewUuid: dynamicTableViewUuid,
      content: new DynamicTableViewUpdatedEventContent(dynamicTableViewUuid)
    })
  }
}
