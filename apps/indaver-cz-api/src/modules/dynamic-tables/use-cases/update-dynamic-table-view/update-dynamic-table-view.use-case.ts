import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Not, Repository } from 'typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableViewConfiguration } from '../../helpers/dynamic-table-view-configuration.js'
import { CreateTableViewConfiguration } from '../../helpers/create-table-view-configuration.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { DynamicTableViewUpdatedEvent } from './dynamic-table-view-updated.event.js'
import { UpdateDynamicTableViewValidator } from './update-dynamic-table-view.validator.js'
import { UpdateDynamicTableViewCommand } from './update-dynamic-table-view.command.js'
import { UpdateDynamicTableViewResponse } from './update-dynamic-table.response.js'

@Injectable()
export class UpdateDynamicTableViewUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly validator: UpdateDynamicTableViewValidator,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    @InjectRepository(UserDefaultDynamicTableView)
    private readonly userDefaultDynamicTableViewRepository: Repository<UserDefaultDynamicTableView>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    tableName: DynamicTableName,
    uuid: string,
    command: UpdateDynamicTableViewCommand
  ): Promise<UpdateDynamicTableViewResponse> {
    const dynamicTableView = await this.validator.validate(
      tableName,
      uuid,
      command
    )
    const userUuid = this.authContext.getUserUuidOrFail()

    const columns = dynamicTableView.dynamicTable?.columns
    const dynamicTable = dynamicTableView.dynamicTable

    assert (dynamicTable !== undefined && columns !== undefined)

    const configuration = this.buildConfiguration(
      dynamicTableView,
      command,
      columns
    )

    await transaction(this.dataSource, async () => {
      if (command.isGlobalDefault === true) {
        await this.dynamicTableViewRepository.update({
          dynamicTableUuid: dynamicTable.uuid,
          isDefaultGlobal: true,
          uuid: Not(uuid)
        }, {
          isDefaultGlobal: false
        })
      }

      await this.dynamicTableViewRepository.update(dynamicTableView.uuid, {
        name: command.viewName,
        isGlobal: command.isGlobal,
        isDefaultGlobal: command.isGlobalDefault,
        configuration: configuration
      })

      if (command.isDefault !== undefined) {
        if (command.isDefault) {
          await this.userDefaultDynamicTableViewRepository.upsert(
            {
              userUuid: userUuid,
              dynamicTableViewUuid: dynamicTableView.uuid,
              dynamicTableUuid: dynamicTable.uuid
            },
            ['userUuid', 'dynamicTableUuid']
          )
        } else {
          await this.userDefaultDynamicTableViewRepository.delete({
            userUuid: userUuid,
            dynamicTableViewUuid: dynamicTableView.uuid
          })
        }
      }

      await this.eventEmitter.emit([new DynamicTableViewUpdatedEvent(dynamicTableView.uuid)])
    })

    return new UpdateDynamicTableViewResponse(dynamicTableView)
  }

  private buildConfiguration (
    dynamicTableView: DynamicTableView,
    command: UpdateDynamicTableViewCommand,
    columns: DynamicTableColumn[]
  ): DynamicTableViewConfiguration {
    return {
      filters: command.filters
        ? CreateTableViewConfiguration.createFilterConfigurations(command.filters, columns)
        : dynamicTableView.configuration.filters,

      sorting: command.sorts
        ? CreateTableViewConfiguration.createSortConfigurations(command.sorts, columns)
        : dynamicTableView.configuration.sorting,

      visibleColumns: command.visibleColumns
        ? CreateTableViewConfiguration.createVisibleColumnsConfigurations(
            command.visibleColumns,
            columns
          )
        : dynamicTableView.configuration.visibleColumns
    }
  }
}
