import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { DynamicTable } from '../../entities/dynamic-table.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { CreateDynamicTableViewCommand } from './create-dynamic-table-view.command.js'

@Injectable()
export class CreateDynamicTableViewValidator {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(DynamicTable)
    private readonly dynamicTableRepository: Repository<DynamicTable>,
    private readonly validatorService: DynamicTableViewValidatorService
  ) {}

  async validate (
    tableName: DynamicTableName,
    command: CreateDynamicTableViewCommand
  ): Promise<DynamicTable> {
    const userUuid = this.authContext.getUserUuidOrFail()

    await this.validatorService.validateGlobalView(
      userUuid,
      command.isGlobal,
      command.isGlobalDefault
    )

    const dynamicTable = await this.dynamicTableRepository.findOneOrFail({
      where: {
        name: tableName
      },
      relations: {
        columns: true
      }
    })

    await this.validatorService.validateViewNameNonExistent(
      userUuid,
      command.viewName,
      dynamicTable.uuid
    )

    const visibleUuids = command.visibleColumns.map(column => column.columnUuid)

    await this.validatorService.validateHiddenColumns(
      dynamicTable.uuid,
      visibleUuids
    )

    await this.validatorService.validateGlobalView(
      userUuid,
      command.isGlobal,
      command.isGlobalDefault
    )

    return dynamicTable
  }
}
