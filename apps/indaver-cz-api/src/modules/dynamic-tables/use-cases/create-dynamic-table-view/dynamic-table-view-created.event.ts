import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { DynamicTableViewEvent } from '../../events/dynamic-table-view.event.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'

export class DynamicTableViewCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly dynamicTableViewUuid: string

  constructor (dynamicTableViewUuid: string) {
    this.dynamicTableViewUuid = dynamicTableViewUuid
  }
}

@RegisterDomainEvent(DomainEventType.DYNAMIC_TABLE_VIEW_CREATED, 1)
export class DynamicTableViewCreatedEvent
  extends DynamicTableViewEvent<DynamicTableViewCreatedEventContent> {
  constructor (dynamicTableView: DynamicTableView) {
    super({
      dynamicTableViewUuid: dynamicTableView.uuid,
      content: new DynamicTableViewCreatedEventContent(dynamicTableView.uuid)
    })
  }
}
