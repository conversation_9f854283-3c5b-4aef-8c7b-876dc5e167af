import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DynamicTable } from '../../entities/dynamic-table.entity.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { User } from '../../../../app/users/entities/user.entity.js'
import { DynamicTableViewValidatorService } from '../../services/dynamic-table-view-validator.service.js'
import { PermissionModule } from '../../../permission/permission.module.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CreateDynamicTableViewController } from './create-dynamic-table-view.controller.js'
import { CreateDynamicTableViewUseCase } from './create-dynamic-table-view.use-case.js'
import { CreateDynamicTableViewValidator } from './create-dynamic-table-view.validator.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DynamicTable,
      DynamicTableColumn,
      DynamicTableView,
      UserDefaultDynamicTableView,
      User
    ]),
    DomainEventEmitterModule,
    PermissionModule
  ],
  controllers: [
    CreateDynamicTableViewController
  ],
  providers: [
    CreateDynamicTableViewUseCase,
    CreateDynamicTableViewValidator,
    DynamicTableViewValidatorService
  ]
})
export class CreateDynamicTableViewModule {}
