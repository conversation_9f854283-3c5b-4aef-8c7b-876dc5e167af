import { ApiProperty } from '@nestjs/swagger'
import { <PERSON><PERSON>rray, IsBoolean, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { DynamicTableViewFilterCommand } from '../../commands/dynamic-table-view-filter.command.js'
import { DynamicTableViewSortCommand } from '../../commands/dynamic-table-view-sort.command.js'
import { DynamicTableViewVisibleColumnsCommand } from '../../commands/dynamic-table-view-visible-columns.command.js'

export class CreateDynamicTableViewCommand {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  viewName: string

  @ApiProperty({ type: Boolean, required: false, description: 'Field only allowed for admins' })
  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean

  @ApiProperty({ type: <PERSON>olean, required: false, description: 'Field only allowed for admins' })
  @IsOptional()
  @IsBoolean()
  isGlobalDefault?: boolean

  @ApiProperty({ type: Boolean, required: false })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean

  @ApiProperty({ type: DynamicTableViewFilterCommand, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewFilterCommand)
  @IsArray()
  @IsNotEmpty()
  filters: DynamicTableViewFilterCommand[]

  @ApiProperty({ type: DynamicTableViewSortCommand, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewSortCommand)
  @IsArray()
  @IsNotEmpty()
  sorts: DynamicTableViewSortCommand[]

  @ApiProperty({ type: DynamicTableViewVisibleColumnsCommand, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => DynamicTableViewVisibleColumnsCommand)
  @IsArray()
  @IsNotEmpty()
  visibleColumns: DynamicTableViewVisibleColumnsCommand[]
}
