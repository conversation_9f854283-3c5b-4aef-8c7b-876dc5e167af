import { ApiProperty } from '@nestjs/swagger'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'

export class CreateDynamicTableViewResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  constructor (dynamicTableView: DynamicTableView) {
    this.uuid = dynamicTableView.uuid
    this.createdAt = dynamicTableView.createdAt.toISOString()
    this.updatedAt = dynamicTableView.updatedAt.toISOString()
  }
}
