import { <PERSON>, Post, Body, Param, ParseEnumPipe } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { ApiBadRequestErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { DuplicateColumnError } from '../../errors/duplicate-column.error.js'
import { ColumnNotFoundError } from '../../errors/column-not-found.error.js'
import { ColumnNotFilterableError } from '../../errors/column-not-filterable.error.js'
import { ColumnNotSortableError } from '../../errors/column-not-sortable.error.js'
import { CreateDynamicTableViewResponse } from './create-dynamic-table-view.response.js'
import { CreateDynamicTableViewCommand } from './create-dynamic-table-view.command.js'
import { CreateDynamicTableViewUseCase } from './create-dynamic-table-view.use-case.js'

@ApiTags('Dynamic table')
@Controller('dynamic-tables/:name/views')
@ApiOAuth2([])
export class CreateDynamicTableViewController {
  constructor (
    private readonly useCase: CreateDynamicTableViewUseCase
  ) {}

  @Post()
  @ApiCreatedResponse({ type: CreateDynamicTableViewResponse })
  @ApiBadRequestErrorResponse(
    ColumnNotFoundError,
    ColumnNotFilterableError,
    ColumnNotSortableError,
    DuplicateColumnError
  )
  async createDynamicTableView (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName,
    @Body() createDynamicTableViewCommand: CreateDynamicTableViewCommand
  ): Promise<CreateDynamicTableViewResponse> {
    return await this.useCase.execute(
      tableName,
      createDynamicTableViewCommand
    )
  }
}
