import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CreateDynamicTableViewValidator } from '../create-dynamic-table-view.validator.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewValidatorService } from '../../../services/dynamic-table-view-validator.service.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { CreateDynamicTableViewCommandBuilder } from './create-dynamic-table-view.command.builder.js'

describe('Create dynamic table view validator unit test', () => {
  let validator: CreateDynamicTableViewValidator

  let userUuid: string

  let authContext: SinonStubbedInstance<AuthContext>
  let dynamicTableRepository: SinonStubbedInstance<Repository<DynamicTable>>
  let validatorService: SinonStubbedInstance<DynamicTableViewValidatorService>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    authContext = createStubInstance(AuthContext)
    dynamicTableRepository = createStubInstance<Repository<DynamicTable>>(
          Repository<DynamicTableView>
    )
    validatorService = createStubInstance(DynamicTableViewValidatorService)

    validator = new CreateDynamicTableViewValidator(
      authContext,
      dynamicTableRepository,
      validatorService
    )

    mockMethods()
  })

  afterEach(() => {
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.returns(userUuid)
    dynamicTableRepository.findOneOrFail.resolves(new DynamicTableEntityBuilder().build())
  }

  it('Calls all the validators', async () => {
    const command = new CreateDynamicTableViewCommandBuilder()
      .withIsGlobal(true)
      .withIsGlobalDefault(true)
      .build()

    await validator.validate(
      DynamicTableName.WASTE_INQUIRY,
      command
    )

    assert.calledOnce(validatorService.validateHiddenColumns)
    assert.calledOnce(validatorService.validateViewNameNonExistent)
    assert.calledTwice(validatorService.validateGlobalView)
  })
})
