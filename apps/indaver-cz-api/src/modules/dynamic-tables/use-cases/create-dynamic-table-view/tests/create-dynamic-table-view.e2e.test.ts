import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { SortDirection } from '@wisemen/pagination'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { DynamicTableColumn } from '../../../entities/dynamic-table-column.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { WasteInquiryColumnName } from '../../../enums/waste-inquiry-column-name.enum.js'
import { DynamicTableColumnEntityBuilder } from '../../../tests/dynamic-table-column-enitity.builder.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { DynamicTableViewVisibleColumnsCommandBuilder } from '../../../tests/dynamic-table-view-visisble-columns.command.builder.js'
import { DynamicTableViewFiltersCommandBuilder } from '../../../tests/dynamic-table-view-filter.command.builder.js'
import { DynamicTableViewSortsCommandBuilder } from '../../../tests/dynamic-table-view-sorts.command.builder.js'
import { CreateDynamicTableViewCommandBuilder } from './create-dynamic-table-view.command.builder.js'

describe('Create dynamic table view e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableColumnRepository: Repository<DynamicTableColumn>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableColumnRepository = setup.dataSource.getRepository(DynamicTableColumn)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      setup.authContext.getUser([]),
      setup.authContext.getUser([Permission.DYNAMIC_TABLE_VIEW_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views`)

    expect(response).toHaveStatus(401)
  })

  it('returns 401 when creating global view unauthorized', async () => {
    const command = new CreateDynamicTableViewCommandBuilder()
      .withIsGlobal(true)
      .build()

    const response = await request(setup.httpServer)
      .post(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)
      .send(command)

    expect(response).toHaveStatus(401)
  })

  it('returns 201 when authorized', async () => {
    const dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.PICK_UP_REQUEST
      }
    })

    const dynamicColumn = new DynamicTableColumnEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withName(WasteInquiryColumnName.WASTE_STREAM_NAME)
      .withFilterableFields(WasteInquiryColumnName.WASTE_STREAM_NAME)
      .withSortableFields([WasteInquiryColumnName.WASTE_STREAM_NAME])
      .build()

    await dynamicTableColumnRepository.save(dynamicColumn)

    const filterCommand = new DynamicTableViewFiltersCommandBuilder()
      .withColumnUuid(dynamicColumn.uuid)
      .withValue('Test')
      .build()

    const sortCommand = new DynamicTableViewSortsCommandBuilder()
      .withColumnUuid(dynamicColumn.uuid)
      .withDirection(SortDirection.ASC)
      .build()

    const visibleColumnsCommand = new DynamicTableViewVisibleColumnsCommandBuilder()
      .withColumnUuid(dynamicColumn.uuid)
      .build()

    const command = new CreateDynamicTableViewCommandBuilder()
      .withIsGlobal(true)
      .withFilters([filterCommand])
      .withSorts([sortCommand])
      .withVisibleColumns([visibleColumnsCommand])
      .build()

    const response = await request(setup.httpServer)
      .post(`/dynamic-tables/${DynamicTableName.PICK_UP_REQUEST}/views`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body).toMatchObject({
      uuid: expect.uuid(),
      createdAt: expect.any(String),
      updatedAt: expect.any(String)
    })
  })
})
