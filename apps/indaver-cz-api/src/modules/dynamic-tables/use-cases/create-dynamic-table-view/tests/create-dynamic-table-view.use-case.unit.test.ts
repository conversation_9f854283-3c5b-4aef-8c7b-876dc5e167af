import { before, afterEach, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { CreateDynamicTableViewUseCase } from '../create-dynamic-table-view.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { CreateDynamicTableViewValidator } from '../create-dynamic-table-view.validator.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTableViewCreatedEvent } from '../dynamic-table-view-created.event.js'
import { DynamicTableEntityBuilder } from '../../../tests/dynamic-table-entity.builder.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { CreateDynamicTableViewCommandBuilder } from './create-dynamic-table-view.command.builder.js'

describe('Create dynamic table view use case unit test', () => {
  let useCase: CreateDynamicTableViewUseCase

  let userUuid: string
  let dynamicTable: DynamicTable

  let authContext: SinonStubbedInstance<AuthContext>
  let validator: SinonStubbedInstance<CreateDynamicTableViewValidator>
  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let userDefaultDynamicTableViewRepository: SinonStubbedInstance<
    Repository<UserDefaultDynamicTableView>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()
    dynamicTable = new DynamicTableEntityBuilder()
      .withColumns([])
      .build()

    authContext = createStubInstance(AuthContext)

    validator = createStubInstance(CreateDynamicTableViewValidator)

    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
      Repository <DynamicTableView>
    )

    userDefaultDynamicTableViewRepository = createStubInstance<Repository<
      UserDefaultDynamicTableView>>(
      Repository<DynamicTable>
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new CreateDynamicTableViewUseCase(
      stubDataSource(),
      authContext,
      validator,
      dynamicTableViewRepository,
      userDefaultDynamicTableViewRepository,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    authContext.getUserUuidOrFail.returns(userUuid)
    validator.validate.resolves(dynamicTable)
    dynamicTableViewRepository.create.returns(new DynamicTableViewEntityBuilder().build())
  }

  it('Calls create & save', async () => {
    const command = new CreateDynamicTableViewCommandBuilder().build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      command
    )

    assert.calledOnce(dynamicTableViewRepository.create)
    assert.calledOnce(dynamicTableViewRepository.save)
  })

  it('Updates existing global default view when isGlobalDefault = true', async () => {
    const command = new CreateDynamicTableViewCommandBuilder()
      .withIsGlobal(true)
      .withIsGlobalDefault(true)
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      command
    )

    assert.calledOnce(dynamicTableViewRepository.update)
  })

  it('Upserts user default view when isDefault = true', async () => {
    const command = new CreateDynamicTableViewCommandBuilder()
      .withIsDefault(true)
      .build()

    await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      command
    )

    assert.calledOnce(userDefaultDynamicTableViewRepository.upsert)
  })

  it('Emits a DynamicTableViewCreatedEvent event', async () => {
    const command = new CreateDynamicTableViewCommandBuilder().build()

    const result = await useCase.execute(
      DynamicTableName.WASTE_INQUIRY,
      command
    )

    const expectedDynamicTableView = new DynamicTableViewEntityBuilder()
      .withUuid(result.uuid)
      .withName(command.viewName)
      .build()

    expect(eventEmitter).toHaveEmitted(new DynamicTableViewCreatedEvent(expectedDynamicTableView))
  })
})
