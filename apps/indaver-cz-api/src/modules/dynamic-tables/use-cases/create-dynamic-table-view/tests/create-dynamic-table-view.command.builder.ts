import { CreateDynamicTableViewCommand } from '../create-dynamic-table-view.command.js'
import { DynamicTableViewVisibleColumnsCommand } from '../../../commands/dynamic-table-view-visible-columns.command.js'
import { DynamicTableViewSortCommand } from '../../../commands/dynamic-table-view-sort.command.js'
import { DynamicTableViewFilterCommand } from '../../../commands/dynamic-table-view-filter.command.js'

export class CreateDynamicTableViewCommandBuilder {
  private command: CreateDynamicTableViewCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new CreateDynamicTableViewCommand()
    this.command.viewName = 'Test'
    this.command.filters = []
    this.command.sorts = []
    this.command.visibleColumns = []

    return this
  }

  withIsGlobalDefault (isGlobalDefault: boolean): this {
    this.command.isGlobalDefault = isGlobalDefault

    return this
  }

  withIsGlobal (isGlobal: boolean): this {
    this.command.isGlobal = isGlobal

    return this
  }

  withIsDefault (isDefault: boolean): this {
    this.command.isDefault = isDefault

    return this
  }

  withViewName (viewName: string): this {
    this.command.viewName = viewName

    return this
  }

  withVisibleColumns (visibleColumns: DynamicTableViewVisibleColumnsCommand[]): this {
    this.command.visibleColumns = visibleColumns

    return this
  }

  withSorts (sorts: DynamicTableViewSortCommand[]): this {
    this.command.sorts = sorts

    return this
  }

  withFilters (filters: DynamicTableViewFilterCommand[]): this {
    this.command.filters = filters

    return this
  }

  build (): CreateDynamicTableViewCommand {
    const result = this.command

    this.reset()

    return result
  }
}
