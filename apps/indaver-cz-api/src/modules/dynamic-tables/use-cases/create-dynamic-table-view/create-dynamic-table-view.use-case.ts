import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewConfiguration } from '../../helpers/dynamic-table-view-configuration.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { CreateTableViewConfiguration } from '../../helpers/create-table-view-configuration.js'
import { DynamicTableColumn } from '../../entities/dynamic-table-column.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { DynamicTableViewCreatedEvent } from './dynamic-table-view-created.event.js'
import { CreateDynamicTableViewValidator } from './create-dynamic-table-view.validator.js'
import { CreateDynamicTableViewResponse } from './create-dynamic-table-view.response.js'
import { CreateDynamicTableViewCommand } from './create-dynamic-table-view.command.js'

@Injectable()
export class CreateDynamicTableViewUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    private readonly validator: CreateDynamicTableViewValidator,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>,
    @InjectRepository(UserDefaultDynamicTableView)
    private readonly userDefaultDynamicTableViewRepository: Repository<UserDefaultDynamicTableView>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    tableName: DynamicTableName,
    command: CreateDynamicTableViewCommand
  ): Promise<CreateDynamicTableViewResponse> {
    const dynamicTable = await this.validator.validate(
      tableName,
      command
    )

    const userUuid = this.authContext.getUserUuidOrFail()

    const columns = dynamicTable.columns

    assert(columns !== undefined)

    const configuration = this.buildConfiguration(command, columns)
    const dynamicTableView = this.dynamicTableViewRepository.create({
      name: command.viewName,
      isDefaultGlobal: command.isGlobalDefault,
      isGlobal: command.isGlobal,
      configuration,
      dynamicTableUuid: dynamicTable.uuid,
      createdByUserUuid: userUuid
    })

    await transaction(this.dataSource, async () => {
      if (command.isGlobalDefault === true) {
        await this.dynamicTableViewRepository.update({
          dynamicTableUuid: dynamicTable.uuid,
          isDefaultGlobal: true
        }, {
          isDefaultGlobal: false
        })
      }

      await this.dynamicTableViewRepository.save(dynamicTableView)

      if (command.isDefault !== undefined && command.isDefault) {
        await this.userDefaultDynamicTableViewRepository.upsert(
          {
            userUuid: userUuid,
            dynamicTableViewUuid: dynamicTableView.uuid,
            dynamicTableUuid: dynamicTable.uuid
          },
          ['userUuid', 'dynamicTableUuid']
        )
      }

      await this.eventEmitter.emit([new DynamicTableViewCreatedEvent(dynamicTableView)])
    })

    return new CreateDynamicTableViewResponse(dynamicTableView)
  }

  private buildConfiguration (
    command: CreateDynamicTableViewCommand,
    columns: DynamicTableColumn[]
  ): DynamicTableViewConfiguration {
    return {
      filters: CreateTableViewConfiguration.createFilterConfigurations(
        command.filters,
        columns
      ),
      sorting: CreateTableViewConfiguration.createSortConfigurations(
        command.sorts,
        columns
      ),
      visibleColumns: CreateTableViewConfiguration.createVisibleColumnsConfigurations(
        command.visibleColumns,
        columns
      )
    }
  }
}
