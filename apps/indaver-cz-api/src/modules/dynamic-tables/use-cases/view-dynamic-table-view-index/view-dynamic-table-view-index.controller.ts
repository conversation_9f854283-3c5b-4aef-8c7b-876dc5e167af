import { <PERSON>, Get, Param, ParseEnumPipe, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { DynamicTableViewIndexResponse } from './view-dynamic-table-view-index.response.js'
import { ViewDynamicTableViewIndexQuery } from './view-dynamic-table-view-index.query.js'
import { ViewDynamicTableViewIndexUseCase } from './view-dynamic-table-view-index.use-case.js'

@ApiTags('Dynamic table')
@ApiOAuth2([])
@Controller('dynamic-tables/:name/views')
export class ViewDynamicTableViewIndexController {
  constructor (
    private readonly useCase: ViewDynamicTableViewIndexUseCase
  ) {}

  @Get()
  @ApiOkResponse({ type: DynamicTableViewIndexResponse })
  public async viewDynamicTableColumnIndex (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName,
    @Query() query: ViewDynamicTableViewIndexQuery
  ): Promise<DynamicTableViewIndexResponse> {
    return await this.useCase.execute(tableName, query)
  }
}
