import { Injectable } from '@nestjs/common'
import { Repository } from 'typeorm'
import { typeormPagination } from '@wisemen/pagination'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { ViewDynamicTableViewIndexQuery } from './view-dynamic-table-view-index.query.js'

@Injectable()
export class ViewDynamicTableViewIndexRepository {
  constructor (
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>
  ) {}

  async getDynamicTableViewIndex (
    userUuid: string,
    tableName: DynamicTableName,
    query: ViewDynamicTableViewIndexQuery
  ): Promise<[DynamicTableView[], number]> {
    const pagination = typeormPagination(query.pagination)

    const fromAndWhereClause = `FROM dynamic_table_view
        INNER JOIN dynamic_table ON dynamic_table.uuid = dynamic_table_view.dynamic_table_uuid
        LEFT JOIN user_default_dynamic_table_view ON user_default_dynamic_table_view.dynamic_table_view_uuid = dynamic_table_view.uuid
        AND user_default_dynamic_table_view.user_uuid = $1
      WHERE
      (
        dynamic_table_view.is_global = TRUE
        OR dynamic_table_view.created_by_user_uuid = $1
      )
      AND dynamic_table.name = $2`

    const tableViews: DynamicTableView[] = await this.dynamicTableViewRepository.query(`
      SELECT
        dynamic_table_view.uuid AS "uuid",
        dynamic_table_view.created_at AS "createdAt",
        dynamic_table_view.updated_at AS "updatedAt",
        dynamic_table_view.name AS "name",
        dynamic_table_view.is_global AS "isGlobal",
        dynamic_table_view.is_default_global AS "isDefaultGlobal",
        dynamic_table_view.configuration AS "configuration",
        dynamic_table_view.dynamic_table_uuid AS "dynamicTableUuid",
        dynamic_table_view.created_by_user_uuid AS "createdByUserUuid",
        CASE
          WHEN user_default_dynamic_table_view.dynamic_table_view_uuid IS NOT NULL THEN TRUE
          ELSE FALSE
        END AS "isUserDefault"
      ${fromAndWhereClause}
      ORDER BY
        "isGlobal" DESC,
        "isUserDefault" DESC,
        "isDefaultGlobal" DESC
      LIMIT $3 OFFSET $4
    `, [userUuid, tableName, pagination.take, pagination.skip])

    const countResult: { count: string }[] = await this.dynamicTableViewRepository.query(`
      SELECT
        COUNT(*) AS "count"
      ${fromAndWhereClause}
    `, [userUuid, tableName])

    return [tableViews, parseInt(countResult[0].count)]
  }
}
