import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { ViewDynamicTableViewIndexController } from './view-dynamic-table-view-index.controller.js'
import { ViewDynamicTableViewIndexUseCase } from './view-dynamic-table-view-index.use-case.js'
import { ViewDynamicTableViewIndexRepository } from './view-dynamic-table-view-index.repository.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DynamicTableView,
      UserDefaultDynamicTableView
    ])
  ],
  controllers: [
    ViewDynamicTableViewIndexController
  ],
  providers: [
    ViewDynamicTableViewIndexUseCase,
    ViewDynamicTableViewIndexRepository
  ]
})
export class ViewDynamicTableViewIndexModule {}
