import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DynamicTableViewIndexResponse } from './view-dynamic-table-view-index.response.js'
import { ViewDynamicTableViewIndexQuery } from './view-dynamic-table-view-index.query.js'
import { ViewDynamicTableViewIndexRepository } from './view-dynamic-table-view-index.repository.js'

@Injectable()
export class ViewDynamicTableViewIndexUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly repository: ViewDynamicTableViewIndexRepository
  ) {}

  async execute (
    tableName: DynamicTableName,
    query: ViewDynamicTableViewIndexQuery
  ): Promise<DynamicTableViewIndexResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const pagination = typeormPagination(query.pagination)

    const [tableViews, count] = await this.repository.getDynamicTableViewIndex(
      userUuid,
      tableName,
      query
    )

    return new DynamicTableViewIndexResponse(
      tableViews,
      count,
      pagination.take,
      pagination.skip
    )
  }
}
