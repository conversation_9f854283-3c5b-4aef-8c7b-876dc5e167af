import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { ViewDynamicTableViewIndexQueryBuilder } from './view-dynamic-table-view-index-query.builder.js'

describe('View dynamic table view index e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableViewRepository: Repository<DynamicTableView>

  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableViewRepository = setup.dataSource.getRepository(DynamicTableView)
    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)

    await dynamicTableViewRepository.deleteAll()

    authorizedUser = await setup.authContext.getUser([])
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/views`)

    expect(response).toHaveStatus(401)
  })

  it('returns dynamic table views', async () => {
    const dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.WASTE_INQUIRY
      },
      relations: {
        columns: true
      }
    })

    await dynamicTableViewRepository.save(
      new DynamicTableViewEntityBuilder()
        .withCreatedByUserUuid(authorizedUser.user.uuid)
        .withDynamicTableUuid(dynamicTable.uuid)
        .withVisibleColumns(dynamicTable.columns!.map((column, index) => {
          return {
            uuid: column.uuid,
            order: index
          }
        }))
        .build()
    )

    const query = new ViewDynamicTableViewIndexQueryBuilder()
      .build()

    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${dynamicTable.name}/views`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .query(
        query
      )

    expect(response).toHaveStatus(200)
    expect(response.body.items.length).toBe(1)
  })
})
