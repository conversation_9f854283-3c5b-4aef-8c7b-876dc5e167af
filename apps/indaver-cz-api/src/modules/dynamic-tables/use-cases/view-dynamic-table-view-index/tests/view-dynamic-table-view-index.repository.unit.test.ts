import { before, after, beforeEach, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewDynamicTableViewIndexRepository } from '../view-dynamic-table-view-index.repository.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { RepositoryTestSetup } from '../../../../../../test/setup/repository-test-setup.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { User } from '../../../../../app/users/entities/user.entity.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { UserDefaultDynamicTableViewEntityBuilder } from '../../../tests/user-default-table-view-entity.builder.js'

describe('View dynamic table view index repository unit test', () => {
  let setup: RepositoryTestSetup

  let repository: ViewDynamicTableViewIndexRepository

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableViewRepository: Repository<DynamicTableView>
  let userDefaultDynamicTableViewRepository: Repository<UserDefaultDynamicTableView>

  let user: User
  let dynamicTable: DynamicTable

  before(async () => {
    setup = await TestBench.setupRepositoryTest()

    const userRepository = setup.dataSource.getRepository(User)

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableViewRepository = setup.dataSource.getRepository(DynamicTableView)
    userDefaultDynamicTableViewRepository = setup.dataSource.getRepository(
      UserDefaultDynamicTableView
    )

    repository = new ViewDynamicTableViewIndexRepository(
      dynamicTableViewRepository
    )

    user = await userRepository.save(new UserEntityBuilder().build())
    dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.WASTE_INQUIRY
      }
    })
  })

  after(async () => await setup.teardown())

  beforeEach(async () => {
    await userDefaultDynamicTableViewRepository.deleteAll()
    await dynamicTableViewRepository.deleteAll()
  })

  it('Returns global dynamic table views', async () => {
    const globalView = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(user.uuid)
      .withIsGlobal(true)
      .build()
    const privateView = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(user.uuid)
      .withIsGlobal(false)
      .build()

    await dynamicTableViewRepository.save([globalView, privateView])

    const [tableViews, amount] = await repository.getDynamicTableViewIndex(
      randomUUID(),
      DynamicTableName.WASTE_INQUIRY,
      {}
    )

    expect(tableViews[0].uuid).toBe(globalView.uuid)
    expect(amount).toBe(1)
  })

  it('Returns private dynamic table views of the user', async () => {
    const otherUser = await setup.dataSource.getRepository(User).save(
      new UserEntityBuilder().build()
    )

    const privateViewOfUser = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(user.uuid)
      .withIsGlobal(false)
      .build()
    const privateViewOfOther = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(otherUser.uuid)
      .withIsGlobal(false)
      .build()

    await dynamicTableViewRepository.save([privateViewOfUser, privateViewOfOther])

    const [tableViews, amount] = await repository.getDynamicTableViewIndex(
      user.uuid,
      DynamicTableName.WASTE_INQUIRY,
      {}
    )

    expect(tableViews[0].uuid).toBe(privateViewOfUser.uuid)
    expect(amount).toBe(1)
  })

  it('Returns dynamic table views with indication of is user\'s default', async () => {
    const globalView1 = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(user.uuid)
      .withIsGlobal(true)
      .build()
    const globalView2 = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(user.uuid)
      .withIsGlobal(true)
      .build()

    await dynamicTableViewRepository.save([globalView1, globalView2])
    await userDefaultDynamicTableViewRepository.save(
      new UserDefaultDynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withDynamicTableViewUuid(globalView1.uuid)
        .withUserUuid(user.uuid)
        .build()
    )

    const [tableViews, amount] = await repository.getDynamicTableViewIndex(
      user.uuid,
      DynamicTableName.WASTE_INQUIRY,
      {}
    )

    expect(tableViews[0].uuid).toBe(globalView1.uuid)
    expect(tableViews[0].isUserDefault).toBe(true)
    expect(tableViews[1].uuid).toBe(globalView2.uuid)
    expect(tableViews[1].isUserDefault).toBe(false)
    expect(amount).toBe(2)
  })

  describe('order', () => {
    it('Returns global views before private views', async () => {
      const privateView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(false)
        .build()
      const globalView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(true)
        .build()

      await dynamicTableViewRepository.save([privateView, globalView])

      const [tableViews, amount] = await repository.getDynamicTableViewIndex(
        user.uuid,
        DynamicTableName.WASTE_INQUIRY,
        {}
      )

      expect(tableViews[0].uuid).toBe(globalView.uuid)
      expect(tableViews[1].uuid).toBe(privateView.uuid)
      expect(amount).toBe(2)
    })

    it('Returns user default view before other private views', async () => {
      const otherView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(false)
        .build()
      const userDefaultView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(false)
        .build()

      await dynamicTableViewRepository.save([otherView, userDefaultView])
      await userDefaultDynamicTableViewRepository.save(
        new UserDefaultDynamicTableViewEntityBuilder()
          .withDynamicTableUuid(dynamicTable.uuid)
          .withDynamicTableViewUuid(userDefaultView.uuid)
          .withUserUuid(user.uuid)
          .build()
      )

      const [tableViews, amount] = await repository.getDynamicTableViewIndex(
        user.uuid,
        DynamicTableName.WASTE_INQUIRY,
        {}
      )

      expect(tableViews[0].uuid).toBe(userDefaultView.uuid)
      expect(tableViews[1].uuid).toBe(otherView.uuid)
      expect(amount).toBe(2)
    })

    it('Returns default global views before other global views', async () => {
      const otherGlobalView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(true)
        .build()
      const defaultGlobalView = new DynamicTableViewEntityBuilder()
        .withDynamicTableUuid(dynamicTable.uuid)
        .withCreatedByUserUuid(user.uuid)
        .withIsGlobal(true)
        .withIsDefaultGlobal(true)
        .build()

      await dynamicTableViewRepository.save([otherGlobalView, defaultGlobalView])

      const [tableViews, amount] = await repository.getDynamicTableViewIndex(
        user.uuid,
        DynamicTableName.WASTE_INQUIRY,
        {}
      )

      expect(tableViews[0].uuid).toBe(defaultGlobalView.uuid)
      expect(tableViews[1].uuid).toBe(otherGlobalView.uuid)
      expect(amount).toBe(2)
    })
  })
})
