import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { ViewDynamicTableViewIndexUseCase } from '../view-dynamic-table-view-index.use-case.js'
import { ViewDynamicTableViewIndexRepository } from '../view-dynamic-table-view-index.repository.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { AuthContext } from '../../../../auth/auth.context.js'

describe('View dynamic table view index use-case unit test', () => {
  let useCase: ViewDynamicTableViewIndexUseCase

  let repository: SinonStubbedInstance<ViewDynamicTableViewIndexRepository>

  before(() => {
    TestBench.setupUnitTest()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: randomUUID()
    })

    repository = createStubInstance(ViewDynamicTableViewIndexRepository, {
      getDynamicTableViewIndex: Promise.resolve([[], 0])
    })

    useCase = new ViewDynamicTableViewIndexUseCase(
      authContext,
      repository
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(DynamicTableName.WASTE_INQUIRY, {})

    assert.calledOnce(repository.getDynamicTableViewIndex)
  })
})
