import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { SortDirection } from '@wisemen/pagination'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { DynamicTableColumn } from '../../../entities/dynamic-table-column.entity.js'
import { DynamicTable } from '../../../entities/dynamic-table.entity.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { UserDefaultDynamicTableViewEntityBuilder } from '../../../tests/user-default-table-view-entity.builder.js'
import { PickUpRequestColumnName } from '../../../enums/pick-up-request-column-name.enum.js'

describe('View default dynamic table view e2e test', () => {
  let setup: EndToEndTestSetup

  let dynamicTableRepository: Repository<DynamicTable>
  let dynamicTableColumnRepository: Repository<DynamicTableColumn>
  let dynamicTableViewRepository: Repository<DynamicTableView>
  let userDefaultDynamicTableViewRepository: Repository<UserDefaultDynamicTableView>

  let authorizedUser: TestUser

  let sortableColumn: DynamicTableColumn
  let filterableColumn: DynamicTableColumn
  let dynamicTable: DynamicTable

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    dynamicTableRepository = setup.dataSource.getRepository(DynamicTable)
    dynamicTableColumnRepository = setup.dataSource.getRepository(DynamicTableColumn)
    dynamicTableViewRepository = setup.dataSource.getRepository(DynamicTableView)
    userDefaultDynamicTableViewRepository = setup.dataSource
      .getRepository(UserDefaultDynamicTableView)

    authorizedUser = await setup.authContext.getUser([])

    dynamicTable = await dynamicTableRepository.findOneOrFail({
      where: {
        name: DynamicTableName.PICK_UP_REQUEST
      },
      relations: {
        columns: true
      }
    })

    const _filterableColumn = dynamicTable.columns!.find(column => column.filterableField !== null)
    if (_filterableColumn === undefined) {
      await dynamicTableColumnRepository.update(
        { name: PickUpRequestColumnName.STATUS },
        { filterableField: 'status' }
      )
      filterableColumn = dynamicTable.columns!.find(
        column => column.name === PickUpRequestColumnName.STATUS
      )!
    } else {
      filterableColumn = _filterableColumn
    }

    const _sortableColumn = dynamicTable.columns!.find(column => column.sortableFields.length > 0)
    if (_sortableColumn === undefined) {
      await dynamicTableColumnRepository.update(
        { name: PickUpRequestColumnName.REQUEST_NUMBER },
        { sortableFields: ['requestNumber'] }
      )
      sortableColumn = dynamicTable.columns!.find(
        column => column.name === PickUpRequestColumnName.REQUEST_NUMBER
      )!
    } else {
      sortableColumn = _sortableColumn
    }
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${DynamicTableName.WASTE_INQUIRY}/default-view`)

    expect(response).toHaveStatus(401)
  })

  it('returns 200 when authorized and has a default view', async () => {
    const tableView = new DynamicTableViewEntityBuilder()
      .withDynamicTableUuid(dynamicTable.uuid)
      .withCreatedByUserUuid(authorizedUser.user.uuid)
      .withFilters([{ uuid: filterableColumn.uuid, value: 'Test' }])
      .withSorts([{ uuid: sortableColumn.uuid, order: 1, direction: SortDirection.ASC }])
      .build()

    tableView.isUserDefault = true

    await dynamicTableViewRepository.save(
      tableView
    )

    await userDefaultDynamicTableViewRepository.save(
      new UserDefaultDynamicTableViewEntityBuilder()
        .withUserUuid(authorizedUser.user.uuid)
        .withDynamicTableUuid(dynamicTable.uuid)
        .withDynamicTableViewUuid(tableView.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .get(`/dynamic-tables/${DynamicTableName.PICK_UP_REQUEST}/default-view`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body).toMatchObject({
      uuid: tableView.uuid,
      createdAt: tableView.createdAt.toISOString(),
      updatedAt: tableView.updatedAt.toISOString(),
      name: tableView.name,
      isGlobal: tableView.isGlobal,
      isDefaultGlobal: tableView.isDefaultGlobal,
      isUserDefault: tableView.isUserDefault,
      visibleColumns: tableView.configuration.visibleColumns,
      sorts: tableView.configuration.sorting,
      filters: tableView.configuration.filters
    })
  })
})
