import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { DynamicTableView } from '../../../entities/dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from '../../../entities/user-default-dynamic-table-view.entity.js'
import { ViewDefaultDynamicTableViewUseCase } from '../view-default-dynamic-table-view.use-case.js'
import { UserDefaultDynamicTableViewEntityBuilder } from '../../../tests/user-default-table-view-entity.builder.js'
import { DynamicTableViewEntityBuilder } from '../../../tests/dynamic-table-view-entity.builder.js'
import { DynamicTableName } from '../../../enums/dynamic-table-name.enum.js'
import { AuthContext } from '../../../../auth/auth.context.js'

describe('View default dynamic table use-case unit test', () => {
  let useCase: ViewDefaultDynamicTableViewUseCase

  let dynamicTableViewRepository: SinonStubbedInstance<Repository<DynamicTableView>>
  let userDefaultDynamicTableViewRepository: SinonStubbedInstance<
    Repository<UserDefaultDynamicTableView>
  >

  before(() => {
    TestBench.setupUnitTest()

    const userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    dynamicTableViewRepository = createStubInstance<Repository<DynamicTableView>>(
      Repository<DynamicTableView>, {
        findOneByOrFail: Promise.resolve(
          new DynamicTableViewEntityBuilder()
            .build()
        )
      }
    )

    userDefaultDynamicTableViewRepository = createStubInstance<
      Repository<UserDefaultDynamicTableView>>(
      Repository<UserDefaultDynamicTableView>, {
        findOne: Promise.resolve(
          new UserDefaultDynamicTableViewEntityBuilder()
            .withDynamicTableView(
              new DynamicTableViewEntityBuilder().build()
            )
            .build()
        )
      }
    )

    useCase = new ViewDefaultDynamicTableViewUseCase(
      authContext,
      userDefaultDynamicTableViewRepository,
      dynamicTableViewRepository
    )
  })

  afterEach(() => {
    Sinon.resetHistory()
  })

  it('Calls userDefaultDynamicTableViewRepository once for user with default view', async () => {
    await useCase.execute(DynamicTableName.WASTE_INQUIRY)

    assert.calledOnce(userDefaultDynamicTableViewRepository.findOne)
  })

  it('Calls all methods once for user without default view', async () => {
    userDefaultDynamicTableViewRepository.findOne.resolves(null)

    await useCase.execute(DynamicTableName.WASTE_INQUIRY)

    assert.calledOnce(userDefaultDynamicTableViewRepository.findOne)
    assert.calledOnce(dynamicTableViewRepository.findOneByOrFail)
  })
})
