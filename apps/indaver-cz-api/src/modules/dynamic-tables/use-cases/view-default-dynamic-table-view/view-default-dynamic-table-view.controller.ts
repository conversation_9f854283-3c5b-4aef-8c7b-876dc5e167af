import { <PERSON>, Get, Param, ParseEnumPipe } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { ViewDefaultDynamicTableViewResponse } from './view-default-dynamic-table-view.response.js'
import { ViewDefaultDynamicTableViewUseCase } from './view-default-dynamic-table-view.use-case.js'

@ApiTags('Dynamic table')
@Controller('dynamic-tables/:name/default-view')
@ApiOAuth2([])
export class ViewDefaultDynamicTableViewController {
  constructor (
    private readonly useCase: ViewDefaultDynamicTableViewUseCase
  ) {}

  @Get()
  @ApiOkResponse({ type: ViewDefaultDynamicTableViewResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  async viewDefaultTableView (
    @Param('name', new ParseEnumPipe(DynamicTableName)) tableName: DynamicTableName
  ): Promise<ViewDefaultDynamicTableViewResponse> {
    return await this.useCase.execute(tableName)
  }
}
