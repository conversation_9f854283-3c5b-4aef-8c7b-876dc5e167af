import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { VisibilityConfigurationResponse } from '../../responses/view-default-dynamic-table-view-visible-columns.response.js'
import { SortConfigurationResponse } from '../../responses/view-default-dynamic-table-view-sort.response.js'
import { FilterConfigurationResponse } from '../../responses/view-default-dynamic-table-view-filter.response.js'

export class ViewDefaultDynamicTableViewResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String })
  name: string

  @ApiProperty({ type: Boolean })
  isGlobal: boolean

  @ApiProperty({ type: Boolean })
  isDefaultGlobal: boolean

  @ApiProperty({ type: Boolean })
  isUserDefault: boolean

  @ApiProperty({ type: VisibilityConfigurationResponse, isArray: true })
  visibleColumns: VisibilityConfigurationResponse[]

  @ApiProperty({ type: SortConfigurationResponse, isArray: true })
  sorts: SortConfigurationResponse[]

  @ApiProperty({ type: FilterConfigurationResponse, isArray: true })
  filters: FilterConfigurationResponse[]

  constructor (
    dynamicTableView: DynamicTableView
  ) {
    assert(dynamicTableView.isUserDefault !== undefined)

    this.uuid = dynamicTableView.uuid
    this.createdAt = dynamicTableView.createdAt.toISOString()
    this.updatedAt = dynamicTableView.updatedAt.toISOString()
    this.name = dynamicTableView.name
    this.isGlobal = dynamicTableView.isGlobal
    this.isDefaultGlobal = dynamicTableView.isDefaultGlobal
    this.isUserDefault = dynamicTableView.isUserDefault

    const {
      visibleColumns,
      filters,
      sorting
    } = dynamicTableView.configuration

    this.visibleColumns = visibleColumns.map((column) => {
      return new VisibilityConfigurationResponse(
        column.uuid,
        column.order
      )
    })

    this.filters = filters.map((filter) => {
      return new FilterConfigurationResponse(
        filter.uuid,
        filter.value
      )
    })

    this.sorts = sorting.map((sort) => {
      return new SortConfigurationResponse(
        sort.uuid,
        sort.order,
        sort.direction
      )
    })
  }
}
