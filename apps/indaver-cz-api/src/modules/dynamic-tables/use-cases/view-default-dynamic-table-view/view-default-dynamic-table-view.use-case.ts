import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { DynamicTableName } from '../../enums/dynamic-table-name.enum.js'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewDefaultDynamicTableViewResponse } from './view-default-dynamic-table-view.response.js'

@Injectable()
export class ViewDefaultDynamicTableViewUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(UserDefaultDynamicTableView)
    private readonly userDefaultDynamicTableViewRepository: Repository<UserDefaultDynamicTableView>,
    @InjectRepository(DynamicTableView)
    private readonly dynamicTableViewRepository: Repository<DynamicTableView>
  ) {}

  async execute (
    tableName: DynamicTableName
  ): Promise<ViewDefaultDynamicTableViewResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const userDefaultTableView = await this.userDefaultDynamicTableViewRepository
      .findOne({
        where: {
          userUuid: userUuid,
          dynamicTable: {
            name: tableName
          }
        },
        relations: {
          dynamicTableView: true
        }
      })

    let defaultView: DynamicTableView

    if (userDefaultTableView === null) {
      defaultView = await this.dynamicTableViewRepository.findOneByOrFail({
        isGlobal: true,
        isDefaultGlobal: true,
        dynamicTable: {
          name: tableName
        }
      })

      defaultView.isUserDefault = false
    } else {
      assert(userDefaultTableView.dynamicTableView !== undefined)

      defaultView = userDefaultTableView.dynamicTableView

      defaultView.isUserDefault = true
    }

    return new ViewDefaultDynamicTableViewResponse(
      defaultView
    )
  }
}
