import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { UserDefaultDynamicTableView } from '../../entities/user-default-dynamic-table-view.entity.js'
import { DynamicTable } from '../../entities/dynamic-table.entity.js'
import { DynamicTableView } from '../../entities/dynamic-table-view.entity.js'
import { ViewDefaultDynamicTableViewUseCase } from './view-default-dynamic-table-view.use-case.js'
import { ViewDefaultDynamicTableViewController } from './view-default-dynamic-table-view.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserDefaultDynamicTableView,
      DynamicTable,
      DynamicTableView
    ])
  ],
  controllers: [
    ViewDefaultDynamicTableViewController
  ],
  providers: [
    ViewDefaultDynamicTableViewUseCase
  ]
})
export class ViewDefaultDynamicTableViewModule {}
