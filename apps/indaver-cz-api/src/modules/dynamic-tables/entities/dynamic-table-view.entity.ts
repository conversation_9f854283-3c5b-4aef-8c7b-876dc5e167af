import { Column, CreateDateColumn, Entity, Index, Join<PERSON>olumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, type Relation, Unique, UpdateDateColumn } from 'typeorm'
import { User } from '../../../app/users/entities/user.entity.js'
import { DynamicTableViewConfiguration } from '../helpers/dynamic-table-view-configuration.js'
import { DynamicTable } from './dynamic-table.entity.js'
import { UserDefaultDynamicTableView } from './user-default-dynamic-table-view.entity.js'

@Entity()
@Unique(['createdByUserUuid', 'name', 'dynamicTableUuid'])
export class DynamicTableView {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'boolean', default: false })
  isGlobal: boolean

  @Column({ type: 'boolean', default: false })
  isDefaultGlobal: boolean

  @Column({ type: 'jsonb' })
  configuration: DynamicTableViewConfiguration

  @Column({ type: 'uuid' })
  @Index()
  dynamicTableUuid: string

  @ManyToOne(() => DynamicTable, dynamicTable => dynamicTable.columns)
  @JoinColumn({ name: 'dynamic_table_uuid' })
  dynamicTable?: Relation<DynamicTable>

  @Column({ type: 'uuid', nullable: true })
  @Index()
  createdByUserUuid: string | null

  @ManyToOne(() => User, user => user.dynamicTableViews)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User> | null

  @OneToMany(() => UserDefaultDynamicTableView,
    defaultTableView => defaultTableView.dynamicTableView
  )
  defaultDynamicTableViews?: Array<Relation<UserDefaultDynamicTableView>>

  @Column({ type: 'boolean', nullable: true, insert: false, update: false, select: false })
  isUserDefault?: boolean
}
