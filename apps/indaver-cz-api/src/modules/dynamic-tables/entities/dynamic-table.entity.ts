import { Column, CreateDateColumn, Entity, Index, OneToMany, PrimaryGeneratedColumn, type Relation, UpdateDateColumn } from 'typeorm'
import { DynamicTableName } from '../enums/dynamic-table-name.enum.js'
import { DynamicTableColumn } from './dynamic-table-column.entity.js'
import { DynamicTableView } from './dynamic-table-view.entity.js'
import { UserDefaultDynamicTableView } from './user-default-dynamic-table-view.entity.js'

@Entity()
export class DynamicTable {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Index()
  @Column({ type: 'enum', enum: DynamicTableName, unique: true })
  name: DynamicTableName

  @OneToMany(() => DynamicTableColumn, dynamicTableColumn => dynamicTableColumn.dynamicTable)
  columns?: Array<Relation<DynamicTableColumn>>

  @OneToMany(() => DynamicTableView, dynamicTableView => dynamicTableView.dynamicTable)
  views?: Array<Relation<DynamicTableView>>

  @OneToMany(() => UserDefaultDynamicTableView,
    defaultTableView => defaultTableView.dynamicTable
  )
  defaultDynamicTableViews?: Array<Relation<UserDefaultDynamicTableView>>
}
