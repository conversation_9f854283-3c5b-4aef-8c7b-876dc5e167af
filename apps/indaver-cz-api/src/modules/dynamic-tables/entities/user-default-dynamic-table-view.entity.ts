import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn, type Relation } from 'typeorm'
import { User } from '../../../app/users/entities/user.entity.js'
import { DynamicTableView } from './dynamic-table-view.entity.js'
import { DynamicTable } from './dynamic-table.entity.js'

@Entity()
export class UserDefaultDynamicTableView {
  @PrimaryColumn('uuid')
  userUuid: string

  @ManyToOne(() => User, user => user.defaultDynamicTableViews)
  @JoinColumn({ name: 'user_uuid' })
  user?: Relation<User>

  @PrimaryColumn('uuid')
  dynamicTableUuid: string

  @ManyToOne(() => DynamicTable, table => table.defaultDynamicTableViews)
  @JoinColumn({ name: 'dynamic_table_uuid' })
  dynamicTable?: Relation<DynamicTable>

  @Column('uuid', { nullable: false })
  dynamicTableViewUuid: string

  @ManyToOne(() => DynamicTableView, view => view.defaultDynamicTableViews)
  @JoinColumn({ name: 'dynamic_table_view_uuid' })
  dynamicTableView?: Relation<DynamicTableView>
}
