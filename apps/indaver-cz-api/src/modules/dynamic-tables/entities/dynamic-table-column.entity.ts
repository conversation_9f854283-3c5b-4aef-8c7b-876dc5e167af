import { Column, CreateDateColumn, Entity, Index, Join<PERSON>olumn, ManyToOne, PrimaryGeneratedColumn, type Relation, Unique, UpdateDateColumn } from 'typeorm'
import { DynamicColumnName } from '../helpers/dynamic-table-column-name.type.js'
import { DynamicTableColumnField } from '../helpers/dynamic-table-column-fields.type.js'
import { DynamicTable } from './dynamic-table.entity.js'

@Entity()
@Unique(['dynamicTableUuid', 'name'])
export class DynamicTableColumn {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar' })
  name: DynamicColumnName // Non changeable property. Frontend uses this property to map columns.

  @Column({ type: 'jsonb' })
  applicableFields: DynamicTableColumnField[]

  @Column({ type: 'jsonb' })
  searchableFields: DynamicTableColumnField[]

  @Column({ type: 'jsonb', nullable: true })
  filterableField: DynamicTableColumnField | null

  @Column({ type: 'jsonb' })
  sortableFields: DynamicTableColumnField[]

  @Column({ type: 'boolean' })
  isHidable: boolean

  @Column({ type: 'uuid' })
  @Index()
  dynamicTableUuid: string

  @ManyToOne(() => DynamicTable, dynamicTable => dynamicTable.columns)
  @JoinColumn({ name: 'dynamic_table_uuid' })
  dynamicTable?: Relation<DynamicTable>
}
