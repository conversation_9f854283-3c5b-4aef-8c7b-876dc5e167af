import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'

export const SAP_INVOICE_TYPE_TO_FILTER = ['M', 'O', 'P'] as const
export type SapInvoiceType = typeof SAP_INVOICE_TYPE_TO_FILTER[number]

export enum InvoiceType {
  INVOICE = 'invoice',
  CREDIT_MEMO = 'credit_memo',
  DEBIT_MEMO = 'debit_memo',
  UNKNOWN = 'unknown'
}

export function InvoiceTypeApiProperty(options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enumName: 'InvoiceType',
    enum: InvoiceType
  })
}

export enum InvoiceFilterType {
  INVOICE = 'invoice',
  CREDIT_MEMO = 'credit_memo',
  DEBIT_MEMO = 'debit_memo'
}
export function InvoiceFilterTypeApiProperty(options?: ApiPropertyOptions): PropertyDecorator {
  return ApiProperty({
    ...options,
    enum: InvoiceFilterType,
    enumName: 'InvoiceFilterType'
  })
}

export class InvoiceTypeMapper {
  static fromSap(sapInvoiceType: SapInvoiceType | (string & {})): InvoiceType {
    switch (sapInvoiceType) {
      case 'M': return InvoiceType.INVOICE
      case 'O': return InvoiceType.CREDIT_MEMO
      case 'P': return InvoiceType.DEBIT_MEMO
      default:
        return InvoiceType.UNKNOWN
    }
  }

  static toSap(type: InvoiceFilterType): SapInvoiceType {
    switch (type) {
      case InvoiceFilterType.INVOICE: return 'M'
      case InvoiceFilterType.CREDIT_MEMO: return 'O'
      case InvoiceFilterType.DEBIT_MEMO: return 'P'
      default:
        exhaustiveCheck(type)
    }
  }
}
