import { Controller, Get, Query, Res } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import type { Response } from 'express'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ExportDraftInvoicesExcelUseCase } from './export-draft-invoices-excel.use-case.js'
import { ExportDraftInvoicesExcelQuery } from './queries/export-draft-invoices-excel.query.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices/export-excel')
export class ExportDraftInvoicesExcelController {
  constructor (
    private readonly useCase: ExportDraftInvoicesExcelUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_READ, Permission.INVOICE_MANAGE)
  @ApiOkResponse({ description: 'draft invoices exported as Excel' })
  public async exportInvoicesExcel (
    @Query() query: ExportDraftInvoicesExcelQuery,
    @Res() res: Response
  ): Promise<void> {
    await this.useCase.execute(query, res)
  }
}
