import { Body, Controller, HttpCode, Param, Post } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiConflictErrorResponse, ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { NonApproveOrRejectableDraftInvoiceError } from '../../errors/non-approve-or-rejectable-draft-invoice.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { RejectDraftInvoiceCommand } from './reject-draft-invoice.command.js'
import { RejectDraftInvoiceUseCase } from './reject-draft-invoice.use-case.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices/:invoiceNumber/reject')
export class RejectDraftInvoiceController {
  constructor (
    private readonly useCase: RejectDraftInvoiceUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_MANAGE)
  @ApiOkResponse()
  @HttpCode(200)
  @ApiNotFoundErrorResponse(InvoiceNotFoundError)
  @ApiConflictErrorResponse(NonApproveOrRejectableDraftInvoiceError)
  public async rejectDraftInvoice (
    @Param('invoiceNumber') invoiceNumber: string,
    @Body() command: RejectDraftInvoiceCommand
  ): Promise<void> {
    return await this.useCase.execute(invoiceNumber, command)
  }
}
