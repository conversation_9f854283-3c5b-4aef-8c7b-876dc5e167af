import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapRejectDraftInvoiceCustomerUseCase } from '../../../sap/use-cases/reject-draft-invoice-customer/reject-draft-invoice-customer.use-case.js'
import { SapRejectDraftInvoiceSalesRepUseCase } from '../../../sap/use-cases/reject-draft-invoice-sales-rep/reject-draft-invoice-sales-rep.use-case.js'
import { SapRejectDraftInvoiceCustomerCommand } from '../../../sap/use-cases/reject-draft-invoice-customer/reject-draft-invoice-customer.command.js'
import { SapRejectDraftInvoiceSalesRepCommand } from '../../../sap/use-cases/reject-draft-invoice-sales-rep/reject-draft-invoice-sales-rep.command.js'
import { RejectDraftInvoiceCommand } from './reject-draft-invoice.command.js'
import { RejectDraftInvoiceValidator } from './reject-draft-invoice.validator.js'

@Injectable()
export class RejectDraftInvoiceUseCase {
  constructor (
    private readonly validator: RejectDraftInvoiceValidator,
    private readonly authContext: AuthContext,
    private readonly sapRejectCustomer: SapRejectDraftInvoiceCustomerUseCase,
    private readonly sapRejectSales: SapRejectDraftInvoiceSalesRepUseCase
  ) {}

  async execute (
    invoiceNumber: string,
    command: RejectDraftInvoiceCommand
  ): Promise<void> {
    await this.validator.validate(invoiceNumber)

    const isInternalUser = this.authContext.isInternalUser()

    if (isInternalUser) {
      const sapCommand = this.mapToSapSalesRepCommand(invoiceNumber, command)
      await this.sapRejectSales.execute(sapCommand)
    } else {
      const sapCommand = this.mapToSapCustomerCommand(invoiceNumber, command)
      await this.sapRejectCustomer.execute(sapCommand)
    }
  }

  private mapToSapCustomerCommand (
    invoiceNumber: string,
    command: RejectDraftInvoiceCommand
  ): SapRejectDraftInvoiceCustomerCommand {
    return {
      Vbeln: invoiceNumber,
      CommentCustomer: command.remark,
      Bstk: ''
    }
  }

  private mapToSapSalesRepCommand (
    invoiceNumber: string,
    command: RejectDraftInvoiceCommand
  ): SapRejectDraftInvoiceSalesRepCommand {
    return {
      Vbeln: invoiceNumber,
      CommentToCustomer: command.remark,
      Bstk: ''
    }
  }
}
