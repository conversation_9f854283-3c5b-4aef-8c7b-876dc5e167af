import { randSentence } from '@ngneat/falso'
import { RejectDraftInvoiceCommand } from '../../reject-draft-invoice.command.js'

export class RejectDraftInvoiceCommandBuilder {
  private command: RejectDraftInvoiceCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new RejectDraftInvoiceCommand()

    this.command.remark = randSentence()

    return this
  }

  public build (): RejectDraftInvoiceCommand {
    const result = this.command

    this.reset()

    return result
  }
}
