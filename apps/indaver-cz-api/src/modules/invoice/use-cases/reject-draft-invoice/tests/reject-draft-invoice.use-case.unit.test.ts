import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { RejectDraftInvoiceUseCase } from '../reject-draft-invoice.use-case.js'
import { RejectDraftInvoiceValidator } from '../reject-draft-invoice.validator.js'
import { SapRejectDraftInvoiceCustomerUseCase } from '../../../../sap/use-cases/reject-draft-invoice-customer/reject-draft-invoice-customer.use-case.js'
import { SapRejectDraftInvoiceSalesRepUseCase } from '../../../../sap/use-cases/reject-draft-invoice-sales-rep/reject-draft-invoice-sales-rep.use-case.js'
import { RejectDraftInvoiceCommandBuilder } from './builders/reject-draft-invoice.command.builder.js'

describe('RejectDraftInvoiceUseCase - Unit test', () => {
  let useCase: RejectDraftInvoiceUseCase

  let validator: SinonStubbedInstance<RejectDraftInvoiceValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let sapRejectCustomer: SinonStubbedInstance<SapRejectDraftInvoiceCustomerUseCase>
  let sapRejectSales: SinonStubbedInstance<SapRejectDraftInvoiceSalesRepUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(RejectDraftInvoiceValidator)
    authContext = createStubInstance(AuthContext)
    sapRejectCustomer = createStubInstance(SapRejectDraftInvoiceCustomerUseCase)
    sapRejectSales = createStubInstance(SapRejectDraftInvoiceSalesRepUseCase)

    useCase = new RejectDraftInvoiceUseCase(
      validator,
      authContext,
      sapRejectCustomer,
      sapRejectSales
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves()
    authContext.isInternalUser.returns(false)
    sapRejectCustomer.execute.resolves()
    sapRejectSales.execute.resolves()
  }

  it('Rejects invoice as customer', async () => {
    await useCase.execute(randomUUID(), new RejectDraftInvoiceCommandBuilder().build())

    assert.calledOnce(validator.validate)
    assert.calledOnce(sapRejectCustomer.execute)
    assert.notCalled(sapRejectSales.execute)
  })

  it('Rejects invoice as internal user', async () => {
    authContext.isInternalUser.returns(true)

    await useCase.execute(randomUUID(), new RejectDraftInvoiceCommandBuilder().build())

    assert.calledOnce(validator.validate)
    assert.calledOnce(sapRejectSales.execute)
    assert.notCalled(sapRejectCustomer.execute)
  })
})
