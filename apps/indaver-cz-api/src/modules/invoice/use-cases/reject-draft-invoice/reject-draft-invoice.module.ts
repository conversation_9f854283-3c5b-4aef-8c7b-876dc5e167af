import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { RejectDraftInvoiceController } from './reject-draft-invoice.controller.js'
import { RejectDraftInvoiceUseCase } from './reject-draft-invoice.use-case.js'
import { RejectDraftInvoiceValidator } from './reject-draft-invoice.validator.js'

@Module({
  imports: [
    SapModule
  ],
  controllers: [
    RejectDraftInvoiceController
  ],
  providers: [
    RejectDraftInvoiceUseCase,
    RejectDraftInvoiceValidator
  ]
})
export class RejectDraftInvoiceModule {}
