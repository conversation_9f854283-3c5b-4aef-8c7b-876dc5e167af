import { FilterQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, IsArray, IsEnum, IsNotEmpty, IsString } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'
import { InvoiceStatus, InvoiceStatusApiProperty } from '../../../enums/invoice-status.enum.js'
import { InvoiceColumnName, InvoiceColumnNameApiProperty } from '../../../../dynamic-tables/enums/invoice-column-name.enum.js'

export class ExportInvoicesExcelFilterQuery extends FilterQuery {
  @InvoiceStatusApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(InvoiceStatus, { each: true })
  statuses: InvoiceStatus[]

  @InvoiceColumnNameApiProperty({ isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsEnum(InvoiceColumnName, { each: true })
  columns: InvoiceColumnName[]

  @ApiProperty({ type: String, description: 'Translated column names for Excel header', example: ['Invoice No.'], isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsNotEmpty({ each: true })
  @IsString({ each: true })
  translatedColumns: string[]
}
