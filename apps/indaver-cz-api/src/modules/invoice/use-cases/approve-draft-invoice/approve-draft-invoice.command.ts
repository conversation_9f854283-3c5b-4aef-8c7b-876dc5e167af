import { ApiProperty } from '@nestjs/swagger'
import { IsNullable } from '@wisemen/validators'
import { IsNotEmpty, IsString } from 'class-validator'

export class ApproveDraftInvoiceCommand {
  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  poNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  @IsNullable()
  @IsString()
  @IsNotEmpty()
  remark: string | null
}
