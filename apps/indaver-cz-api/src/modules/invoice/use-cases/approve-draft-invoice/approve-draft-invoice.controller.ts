import { Body, Controller, HttpCode, Param, Post } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse, ApiConflictErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { NonApproveOrRejectableDraftInvoiceError } from '../../errors/non-approve-or-rejectable-draft-invoice.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { ApproveDraftInvoiceUseCase } from './approve-draft-invoice.use-case.js'
import { ApproveDraftInvoiceCommand } from './approve-draft-invoice.command.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices/:invoiceNumber/approve')
export class ApproveDraftInvoiceController {
  constructor (
    private readonly useCase: ApproveDraftInvoiceUseCase
  ) { }

  @Post()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_MANAGE)
  @ApiOkResponse()
  @HttpCode(200)
  @ApiNotFoundErrorResponse(InvoiceNotFoundError)
  @ApiConflictErrorResponse(NonApproveOrRejectableDraftInvoiceError)
  public async approveDraftInvoice (
    @Param('invoiceNumber') invoiceNumber: string,
    @Body() command: ApproveDraftInvoiceCommand
  ): Promise<void> {
    return await this.useCase.execute(invoiceNumber, command)
  }
}
