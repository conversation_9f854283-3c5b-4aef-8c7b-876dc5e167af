import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapGetDraftInvoiceIndexUseCase } from '../../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { SapGetDraftInvoiceIndexResponseBuilder } from '../../../../sap/use-cases/get-draft-invoice-index/tests/builders/get-draft-invoice-index.response.builder.js'
import { UserCustomerAuthService } from '../../../../auth/services/user-customer-auth.service.js'
import { ApproveDraftInvoiceCommandBuilder } from './builders/approve-draft-invoice.command.builder.js'

describe('ApproveDraftInvoice E2E Tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('approves the draft invoice', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_MANAGE])

    const sapMock = mock.method(SapGetDraftInvoiceIndexUseCase.prototype, 'execute', () => {
      return [
        new SapGetDraftInvoiceIndexResponseBuilder()
          .withStatus2('')
          .withKatr6('A1')
          .build()
      ]
    })

    mock.method(UserCustomerAuthService.prototype, 'canUserAccessCustomer', () => {
      return true
    })

    const command = new ApproveDraftInvoiceCommandBuilder().build()

    const response = await request(setup.httpServer)
      .post(`/draft-invoices/${randomUUID()}/approve`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    sapMock.mock.restore()

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
