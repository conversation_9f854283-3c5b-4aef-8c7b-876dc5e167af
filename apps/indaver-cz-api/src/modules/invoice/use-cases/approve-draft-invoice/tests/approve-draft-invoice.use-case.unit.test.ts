import { before, describe, it, afterEach } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { ApproveDraftInvoiceUseCase } from '../approve-draft-invoice.use-case.js'
import { ApproveDraftInvoiceValidator } from '../approve-draft-invoice.validator.js'
import { SapApproveDraftInvoiceCustomerUseCase } from '../../../../sap/use-cases/approve-draft-invoice-customer/approve-draft-invoice-customer.use-case.js'
import { SapApproveDraftInvoiceSalesRepUseCase } from '../../../../sap/use-cases/approve-draft-invoice-sales-rep/approve-draft-invoice-sales-rep.use-case.js'
import { ApproveDraftInvoiceCommandBuilder } from './builders/approve-draft-invoice.command.builder.js'

describe('ApproveDraftInvoiceUseCase - Unit test', () => {
  let useCase: ApproveDraftInvoiceUseCase

  let validator: SinonStubbedInstance<ApproveDraftInvoiceValidator>
  let authContext: SinonStubbedInstance<AuthContext>
  let sapApproveCustomer: SinonStubbedInstance<SapApproveDraftInvoiceCustomerUseCase>
  let sapApproveSales: SinonStubbedInstance<SapApproveDraftInvoiceSalesRepUseCase>

  before(() => {
    TestBench.setupUnitTest()

    validator = createStubInstance(ApproveDraftInvoiceValidator)
    authContext = createStubInstance(AuthContext)
    sapApproveCustomer = createStubInstance(SapApproveDraftInvoiceCustomerUseCase)
    sapApproveSales = createStubInstance(SapApproveDraftInvoiceSalesRepUseCase)

    useCase = new ApproveDraftInvoiceUseCase(
      validator,
      authContext,
      sapApproveCustomer,
      sapApproveSales
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    validator.validate.resolves()
    authContext.isInternalUser.returns(false)
    sapApproveCustomer.execute.resolves()
    sapApproveSales.execute.resolves()
  }

  it('Approves invoice as customer', async () => {
    await useCase.execute(randomUUID(), new ApproveDraftInvoiceCommandBuilder().build())

    assert.calledOnce(validator.validate)
    assert.calledOnce(sapApproveCustomer.execute)
    assert.notCalled(sapApproveSales.execute)
  })

  it('Approves invoice as internal user', async () => {
    authContext.isInternalUser.returns(true)

    await useCase.execute(randomUUID(), new ApproveDraftInvoiceCommandBuilder().build())

    assert.calledOnce(validator.validate)
    assert.calledOnce(sapApproveSales.execute)
    assert.notCalled(sapApproveCustomer.execute)
  })
})
