import { ApproveDraftInvoiceCommand } from '../../approve-draft-invoice.command.js'

export class ApproveDraftInvoiceCommandBuilder {
  private command: ApproveDraftInvoiceCommand

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.command = new ApproveDraftInvoiceCommand()

    this.command.poNumber = null
    this.command.remark = null

    return this
  }

  public build (): ApproveDraftInvoiceCommand {
    const result = this.command

    this.reset()

    return result
  }
}
