import { Module } from '@nestjs/common'
import { SapModule } from '../../../sap/sap.module.js'
import { ApproveDraftInvoiceController } from './approve-draft-invoice.controller.js'
import { ApproveDraftInvoiceValidator } from './approve-draft-invoice.validator.js'
import { ApproveDraftInvoiceUseCase } from './approve-draft-invoice.use-case.js'

@Module({
  imports: [
    SapModule
  ],
  controllers: [
    ApproveDraftInvoiceController
  ],
  providers: [
    ApproveDraftInvoiceUseCase,
    ApproveDraftInvoiceValidator
  ]
})
export class ApproveDraftInvoiceModule {}
