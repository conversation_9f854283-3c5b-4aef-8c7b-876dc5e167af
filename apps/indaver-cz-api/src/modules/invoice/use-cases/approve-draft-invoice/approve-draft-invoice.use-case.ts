import { Injectable } from '@nestjs/common'
import { AuthContext } from '../../../auth/auth.context.js'
import { SapApproveDraftInvoiceCustomerUseCase } from '../../../sap/use-cases/approve-draft-invoice-customer/approve-draft-invoice-customer.use-case.js'
import { SapApproveDraftInvoiceSalesRepUseCase } from '../../../sap/use-cases/approve-draft-invoice-sales-rep/approve-draft-invoice-sales-rep.use-case.js'
import { SapApproveDraftInvoiceCustomerCommand } from '../../../sap/use-cases/approve-draft-invoice-customer/approve-draft-invoice-customer.command.js'
import { SapApproveDraftInvoiceSalesRepCommand } from '../../../sap/use-cases/approve-draft-invoice-sales-rep/approve-draft-invoice-sales-rep.command.js'
import { ApproveDraftInvoiceValidator } from './approve-draft-invoice.validator.js'
import { ApproveDraftInvoiceCommand } from './approve-draft-invoice.command.js'

@Injectable()
export class ApproveDraftInvoiceUseCase {
  constructor (
    private readonly validator: ApproveDraftInvoiceValidator,
    private readonly authContext: AuthContext,
    private readonly sapApproveCustomer: SapApproveDraftInvoiceCustomerUseCase,
    private readonly sapApproveSales: SapApproveDraftInvoiceSalesRepUseCase
  ) {}

  async execute (
    invoiceNumber: string,
    command: ApproveDraftInvoiceCommand
  ): Promise<void> {
    await this.validator.validate(invoiceNumber)

    const isInternalUser = this.authContext.isInternalUser()

    if (isInternalUser) {
      const sapCommand = this.mapToSapSalesRepCommand(invoiceNumber, command)
      await this.sapApproveSales.execute(sapCommand)
    } else {
      const sapCommand = this.mapToSapCustomerCommand(invoiceNumber, command)
      await this.sapApproveCustomer.execute(sapCommand)
    }
  }

  private mapToSapCustomerCommand (
    invoiceNumber: string,
    command: ApproveDraftInvoiceCommand
  ): SapApproveDraftInvoiceCustomerCommand {
    return {
      Vbeln: invoiceNumber,
      Bstk: command.poNumber ?? '',
      CommentCustomer: command.remark ?? ''
    }
  }

  private mapToSapSalesRepCommand (
    invoiceNumber: string,
    command: ApproveDraftInvoiceCommand
  ): SapApproveDraftInvoiceSalesRepCommand {
    return {
      Vbeln: invoiceNumber,
      Bstk: command.poNumber ?? '',
      CommentToCustomer: command.remark ?? ''
    }
  }
}
