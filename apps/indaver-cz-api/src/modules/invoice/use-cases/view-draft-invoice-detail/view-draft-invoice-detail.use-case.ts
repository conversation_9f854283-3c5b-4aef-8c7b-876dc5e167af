import { Injectable } from '@nestjs/common'
import { SapGetDraftInvoiceIndexUseCase } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.use-case.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetDraftInvoiceIndexResponse } from '../../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { DraftInvoiceMapper } from '../../mappers/draft-invoice.mapper.js'
import { ViewDraftInvoiceDetailResponse } from './view-draft-invoice-detail.response.js'

@Injectable()
export class ViewDraftInvoiceDetailUseCase {
  constructor (
    private readonly sapGetDraftInvoiceIndexUseCase: SapGetDraftInvoiceIndexUseCase
  ) {}

  async execute (
    invoiceNumber: string,
    selectedCustomerId: string | null
  ): Promise<ViewDraftInvoiceDetailResponse> {
    const sapQuery = new SapQuery<SapGetDraftInvoiceIndexResponse>()
      .where('Vbeln', invoiceNumber)
      .setTop(1)

    if (selectedCustomerId !== null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }

    const invoiceResponse = await this.sapGetDraftInvoiceIndexUseCase.execute(sapQuery)
    const requestedInvoice = invoiceResponse.at(0)

    if (requestedInvoice === undefined) {
      throw new InvoiceNotFoundError({ invoiceNumber })
    }

    const invoice = DraftInvoiceMapper.fromSapResponse(requestedInvoice)

    return new ViewDraftInvoiceDetailResponse(invoice)
  }
}
