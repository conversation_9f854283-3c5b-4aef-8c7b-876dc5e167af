import { ApiProperty } from '@nestjs/swagger'
import { DraftInvoiceStatusApiProperty, DraftInvoiceStatus } from '../../enums/draft-invoice-status.enum.js'
import { MailStatusApiProperty, MailStatus } from '../../enums/mail-status.enum.js'
import { DraftInvoice } from '../../types/draft-invoice-type.js'

export class ViewDraftInvoiceDetailResponse {
  @ApiProperty({ type: String })
  invoiceNumber: string

  @DraftInvoiceStatusApiProperty()
  status: DraftInvoiceStatus

  @ApiProperty({ type: String })
  payerId: string

  @ApiProperty({ type: String })
  payerName: string

  @ApiProperty({ type: String, format: 'date' })
  issuedOn: string

  @MailStatusApiProperty()
  firstReminderMailStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  firstReminderOn: string | null

  @MailStatusApiProperty()
  secondReminderMailStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  secondReminderOn: string | null

  @MailStatusApiProperty()
  thirdReminderMailStatus: MailStatus

  @ApiProperty({ type: String, format: 'date', nullable: true })
  autoApprovedOn: string | null

  @ApiProperty({ type: String })
  netAmount: string

  @ApiProperty({ type: String })
  vatAmount: string

  @ApiProperty({ type: String })
  currency: string

  @ApiProperty({ type: String, nullable: true })
  customerApprovalBy: string | null

  @ApiProperty({ type: String, nullable: true, format: 'date' })
  customerApprovalDate: string | null

  @ApiProperty({ type: String, nullable: true })
  poNumber: string | null

  @ApiProperty({ type: String, nullable: true })
  accountDocumentNumber: string | null

  @ApiProperty({ type: String })
  customerId: string

  constructor (invoice: DraftInvoice) {
    this.invoiceNumber = invoice.invoiceNumber
    this.status = invoice.status
    this.payerId = invoice.payerId
    this.payerName = invoice.payerName
    this.issuedOn = invoice.issuedOn
    this.firstReminderMailStatus = invoice.firstReminderMailStatus
    this.firstReminderOn = invoice.firstReminderOn
    this.secondReminderMailStatus = invoice.secondReminderMailStatus
    this.secondReminderOn = invoice.secondReminderOn
    this.thirdReminderMailStatus = invoice.thirdReminderMailStatus
    this.autoApprovedOn = invoice.autoApprovedOn
    this.netAmount = invoice.netAmount
    this.vatAmount = invoice.vatAmount
    this.currency = invoice.currency
    this.customerApprovalBy = invoice.customerApprovalBy
    this.customerApprovalDate = invoice.customerApprovalDate
    this.poNumber = invoice.poNumber
    this.accountDocumentNumber = invoice.accountDocumentNumber
    this.customerId = invoice.customerId
  }
}
