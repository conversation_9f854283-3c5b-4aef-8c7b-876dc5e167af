import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Permission } from '../../../../permission/permission.enum.js'

describe('ViewDraftInvoiceDetail - E2E Tests', () => {
  let setup: EndToEndTestSetup
  let user: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
    user = await setup.authContext.getUser([Permission.INVOICE_READ])
  })

  after(async () => await setup.teardown())

  it('returns the detail', async () => {
    const response = await request(setup.httpServer)
      .get(`/draft-invoices/${randomUUID()}`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
