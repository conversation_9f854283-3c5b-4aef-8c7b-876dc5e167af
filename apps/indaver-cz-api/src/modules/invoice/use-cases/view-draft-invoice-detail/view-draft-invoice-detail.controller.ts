import { <PERSON>, Get, Param } from '@nestjs/common'
import { ApiTags, ApiOkResponse, ApiOAuth2 } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { ViewDraftInvoiceDetailUseCase } from './view-draft-invoice-detail.use-case.js'
import { ViewDraftInvoiceDetailResponse } from './view-draft-invoice-detail.response.js'

@ApiTags('Draft invoice')
@ApiOAuth2([])
@Controller('draft-invoices/:invoiceNumber')
export class ViewDraftInvoiceDetailController {
  constructor (
    private readonly authContext: AuthContext,
    private readonly useCase: ViewDraftInvoiceDetailUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @Permissions(Permission.INVOICE_MANAGE, Permission.INVOICE_READ)
  @ApiOkResponse({ type: ViewDraftInvoiceDetailResponse })
  @ApiNotFoundErrorResponse(InvoiceNotFoundError)
  public async viewInvoiceDetail (
    @Param('invoiceNumber') invoiceNumber: string
  ): Promise<ViewDraftInvoiceDetailResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    return await this.useCase.execute(invoiceNumber, selectedCustomerId)
  }
}
