import { Module } from '@nestjs/common'
import { SharepointModule } from '../../../sharepoint/sharepoint.module.js'
import { DownloadInvoiceCertificateController } from './download-invoice-certificate.controller.js'
import { DownloadInvoiceCertificateUseCase } from './download-invoice-certificate.use-case.js'

@Module({
  imports: [
    SharepointModule
  ],
  controllers: [
    DownloadInvoiceCertificateController
  ],
  providers: [
    DownloadInvoiceCertificateUseCase
  ]
})
export class DownloadInvoiceCertificateModule {}
