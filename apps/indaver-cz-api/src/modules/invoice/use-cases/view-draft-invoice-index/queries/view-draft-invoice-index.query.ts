import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetSearchQuery } from '@wisemen/pagination'
import { ArrayMinSize, ArrayUnique, Equals, IsArray, IsObject, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { IsUndefinable } from '@wisemen/validators'
import { ViewDraftInvoiceIndexFilterQuery } from './view-draft-invoice-index.filter.query.js'
import { ViewDraftInvoiceIndexSortQuery } from './view-draft-invoice-index-sort.query.js'

export class ViewDraftInvoiceIndexQuery extends PaginatedOffsetSearchQuery {
  @ApiProperty({ type: ViewDraftInvoiceIndexSortQuery, isArray: true, required: false })
  @IsUndefinable()
  @Type(() => ViewDraftInvoiceIndexSortQuery)
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  sort?: ViewDraftInvoiceIndexSortQuery[]

  @ApiProperty({ type: ViewDraftInvoiceIndexFilterQuery })
  @Type(() => ViewDraftInvoiceIndexFilterQuery)
  @ValidateNested()
  @IsObject()
  filter: ViewDraftInvoiceIndexFilterQuery

  @Equals(undefined)
  search?: never
}
