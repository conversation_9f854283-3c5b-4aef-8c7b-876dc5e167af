import { ApiProperty } from '@nestjs/swagger'
import { PaginatedKeysetQuery } from '@wisemen/pagination'
import { Type } from 'class-transformer'
import { IsObject, IsOptional, ValidateNested } from 'class-validator'
import { ViewDraftInvoiceIndexPaginationQueryKey } from './view-draft-invoice-index.pagination.query.key.js'

export class ViewDraftInvoiceIndexQueryKey extends PaginatedKeysetQuery {
  @ApiProperty({
    type: ViewDraftInvoiceIndexPaginationQueryKey,
    required: false,
    nullable: true
  })
  @Type(() => ViewDraftInvoiceIndexPaginationQueryKey)
  @ValidateNested()
  @IsObject()
  @IsOptional()
  key?: ViewDraftInvoiceIndexPaginationQueryKey | null
}
