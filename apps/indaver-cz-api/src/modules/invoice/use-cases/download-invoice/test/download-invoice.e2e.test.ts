import { after, before, describe, it, mock } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { HttpStatus } from '@nestjs/common'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { SapGetInvoiceIndexUseCase } from '../../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SapGetDocumentIndexResponseBuilder } from '../../../../sap/use-cases/get-documents-index/tests/get-documents-index.response.builder.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../../../sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'

describe('Download invoice e2e tests', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('downloads the invoice document', async () => {
    const user = await setup.authContext.getUser([Permission.INVOICE_READ])
    const invoiceNumber = randomUUID()

    mock.method(SapGetInvoiceIndexUseCase.prototype, 'execute', () => {
      return [
        new SapGetInvoiceIndexResponseBuilder()
          .build()
      ]
    })

    mock.method(SapGetDocumentsIndexUseCase.prototype, 'execute', () => {
      return [
        new SapGetDocumentIndexResponseBuilder()
          .withArObject('ZSI_O')
          .build()
      ]
    })

    const response = await request(setup.httpServer)
      .get(`/invoices/${invoiceNumber}/download`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(HttpStatus.OK)
  })
})
