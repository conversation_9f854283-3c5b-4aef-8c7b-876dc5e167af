import { Injectable } from '@nestjs/common'
import { SapGetInvoiceIndexUseCase } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.use-case.js'
import { SapGetDocumentsIndexUseCase } from '../../../sap/use-cases/get-documents-index/get-documents-index.use-case.js'
import { SapGetDocumentResponse } from '../../../sap/use-cases/get-documents-index/get-documents-index.response.js'
import { SapQuery } from '../../../sap/query/sap-query.js'
import { SapGetInvoiceIndexResponse } from '../../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { InvoiceNotFoundError } from '../../errors/invoice-not-found.error.js'
import { InvoiceDocumentNotFoundError } from '../../errors/invoice-document-not-found.error.js'
import { SapGetDocumentObject } from '../../../sap/use-cases/get-documents-index/get-document-sap-object.enum.js'

@Injectable()
export class DownloadInvoiceUseCase {
  constructor (
    private readonly authContext: AuthContext,
    private readonly sapGetInvoiceUseCase: SapGetInvoiceIndexUseCase,
    private readonly sapGetDocumentsIndexUseCase: SapGetDocumentsIndexUseCase
  ) {}

  async execute (invoiceNumber: string): Promise<string> {
    const formattedInvoiceNumber = invoiceNumber.padStart(10, '0')
    await this.validateInvoice(invoiceNumber)

    const documents = await this.getDocuments(formattedInvoiceNumber)

    const matchingDocuments = documents.filter((document) => {
      return this.isInvoiceDocument(document)
    })

    if (matchingDocuments.length === 0) {
      throw new InvoiceDocumentNotFoundError({ invoiceNumber })
    }

    return matchingDocuments[0].ArUrl
  }

  private async getDocuments (invoiceNumber: string): Promise<SapGetDocumentResponse[]> {
    const documentResponse = await this.sapGetDocumentsIndexUseCase.execute(
      invoiceNumber,
      SapGetDocumentObject.VBRK
    )

    if (documentResponse.length === 0) {
      throw new InvoiceDocumentNotFoundError({ invoiceNumber })
    }

    return documentResponse
  }

  private async validateInvoice (invoiceNumber: string): Promise<void> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()

    const sapQuery = new SapQuery<SapGetInvoiceIndexResponse>()
      .where('Vbeln', invoiceNumber)
      .setTop(1)
      .addSelect('Vbeln')

    if (selectedCustomerId !== null) {
      sapQuery.andWhere('Kunrg', selectedCustomerId)
    }

    const response = await this.sapGetInvoiceUseCase.execute(sapQuery)
    const invoice = response.at(0)

    if (invoice === undefined) {
      throw new InvoiceNotFoundError({ invoiceNumber })
    }
  }

  private isInvoiceDocument (response: SapGetDocumentResponse): boolean {
    const invoiceDocumentValue = 'ZSI_O'
    return response.ArObject === invoiceDocumentValue
  }
}
