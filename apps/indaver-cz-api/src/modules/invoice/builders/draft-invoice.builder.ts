import { randomUUID } from 'crypto'
import dayjs from 'dayjs'
import { DraftInvoice } from '../types/draft-invoice-type.js'
import { DraftInvoiceStatus } from '../enums/draft-invoice-status.enum.js'
import { MailStatus } from '../enums/mail-status.enum.js'

export class DraftInvoiceBuilder {
  private invoice: DraftInvoice

  constructor () {
    this.reset()
  }

  private reset (): this {
    this.invoice = {
      invoiceNumber: randomUUID(),
      status: DraftInvoiceStatus.APPROVED_BY_CUSTOMER,
      payerId: randomUUID(),
      payerName: randomUUID(),
      issuedOn: dayjs().toISOString(),
      firstReminderMailStatus: MailStatus.NOT_SENT,
      firstReminderOn: null,
      secondReminderMailStatus: MailStatus.NOT_SENT,
      secondReminderOn: null,
      thirdReminderMailStatus: MailStatus.NOT_SENT,
      autoApprovedOn: null,
      netAmount: '2',
      vatAmount: '2',
      currency: 'EUR',
      accountDocumentNumber: '123',
      customerApprovalBy: '<PERSON> Oed',
      customerApprovalDate: '2025-01-01',
      customerId: '12313',
      poNumber: '123123'
    }

    return this
  }

  withInvoiceNumber (invoiceNumber: string): this {
    this.invoice.invoiceNumber = invoiceNumber

    return this
  }

  withPayerId (payerId: string): this {
    this.invoice.payerId = payerId

    return this
  }

  withStatus (status: DraftInvoiceStatus): this {
    this.invoice.status = status

    return this
  }

  public build (): DraftInvoice {
    const result = this.invoice

    this.reset()

    return result
  }
}
