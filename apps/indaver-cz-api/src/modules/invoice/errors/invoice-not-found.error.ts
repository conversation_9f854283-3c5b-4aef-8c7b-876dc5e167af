import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { tc } from '../../localization/helpers/translate.helper.js'

export class InvoiceNotFoundErrorMeta {
  @ApiProperty({ type: String })
  invoiceNumber: string
}

export class InvoiceNotFoundError extends NotFoundApiError {
  @ApiErrorCode('invoice_not_found')
  code = 'invoice_not_found'

  @ApiErrorMeta()
  meta: InvoiceNotFoundErrorMeta

  constructor (meta: InvoiceNotFoundErrorMeta) {
    super(tc('error.invoice.not_found'))
    this.meta = meta
  }
}
