import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ConflictApiError } from '../../exceptions/api-errors/conflict.api-error.js'
import { tc } from '../../localization/helpers/translate.helper.js'

export class NonApproveOrRejectableDraftInvoiceError extends ConflictApiError {
  @ApiErrorCode('non_approve_or_rejectable_draft_invoice')
  code = 'non_approve_or_rejectable_draft_invoice'

  meta: never

  constructor () {
    super(tc('error.invoice.non_approve_or_rejectable_draft_invoice'))
  }
}
