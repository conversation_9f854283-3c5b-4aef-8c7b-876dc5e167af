import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { I18nPath } from '../../localization/generated/i18n.generated.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class InvalidInvoiceStatusCombinationError extends BadRequestApiError {
  @ApiErrorCode('invalid_invoice_status_combination')
  readonly code = 'invalid_invoice_status_combination'

  readonly meta: never

  constructor (detail?: I18nPath, source?: ErrorSource) {
    const translatedDetail = translateCurrent(
      detail ?? 'error.waste-inquiry.invalid_waste_inquiry_status_combination_default'
    )

    super(
      translatedDetail,
      source
    )
  }
}
