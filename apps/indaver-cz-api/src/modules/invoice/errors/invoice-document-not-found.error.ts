import { ApiProperty } from '@nestjs/swagger'
import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { ApiErrorMeta } from '../../exceptions/api-errors/api-error-meta.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class InvoiceDocumentNotFoundErrorMeta {
  @ApiProperty({ type: String })
  invoiceNumber: string

  constructor (invoiceNumber: string) {
    this.invoiceNumber = invoiceNumber
  }
}

export class InvoiceDocumentNotFoundError extends NotFoundApiError {
  @ApiErrorCode('invoice_document_not_found')
  code = 'invoice_document_not_found'

  @ApiErrorMeta()
  meta: InvoiceDocumentNotFoundErrorMeta

  constructor (meta: InvoiceDocumentNotFoundErrorMeta) {
    super(translateCurrent('error.invoice.document_not_found'))
    this.meta = meta
  }
}
