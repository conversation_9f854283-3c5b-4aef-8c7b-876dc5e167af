import { describe, it } from 'node:test'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { InvoiceStatus } from '../../enums/invoice-status.enum.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../../sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'
import { InvoiceStatusMapper } from '../invoice-status.mapper.js'

describe('InvoiceStatusMapper - Unit Tests', () => {
  it(`maps ${InvoiceStatus.CLEARED} invoice correctly`, () => {
    const sapResponse = new SapGetInvoiceIndexResponseBuilder()
      .withAugbl('123132323213213')
      .withDueDate(dayjs().toDate())
      .build()

    const status = InvoiceStatusMapper.fromSapResponse(sapResponse)

    expect(status).toBe(InvoiceStatus.CLEARED)
  })

  it(`maps ${InvoiceStatus.OUTSTANDING} invoice correctly`, (context) => {
    context.mock.timers.enable({ apis: ['Date'] })
    context.mock.timers.setTime(dayjs().toDate().getTime())

    const sapResponse = new SapGetInvoiceIndexResponseBuilder()
      .withAugbl('')
      .withDueDate(dayjs().toDate())
      .build()

    const status = InvoiceStatusMapper.fromSapResponse(sapResponse)

    context.mock.timers.reset()

    expect(status).toBe(InvoiceStatus.OUTSTANDING)
  })

  it(`maps ${InvoiceStatus.OVERDUE} invoice correctly`, () => {
    const sapResponse = new SapGetInvoiceIndexResponseBuilder()
      .withAugbl('')
      .withDueDate(dayjs().subtract(1, 'week').toDate())
      .build()

    const status = InvoiceStatusMapper.fromSapResponse(sapResponse)

    expect(status).toBe(InvoiceStatus.OVERDUE)
  })
})
