import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { DraftInvoiceStatus } from '../../enums/draft-invoice-status.enum.js'
import { SapGetDraftInvoiceIndexResponseBuilder } from '../../../sap/use-cases/get-draft-invoice-index/tests/builders/get-draft-invoice-index.response.builder.js'
import { DraftInvoiceStatusMapper } from '../draft-invoice-status.mapper.js'

describe('DraftInvoiceStatusMapper - Unit tests', () => {
  before(() => TestBench.setupUnitTest())

  describe('A1', () => {
    it(`maps to ${DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('')
        .withStatus3('4')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER)
    })

    it(`maps to ${DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.APPROVED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('2')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.APPROVED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.REJECTED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('3')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.REJECTED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.AUTO_APPROVED} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withStatus3('4')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.AUTO_APPROVED)
    })

    it(`maps to ${DraftInvoiceStatus.REJECTED_BY_INDAVER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('3')
        .withStatus3('4')
        .withKatr6('A1')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.REJECTED_BY_INDAVER)
    })

    it('throws error if no mapping was found', () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('1')
        .withStatus3('4')
        .withKatr6('A1')
        .build()

      expect(() => DraftInvoiceStatusMapper.fromSapResponse(response)).toThrow()
    })
  })

  describe('A2', () => {
    it(`maps to ${DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus3('')
        .withKatr6('A2')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.APPROVED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus3('2')
        .withKatr6('A2')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.APPROVED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.REJECTED_BY_CUSTOMER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus3('3')
        .withKatr6('A2')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.REJECTED_BY_CUSTOMER)
    })

    it(`maps to ${DraftInvoiceStatus.AUTO_APPROVED} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus3('4')
        .withKatr6('A2')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.AUTO_APPROVED)
    })
  })

  describe('A3', () => {
    it(`maps to ${DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('')
        .withKatr6('A3')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER)
    })

    it(`maps to ${DraftInvoiceStatus.REJECTED_BY_INDAVER} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('3')
        .withKatr6('A3')
        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.REJECTED_BY_INDAVER)
    })

    it(`maps to ${DraftInvoiceStatus.INTERNAL_APPROVED} correctly`, () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('4')
        .withKatr6('A3')

        .build()

      const result = DraftInvoiceStatusMapper.fromSapResponse(response)

      expect(result).toBe(DraftInvoiceStatus.INTERNAL_APPROVED)
    })

    it('throws error if no mapping was found', () => {
      const response = new SapGetDraftInvoiceIndexResponseBuilder()
        .withStatus2('2')
        .withKatr6('A3')
        .build()

      expect(() => DraftInvoiceStatusMapper.fromSapResponse(response)).toThrow()
    })
  })
})
