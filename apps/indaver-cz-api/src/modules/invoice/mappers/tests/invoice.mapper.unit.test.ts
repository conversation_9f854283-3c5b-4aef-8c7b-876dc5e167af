import { describe, it } from 'node:test'
import { expect } from 'expect'
import { InvoiceMapper } from '../invoice.mapper.js'
import { SapGetInvoiceIndexResponseBuilder } from '../../../sap/use-cases/get-invoice-index/tests/builders/get-invoice-index.response.builder.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'

describe('InvoiceMapper - Unit Test', () => {
  it('maps the response to the correct invoice properties', () => {
    const response = new SapGetInvoiceIndexResponseBuilder()
      .build()

    const invoice = InvoiceMapper.fromSapResponse(response)

    expect(invoice).toStrictEqual(expect.objectContaining({
      invoiceNumber: response.Vbeln,
      issueDate: SapDateFormatterService.parseDateToString(response.Fkdat!),
      dueDate: SapDateFormatterService.parseDateToString(response.Duedate ?? null),
      customerName: response.KunagName1,
      companyName: response.Butxt,
      accountManagerName: response.SalesRep,
      accountDocumentNumber: response.Belnr === '' ? null : response.Belnr,
      currency: response.Waerk,
      vatAmount: response.Mwsbk,
      netAmount: response.Netwr,
      payerName: response.KunrgName1,
      payerId: response.Kunrg,
      customerReference: response.Bstkd === '' ? null : response.Bstkd
    }))
  })
})
