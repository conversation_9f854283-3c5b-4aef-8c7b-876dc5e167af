import { before, describe, it } from 'node:test'
import { expect } from 'expect'
import { TestBench } from '../../../../../test/setup/test-bench.js'
import { SapGetDraftInvoiceIndexResponseBuilder } from '../../../sap/use-cases/get-draft-invoice-index/tests/builders/get-draft-invoice-index.response.builder.js'
import { DraftInvoiceMapper } from '../draft-invoice.mapper.js'
import { SapDateFormatterService } from '../../../sap/services/sap-date-formatter.service.js'
import { DraftInvoiceStatusMapper } from '../draft-invoice-status.mapper.js'
import { MailStatus } from '../../enums/mail-status.enum.js'

describe('DraftInvoiceMapper - Unit Test', () => {
  before(() => TestBench.setupUnitTest())

  it('maps to a draft invoice', () => {
    const response = new SapGetDraftInvoiceIndexResponseBuilder()
      .build()

    const draftInvoice = DraftInvoiceMapper.fromSapResponse(response)

    expect(draftInvoice).toEqual(expect.objectContaining({
      invoiceNumber: response.Vbeln,
      payerId: response.Kunrg,
      payerName: `${response.Name1Rg} ${response.StreetRg} ${response.PostCode1Rg} ${response.City1Rg}`,
      issuedOn: SapDateFormatterService.parseDateToString(response.Fkdat!),
      firstReminderMailStatus: MailStatus.SENT,
      firstReminderOn: SapDateFormatterService.parseDateToString(response.St5Date ?? null),
      secondReminderMailStatus: MailStatus.SENT,
      secondReminderOn: SapDateFormatterService.parseDateToString(response.St6Date ?? null),
      thirdReminderMailStatus: MailStatus.SENT,
      autoApprovedOn: SapDateFormatterService.parseDateToString(response.St7Date ?? null),
      netAmount: response.Netwr,
      vatAmount: response.Mwsbk,
      currency: response.Waerk,
      status: DraftInvoiceStatusMapper.fromSapResponse(response),
      customerApprovalBy: response.St3Ernam,
      customerApprovalDate: SapDateFormatterService.parseDateToString(response.St3Erdat!),
      accountDocumentNumber: null,
      poNumber: null,
      customerId: response.Kunag
    }))
  })
})
