import assert from 'assert'
import { SapGetDraftInvoiceIndexResponse } from '../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { DraftInvoiceStatus } from '../enums/draft-invoice-status.enum.js'
import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'

export class DraftInvoiceStatusMapper {
  static fromSapResponse (
    sapResponse: SapGetDraftInvoiceIndexResponse
  ): DraftInvoiceStatus {
    switch (sapResponse.Katr6) {
      case 'A1': return this.fromA1(sapResponse)
      case 'A2': return this.fromA2(sapResponse)
      case 'A3': return this.fromA3(sapResponse)
      default:
        assert(sapResponse.Katr6 !== undefined)
        exhaustiveCheck(sapResponse.Katr6)
    }
  }

  private static fromA1 (
    sapResponse: SapGetDraftInvoiceIndexResponse
  ): DraftInvoiceStatus {
    if (sapResponse.Status2 === '') {
      return DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER
    }

    if (sapResponse.Status2 === '2') {
      if (sapResponse.Status3 === '') {
        return DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER
      }

      if (sapResponse.Status3 === '2') {
        return DraftInvoiceStatus.APPROVED_BY_CUSTOMER
      }

      if (sapResponse.Status3 === '3') {
        return DraftInvoiceStatus.REJECTED_BY_CUSTOMER
      }

      if (sapResponse.Status3 === '4') {
        return DraftInvoiceStatus.AUTO_APPROVED
      }
    }

    if (sapResponse.Status2 === '3') {
      return DraftInvoiceStatus.REJECTED_BY_INDAVER
    }

    throw new Error('Unsupported draft invoice status for A1')
  }

  private static fromA2 (
    sapResponse: SapGetDraftInvoiceIndexResponse
  ): DraftInvoiceStatus {
    if (sapResponse.Status3 === '') {
      return DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER
    }

    if (sapResponse.Status3 === '2') {
      return DraftInvoiceStatus.APPROVED_BY_CUSTOMER
    }

    if (sapResponse.Status3 === '3') {
      return DraftInvoiceStatus.REJECTED_BY_CUSTOMER
    }

    if (sapResponse.Status3 === '4') {
      return DraftInvoiceStatus.AUTO_APPROVED
    }

    throw new Error('Unsupported draft invoice status A2')
  }

  private static fromA3 (
    sapResponse: SapGetDraftInvoiceIndexResponse
  ): DraftInvoiceStatus {
    if (sapResponse.Status2 === '') {
      return DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER
    }

    if (sapResponse.Status2 === '3') {
      return DraftInvoiceStatus.REJECTED_BY_INDAVER
    }

    if (sapResponse.Status2 === '4') {
      return DraftInvoiceStatus.INTERNAL_APPROVED
    }

    throw new Error('Unsupported draft invoice status A3')
  }
}
