import assert from 'assert'
import dayjs from 'dayjs'
import { SapGetInvoiceIndexResponse } from '../../sap/use-cases/get-invoice-index/get-invoice-index.response.js'
import { InvoiceStatus } from '../enums/invoice-status.enum.js'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'

export class InvoiceStatusMapper {
  static fromSapResponse (
    sapResponse: SapGetInvoiceIndexResponse
  ): InvoiceStatus {
    assert(sapResponse.Augbl !== undefined, 'Augbl must be selected')

    if (sapResponse.Duedate == null) {
      return InvoiceStatus.OUTSTANDING
    }

    const dueDate = SapDateFormatterService.parseDate(sapResponse.Duedate)

    const isDue = dayjs().isSameOrAfter(dueDate)

    if (sapResponse.Augbl !== '') {
      return InvoiceStatus.CLEARED
    } else if (isDue) {
      return InvoiceStatus.OVERDUE
    } else {
      return InvoiceStatus.OUTSTANDING
    }
  }
}
