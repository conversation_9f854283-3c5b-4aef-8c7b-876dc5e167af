import assert from 'assert'
import { SapDateFormatterService } from '../../sap/services/sap-date-formatter.service.js'
import { SapGetDraftInvoiceIndexResponse } from '../../sap/use-cases/get-draft-invoice-index/get-draft-invoice-index.response.js'
import { MailStatus } from '../enums/mail-status.enum.js'
import { DraftInvoice } from '../types/draft-invoice-type.js'
import { DraftInvoiceStatusMapper } from './draft-invoice-status.mapper.js'

export class DraftInvoiceMapper {
  static fromSapResponse (response: SapGetDraftInvoiceIndexResponse): DraftInvoice {
    assert (response.Vbeln !== undefined)
    assert (response.Kunrg !== undefined)
    assert (response.Fkdat !== undefined)
    assert (response.Netwr !== undefined)
    assert (response.Mwsbk !== undefined)
    assert (response.Waerk !== undefined)
    assert (response.Kunag !== undefined)

    return {
      invoiceNumber: response.Vbeln,
      payerId: response.Kunrg,
      payerName: this.mapResponseToPayerName(response),
      issuedOn: SapDateFormatterService.parseDateToString(response.Fkdat)!,
      firstReminderMailStatus: this.mapResponseToMailStatus(response.St5Mail),
      firstReminderOn: SapDateFormatterService.parseDateToString(response.St5Date ?? null),
      secondReminderMailStatus: this.mapResponseToMailStatus(response.St6Mail),
      secondReminderOn: SapDateFormatterService.parseDateToString(response.St6Date ?? null),
      thirdReminderMailStatus: this.mapResponseToMailStatus(response.St7Mail),
      autoApprovedOn: SapDateFormatterService.parseDateToString(response.St7Date ?? null),
      netAmount: response.Netwr,
      vatAmount: response.Mwsbk,
      currency: response.Waerk,
      status: DraftInvoiceStatusMapper.fromSapResponse(response),
      customerApprovalBy: response.St3Ernam !== undefined && response.St3Ernam !== ''
        ? response.St3Ernam
        : null,
      customerApprovalDate: response.St3Erdat !== undefined && response.St3Erdat !== ''
        ? SapDateFormatterService.parseDateToString(response.St3Erdat)
        : null,
      accountDocumentNumber: null, // TODO IND-1008 not delivered yet
      poNumber: null, // TODO IND-1008 not delivered yet
      customerId: response.Kunag
    }
  }

  static fromSapResponses (
    responses: SapGetDraftInvoiceIndexResponse[]
  ): DraftInvoice[] {
    return responses.map(response => this.fromSapResponse(response))
  }

  static mapResponseToMailStatus (response?: string): MailStatus {
    return response === 'X' ? MailStatus.SENT : MailStatus.NOT_SENT
  }

  private static mapResponseToPayerName (
    response: SapGetDraftInvoiceIndexResponse
  ): string {
    return `${response.Name1Rg} ${response.StreetRg} ${response.PostCode1Rg} ${response.City1Rg}`
  }
}
