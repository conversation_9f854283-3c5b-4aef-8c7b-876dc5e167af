import { DraftInvoiceStatus } from '../enums/draft-invoice-status.enum.js'
import { MailStatus } from '../enums/mail-status.enum.js'

export interface DraftInvoiceDynamicTableFields {
  invoiceNumber: string
  status: DraftInvoiceStatus
  payerId: string
  payerName: string
  issuedOn: string
  firstReminderMailStatus: MailStatus
  firstReminderOn: string | null
  secondReminderMailStatus: MailStatus
  secondReminderOn: string | null
  thirdReminderMailStatus: MailStatus
  autoApprovedOn: string | null
  netAmount: string
  vatAmount: string
  currency: string
  customerApprovalBy: string | null
  customerApprovalDate: string | null
  poNumber: string | null
  accountDocumentNumber: string | null
  customerId: string
}
