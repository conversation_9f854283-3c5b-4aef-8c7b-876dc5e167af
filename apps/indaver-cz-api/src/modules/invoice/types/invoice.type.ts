import { InvoiceStatus } from '../enums/invoice-status.enum.js'
import { InvoiceType } from '../enums/invoice-type.enum.js'

export interface Invoice {
  invoiceNumber: string
  status: InvoiceStatus
  issueDate: string
  customerId: string
  customerName: string
  dueDate: string | null
  customerReference: string | null
  type: InvoiceType
  payerId: string
  payerName: string
  netAmount: string
  vatAmount: string
  currency: string
  accountDocumentNumber: string | null
  accountManagerName: string | null
  companyName: string
}
