import { InvoiceStatus } from '../enums/invoice-status.enum.js'
import { InvoiceType } from '../enums/invoice-type.enum.js'

export interface InvoiceDynamicTableFields {
  invoiceNumber: string
  status: InvoiceStatus
  issuedOn: string
  customerId: string
  customerName: string
  dueOn: string | null
  customerReference: string | null
  type: InvoiceType
  payerId: string
  payerName: string
  netAmount: string
  vatAmount: string
  currency: string
  accountDocumentNumber: string | null
  accountManagerName: string | null
  companyName: string
}
