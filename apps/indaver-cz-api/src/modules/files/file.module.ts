import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { ConfigModule } from '@nestjs/config'
import Joi from 'joi'
import { UserModule } from '../../app/users/user.module.js'
import { File } from './entities/file.entity.js'
import { FileLink } from './entities/file-link.entity.js'
import { FileLinkService } from './services/file-link.service.js'
import { AzureBlobService } from './services/azure-blob.service.js'
import { CreateFileModule } from './use-cases/create-file/create-file.module.js'
import { ConfirmFileUploadModule } from './use-cases/confirm-file-upload/confirm-file-upload.module.js'
import { DownloadFileModule } from './use-cases/download-file/download-file.module.js'
import { ProxyExternalFileModule } from './use-cases/proxy-external-file/proxy-external-file.module.js'

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: process.env.ENV_FILE,
      validationSchema: Joi.object({
        AZURE_BLOB_ENDPOINT: Joi.string().uri().required(),
        AZURE_BLOB_ACCOUNT_NAME: Joi.string().required(),
        AZURE_BLOB_ACCOUNT_KEY: Joi.string().required(),
        AZURE_BLOB_CONTAINER_NAME: Joi.string().required()
      })
    }),
    TypeOrmModule.forFeature([File, FileLink]),
    UserModule,

    CreateFileModule,
    ConfirmFileUploadModule,
    DownloadFileModule,
    ProxyExternalFileModule
  ],
  providers: [
    FileLinkService,
    AzureBlobService

    // RemoveUnusedMediaJobHandler,
    // RemoveUnusedMediaCron
  ],
  exports: [
    FileLinkService,
    AzureBlobService
  ]
})
export class FileModule {}
