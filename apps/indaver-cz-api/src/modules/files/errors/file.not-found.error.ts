import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { NotFoundApiError } from '../../exceptions/api-errors/not-found.api-error.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class FileNotFoundError extends NotFoundApiError {
  @ApiErrorCode('file_not_found')
  code: 'file_not_found'

  meta: never

  constructor (fileUuid: string | string[]) {
    const detail = translateCurrent('error.files.file_not_found', {
      args: { fileUuid: fileUuid }
    })
    super(detail)
    this.code = 'file_not_found'
  }
}
