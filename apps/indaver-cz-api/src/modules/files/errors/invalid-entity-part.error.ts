import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class InvalidEntityPartError extends BadRequestApiError {
  @ApiErrorCode('invalid_entity_part')
  readonly code = 'invalid_entity_part'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.files.invalid_entity_part')
    super(detail, source)
  }
}
