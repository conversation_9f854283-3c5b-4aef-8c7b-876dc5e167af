import { ApiErrorCode } from '../../exceptions/api-errors/api-error-code.decorator.js'
import { BadRequestApiError } from '../../exceptions/api-errors/bad-request.api-error.js'
import { ErrorSource } from '../../exceptions/types/json-api-error.type.js'
import { translateCurrent } from '../../localization/helpers/translate.helper.js'

export class FileNotAccessibleError extends BadRequestApiError {
  @ApiErrorCode('file_not_accessible')
  readonly code = 'file_not_accessible'

  readonly meta: never

  constructor (source?: ErrorSource) {
    const detail = translateCurrent('error.files.file_not_accessible')
    super(detail, source)
  }
}
