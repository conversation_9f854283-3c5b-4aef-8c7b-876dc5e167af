import { Controller, Get, Query, Res } from '@nestjs/common'
import { ApiOAuth2, ApiTags } from '@nestjs/swagger'
import type { Response } from 'express'
import { FileStreamUtil } from '../../../../utils/streams/file-stream.util.js'
import { ProxyExternalFileQuery } from './proxy-external-file.query.js'

@ApiTags('File')
@Controller('files/proxy-external')
@ApiOAuth2([])
export class ProxyExternalFileController {
  @Get()
  async proxyExternalFile (
    @Query() query: ProxyExternalFileQuery,
    @Res() res: Response
  ): Promise<void> {
    await FileStreamUtil.pipeFileResponse(res, query.url)
  }
}
