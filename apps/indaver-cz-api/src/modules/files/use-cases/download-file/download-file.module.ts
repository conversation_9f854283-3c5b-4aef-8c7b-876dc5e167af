import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { File } from '../../entities/file.entity.js'
import { AzureBlobService } from '../../services/azure-blob.service.js'
import { DownloadFileController } from './download-file.controller.js'
import { DownloadFileUseCase } from './download-file.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([File])
  ],
  controllers: [
    DownloadFileController
  ],
  providers: [
    DownloadFileUseCase,
    AzureBlobService
  ]
})
export class DownloadFileModule {}
