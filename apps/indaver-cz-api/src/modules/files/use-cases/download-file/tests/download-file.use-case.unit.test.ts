import { afterEach, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { EntityNotFoundError, Repository } from 'typeorm'
import { randUrl } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { File } from '../../../entities/file.entity.js'
import { DownloadFileUseCase } from '../download-file.use-case.js'
import { AzureBlobService } from '../../../services/azure-blob.service.js'
import { FileEntityBuilder } from '../../../entities/file-entity.builder.js'

describe('Download file use case unit tests', () => {
  let useCase: DownloadFileUseCase

  let file: File
  let url: string

  let fileRepository: SinonStubbedInstance<Repository<File>>
  let azureBlobService: SinonStubbedInstance<AzureBlobService>

  before(() => {
    TestBench.setupUnitTest()

    file = new FileEntityBuilder().build()
    url = randUrl()

    fileRepository = createStubInstance<Repository<File>>(Repository<File>)
    azureBlobService = createStubInstance(AzureBlobService)

    useCase = new DownloadFileUseCase(
      fileRepository,
      azureBlobService
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()

    mockMethods()
  })

  function mockMethods () {
    fileRepository.findOneByOrFail.resolves(file)
    azureBlobService.createTemporaryDownloadUrl.returns(url)
  }

  it('should return 404 when the file does not exist', async () => {
    fileRepository.findOneByOrFail.rejects(new EntityNotFoundError(File, {}))

    await expect(useCase.execute(randomUUID())).rejects.toThrow()
    assert.notCalled(azureBlobService.createTemporaryDownloadUrl)
  })

  it('should return a file and a temporary URL', async () => {
    const file = new File()
    file.uuid = randomUUID()
    fileRepository.findOneByOrFail.resolves(file)

    const result = await useCase.execute(file.uuid)

    expect(result.file).toEqual(file)
    expect(result.temporaryUrl).toBe(url)
    assert.calledOnce(azureBlobService.createTemporaryDownloadUrl)
  })
})
