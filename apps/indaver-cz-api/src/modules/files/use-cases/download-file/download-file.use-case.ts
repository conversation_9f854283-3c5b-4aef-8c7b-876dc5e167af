import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { File } from '../../entities/file.entity.js'
import { AzureBlobService } from '../../services/azure-blob.service.js'

@Injectable()
export class DownloadFileUseCase {
  constructor (
    @InjectRepository(File)
    private fileRepository: Repository<File>,
    private readonly azureBlobService: AzureBlobService
  ) {}

  async execute (fileUuid: string): Promise<{ file: File, temporaryUrl: string }> {
    const file = await this.fileRepository.findOneByOrFail({ uuid: fileUuid })
    const temporaryUrl = this.azureBlobService.createTemporaryDownloadUrl(file)

    return { file, temporaryUrl }
  }
}
