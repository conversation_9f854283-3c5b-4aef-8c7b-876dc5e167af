import { Controller, Post, HttpCode } from '@nestjs/common'
import { <PERSON>piTag<PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { ConfirmFileUploadUseCase } from './confirm-file-upload.use-case.js'

@ApiTags('File')
@Controller('files/:file/confirm-upload')
@ApiOAuth2([])
export class ConfirmFileUploadController {
  constructor (
    private readonly useCase: ConfirmFileUploadUseCase
  ) {}

  @Post()
  @HttpCode(200)
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiOkResponse()
  async confirmFileUpload (
    @UuidParam('file') fileUuid: string
  ): Promise<void> {
    await this.useCase.execute(fileUuid)
  }
}
