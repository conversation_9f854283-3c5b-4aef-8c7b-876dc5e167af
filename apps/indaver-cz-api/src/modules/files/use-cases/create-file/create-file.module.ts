import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { File } from '../../entities/file.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { AzureBlobService } from '../../services/azure-blob.service.js'
import { CreateFileController } from './create-file.controller.js'
import { CreateFileUseCase } from './create-file.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([File]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateFileController
  ],
  providers: [
    CreateFileUseCase,
    AzureBlobService
  ]
})
export class CreateFileModule {}
