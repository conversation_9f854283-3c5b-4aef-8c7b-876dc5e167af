import { randomUUID } from 'node:crypto'
import { Injectable } from '@nestjs/common'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { DataSource, Repository } from 'typeorm'
import { File } from '../../entities/file.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { AzureBlobService } from '../../services/azure-blob.service.js'
import { CreateFileCommand } from './create-file.command.js'
import { CreateFileResponse } from './create-file.response.js'
import { FileCreatedEvent } from './file-created.event.js'

@Injectable()
export class CreateFileUseCase {
  constructor (
    private readonly authContext: AuthContext,
    @InjectRepository(File)
    private fileRepository: Repository<File>,
    private readonly azureBlobService: AzureBlobService,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (command: CreateFileCommand): Promise<CreateFileResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()
    const file = this.fileRepository.create({
      uuid: randomUUID(),
      name: command.name,
      mimeType: command.mimeType,
      userUuid: userUuid
    })

    const uploadUrl = await this.azureBlobService.createTemporaryUploadUrl(file)

    await transaction(this.dataSource, async () => {
      await this.fileRepository.insert(file)
      await this.eventEmitter.emitOne(new FileCreatedEvent(file))
    })

    return new CreateFileResponse(file, uploadUrl)
  }
}
