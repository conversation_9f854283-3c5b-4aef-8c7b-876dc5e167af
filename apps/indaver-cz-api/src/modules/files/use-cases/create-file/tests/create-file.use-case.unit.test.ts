import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance } from 'sinon'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { CreateFileUseCase } from '../create-file.use-case.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { FileEntityBuilder } from '../../../entities/file-entity.builder.js'
import { FileCreatedEvent } from '../file-created.event.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { AzureBlobService } from '../../../services/azure-blob.service.js'
import { CreateFileCommand } from '../create-file.command.js'
import { CreateFileCommandBuilder } from './create-file.command.builder.js'

describe('CreateFile use case unit tests', () => {
  let useCase: CreateFileUseCase

  let command: CreateFileCommand

  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    command = new CreateFileCommandBuilder().build()

    eventEmitter = createStubInstance(DomainEventEmitter)

    const authContext = createStubInstance(AuthContext, {
      getUserUuid: randomUUID()
    })
    const fileRepository = createStubInstance(Repository, {
      create: {
        name: command.name,
        mimeType: command.mimeType,
        userUuid: randomUUID()
      }
    })
    const azureBlobService = createStubInstance(AzureBlobService)

    useCase = new CreateFileUseCase(
      authContext,
      fileRepository,
      azureBlobService,
      stubDataSource(),
      eventEmitter
    )
  })

  it('emits an event when a file is created', async () => {
    const response = await useCase.execute(command)

    const expectedFile = new FileEntityBuilder()
      .withUuid(response.uuid)
      .withName(command.name)
      .build()

    expect(eventEmitter).toHaveEmitted(new FileCreatedEvent(expectedFile))
  })
})
