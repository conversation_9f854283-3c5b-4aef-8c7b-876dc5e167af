import { <PERSON>, <PERSON>, Body } from '@nestjs/common'
import { <PERSON>pi<PERSON><PERSON><PERSON>, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permissions } from '../../../../modules/permission/permission.decorator.js'
import { Permission } from '../../../../modules/permission/permission.enum.js'
import { CreateFileCommand } from './create-file.command.js'
import { CreateFileUseCase } from './create-file.use-case.js'
import { CreateFileResponse } from './create-file.response.js'

@ApiTags('File')
@Controller('files')
@ApiOAuth2([])
export class CreateFileController {
  constructor (
    private readonly useCase: CreateFileUseCase
  ) {}

  @Post()
  @Permissions(
    Permission.WASTE_INQUIRY_MANAGE,
    Permission.WASTE_INQUIRY_READ,
    Permission.PICK_UP_REQUEST_MANAGE,
    Permission.PICK_UP_REQUEST_READ,
    Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
    Permission.WEEKLY_PLANNING_REQUEST_READ
  )
  @ApiCreatedResponse({
    type: CreateFileResponse
  })
  async createFile (
    @Body() command: CreateFileCommand
  ): Promise<CreateFileResponse> {
    return await this.useCase.execute(command)
  }
}
