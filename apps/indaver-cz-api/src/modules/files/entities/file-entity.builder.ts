import { randomUUID } from 'crypto'
import { MimeType } from '../enums/mime-type.enum.js'
import { File } from './file.entity.js'

export class FileEntityBuilder {
  private file: File

  constructor () {
    this.reset()
  }

  reset (): this {
    this.file = new File()

    this.file.uuid = randomUUID()
    this.file.name = 'test.png'
    this.file.mimeType = MimeType.PNG

    return this
  }

  withUuid (uuid: string): this {
    this.file.uuid = uuid

    return this
  }

  withName (name: string): this {
    this.file.name = name

    return this
  }

  withUserUuid (userUuid: string): this {
    this.file.userUuid = userUuid

    return this
  }

  withFileName (name: string): this {
    this.file.name = name

    return this
  }

  withMimeType (mimeType: MimeType): this {
    this.file.mimeType = mimeType

    return this
  }

  build (): File {
    const result = this.file

    this.reset()

    return result
  }
}
