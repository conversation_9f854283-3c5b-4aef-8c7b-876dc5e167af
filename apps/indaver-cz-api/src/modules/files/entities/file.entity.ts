import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, DeleteDateColumn, Column, OneToMany, type Relation, OneToOne, Index } from 'typeorm'
import type { MimeType } from '../enums/mime-type.enum.js'
import { NewsItem } from '../../news/entities/news-item.entity.js'
import { FileLink } from './file-link.entity.js'

@Entity()
export class File {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @DeleteDateColumn({ precision: 3 })
  deletedAt: Date | null

  @Column({ type: 'varchar' })
  name: string

  @Column({ type: 'varchar' })
  mimeType: MimeType

  @Index()
  @Column({ type: 'uuid', nullable: true })
  userUuid: string | null

  @Column({ type: 'boolean', default: false })
  isUploadConfirmed: boolean

  @OneToMany(() => FileLink, fileLink => fileLink.file)
  fileEntities?: Array<Relation<FileLink>>

  @OneToOne(() => NewsItem, newsItem => newsItem.image)
  newsItem?: Relation<File>

  url: string

  // @Column({ type: 'varchar', default: () => 'uuid_generate_v4()' })
  // key: string

  // @Column({ type: 'jsonb', default: [] })
  // variants: FileVariant[]

  // @Column({ type: 'uuid', nullable: true })
  // uploaderUuid: string | null

  // @ManyToOne(() => User)
  // uploader?: Relation<User> | null
}
