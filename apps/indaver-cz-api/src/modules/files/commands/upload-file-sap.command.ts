import { ApiProperty } from '@nestjs/swagger'
import { IsUUID, IsEnum } from 'class-validator'
import { EntityPart } from '../enums/entity-part.enum.js'

export abstract class UploadDocumentCommand {
  @ApiProperty({ type: String, format: 'uuid' })
  @IsUUID()
  fileUuid: string

  @ApiProperty({ type: String, enum: EntityPart, enumName: 'EntityPart' })
  @IsEnum(EntityPart)
  entityPart: EntityPart
}
