import { Injectable, OnApplicationBootstrap } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { BlobSASPermissions, BlobServiceClient, ContainerClient, generateBlobSASQueryParameters, StorageSharedKeyCredential } from '@azure/storage-blob'
import { captureError } from 'rxjs/internal/util/errorContext'
import dayjs from 'dayjs'
import { File } from '../entities/file.entity.js'

@Injectable()
export class AzureBlobService implements OnApplicationBootstrap {
  private sharedKeyCredential: StorageSharedKeyCredential
  private client: BlobServiceClient
  private containerClient: ContainerClient

  constructor (
    private readonly configService: ConfigService
  ) {}

  onApplicationBootstrap () {
    try {
      this.sharedKeyCredential = new StorageSharedKeyCredential(
        this.configService.getOrThrow('AZURE_BLOB_ACCOUNT_NAME'),
        this.configService.getOrThrow('AZURE_BLOB_ACCOUNT_KEY')
      )

      let url = this.configService.getOrThrow<string>('AZURE_BLOB_ENDPOINT')

      if (this.configService.getOrThrow<string>('NODE_ENV') === 'local') {
        url += `/${this.configService.getOrThrow<string>('AZURE_BLOB_ACCOUNT_NAME')}`
      }

      this.client = new BlobServiceClient(
        url,
        this.sharedKeyCredential
      )

      this.containerClient = this.client.getContainerClient(
        this.configService.getOrThrow('AZURE_BLOB_CONTAINER_NAME')
      )
    } catch (error) {
      captureError(error)
    }
  }

  public createTemporaryDownloadUrl (
    file: File,
    expiresInSeconds?: number
  ): string {
    const blobName = this.getBlobName(file)
    const blobClient = this.containerClient.getBlockBlobClient(blobName)

    const sasToken = generateBlobSASQueryParameters({
      containerName: this.configService.getOrThrow('AZURE_BLOB_CONTAINER_NAME'),
      blobName: blobName,
      expiresOn: dayjs().add(expiresInSeconds ?? 1800, 'second').toDate(),
      permissions: BlobSASPermissions.parse('r')
    }, this.sharedKeyCredential).toString()

    const downloadUrl = `${blobClient.url}?${sasToken}`

    return downloadUrl
  }

  public async createTemporaryUploadUrl (
    file: File,
    expiresInSeconds?: number
  ): Promise<string> {
    const blobClient = this.containerClient.getBlockBlobClient(this.getBlobName(file))

    const options = {
      blobHTTPHeaders: {
        blobContentType: file.mimeType
      },
      permissions: BlobSASPermissions.from({
        write: true
      }),
      expiresOn: dayjs().add(expiresInSeconds ?? 1800, 'second').toDate()
    }

    return await blobClient.generateSasUrl(options)
  }

  public async delete (
    file: File
  ): Promise<void> {
    const blobClient = this.containerClient.getBlockBlobClient(this.getBlobName(file))

    await blobClient.deleteIfExists()
  }

  public async downloadFileBuffer (
    file: File
  ): Promise<Buffer> {
    const blobClient = this.containerClient.getBlockBlobClient(this.getBlobName(file))

    return blobClient.downloadToBuffer()
  }

  private getBlobName (
    file: File
  ): string {
    const env: string = this.configService.get('NODE_ENV', 'local')
    const suffix = file.name.split('.').pop()

    return `${env}/${file.uuid}.${suffix}`
  }
}
