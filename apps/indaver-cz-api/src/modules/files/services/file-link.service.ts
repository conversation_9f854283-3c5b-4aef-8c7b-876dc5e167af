import { Injectable } from '@nestjs/common'
import { Any, DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import type { CreateFileLinkCommand } from '../commands/create-file-link.command.js'
import { FileLink } from '../entities/file-link.entity.js'
import { AzureBlobService } from './azure-blob.service.js'

@Injectable()
export class FileLinkService {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(FileLink)
    private readonly fileLinkRepository: Repository<FileLink>,
    private readonly azureBlobService: AzureBlobService
  ) {}

  async loadFileLinks (
    entityUuid: string,
    entityType: string,
    entityPart: string
  ): Promise<FileLink[]> {
    return await this.fileLinkRepository.find({
      where: {
        entityUuid,
        entityType,
        entityPart
      },
      order: {
        order: 'ASC'
      },
      relations: {
        file: true
      }
    })
  }

  async loadFileLinksGroupedByEntityParts (
    entityUuid: string,
    entityType: string,
    entityParts: string[]
  ): Promise<FileLink[]> {
    return await this.fileLinkRepository.find({
      where: {
        entityUuid,
        entityType,
        entityPart: Any(entityParts)
      },
      order: {
        order: 'ASC'
      },
      relations: {
        file: true
      }
    })
  }

  async create (
    command: CreateFileLinkCommand,
    entityUuid: string,
    entityType: string,
    entityPart: string
  ): Promise<FileLink> {
    const file = this.fileLinkRepository.create({
      ...command,
      entityUuid,
      entityType,
      entityPart
    })

    await this.fileLinkRepository.insert(file)

    return file
  }

  async update (
    link: FileLink,
    command: CreateFileLinkCommand
  ): Promise<void> {
    await this.fileLinkRepository.update({
      uuid: link.uuid
    }, command)
  }

  async sync (
    commands: CreateFileLinkCommand[],
    entityUuid: string,
    entityType: string,
    entityPart: string
  ): Promise<void> {
    const previousFileLinks = await this.fileLinkRepository.find({
      where: {
        entityPart,
        entityUuid,
        entityType
      }
    })

    const removedFileLinks = previousFileLinks.filter((existing) => {
      return !commands.some(command => command.fileUuid === existing.fileUuid)
    })

    const upsertFileLinks = commands.map((command) => {
      const existing = previousFileLinks.find((link) => {
        return link.fileUuid === command.fileUuid
      })

      return {
        uuid: existing?.uuid,
        fileUuid: command.fileUuid,
        order: command.order,
        entityType: entityType,
        entityUuid: entityUuid,
        entityPart: entityPart
      }
    })

    await transaction(this.dataSource, async () => {
      await this.fileLinkRepository.delete({
        uuid: Any(removedFileLinks.map(link => link.uuid))
      })

      await this.fileLinkRepository.upsert(
        upsertFileLinks,
        ['uuid']
      )
    })
  }

  async delete (
    entityUuid: string,
    entityType: string
  ): Promise<void> {
    await this.fileLinkRepository.delete({
      entityUuid,
      entityType
    })
  }

  async deleteMany (
    entityUuids: string[],
    entityType: string
  ) {
    await this.fileLinkRepository.delete({
      entityUuid: Any(entityUuids),
      entityType
    })
  }

  loadTempUrlOnFileLinks (fileLinks: FileLink[]): void {
    for (const fileLink of fileLinks) {
      if (fileLink.file !== undefined) {
        fileLink.file.url = this.azureBlobService.createTemporaryDownloadUrl(
          fileLink.file
        )
      }
    }
  }
}
