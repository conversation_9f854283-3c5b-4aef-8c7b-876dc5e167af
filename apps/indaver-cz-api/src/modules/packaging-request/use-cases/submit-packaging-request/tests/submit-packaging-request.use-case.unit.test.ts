import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { randNumber } from '@ngneat/falso'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { SubmitPackagingRequestUseCase } from '../submit-packaging-request.use-case.js'
import { SubmitPackagingRequestValidator } from '../submit-packaging-request.validator.js'
import { PackagingRequest } from '../../../types/packaging-request.type.js'
import { ValidPackagingRequestEntityBuilder } from '../../../tests/valid-packaging-request-entity.builder.js'
import { PackagingRequestSubmittedEvent } from '../packaging-request-submitted.event.js'
import { CreatePackagingRequestSapUseCase } from '../../create-packaging-request-sap/create-packaging-request-sap.use-case.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { AuthContext } from '../../../../auth/auth.context.js'

describe('Submit packaging request use-case unit test', () => {
  let useCase: SubmitPackagingRequestUseCase

  let validator: SinonStubbedInstance<SubmitPackagingRequestValidator>
  let createPackagingRequestSapUseCase: SinonStubbedInstance<CreatePackagingRequestSapUseCase>
  let packagingRequestRepository: SinonStubbedInstance<Repository<PackagingRequest>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>
  let authContext: SinonStubbedInstance<AuthContext>

  before(() => {
    TestBench.setupUnitTest()

    const validPackagingRequest = new ValidPackagingRequestEntityBuilder().build()

    validator = createStubInstance(SubmitPackagingRequestValidator, {
      validate: Promise.resolve(validPackagingRequest)
    })
    createPackagingRequestSapUseCase = createStubInstance(CreatePackagingRequestSapUseCase, {
      execute: Promise.resolve(randNumber({ min: 10000000, max: 99999999 }).toString())
    })

    packagingRequestRepository = createStubInstance<Repository<PackagingRequest>>(
      Repository<PackagingRequest>, {
        findOneByOrFail: Promise.resolve(validPackagingRequest),
        save: Promise.resolve(validPackagingRequest)
      }
    )
    eventEmitter = createStubInstance(DomainEventEmitter)
    authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: randomUUID()
    })

    useCase = new SubmitPackagingRequestUseCase(
      authContext,
      stubDataSource(),
      validator,
      createPackagingRequestSapUseCase,
      packagingRequestRepository,
      eventEmitter
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(randomUUID())

    assert.calledOnce(validator.validate)
    assert.calledOnce(createPackagingRequestSapUseCase.execute)
    assert.calledOnce(packagingRequestRepository.save)
  })

  it('Emits a PackagingRequestSubmittedEvent event', async () => {
    const packagingRequestUuid = randomUUID()

    await useCase.execute(packagingRequestUuid)

    expect(eventEmitter).toHaveEmitted(new PackagingRequestSubmittedEvent(packagingRequestUuid))
  })
})
