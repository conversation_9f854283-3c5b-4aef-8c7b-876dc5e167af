import { ApiProperty } from '@nestjs/swagger'
import { AnnouncementTranslation } from '../entities/announcement-translation.entity.js'
import { Locale } from '../../localization/enums/locale.enum.js'

export class ViewAnnouncementTranslationResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String })
  title: string

  @ApiProperty({ type: Object })
  content: object

  @ApiProperty({ type: String, enum: Locale, enumName: 'Locale' })
  language: Locale

  constructor (translation: AnnouncementTranslation) {
    this.uuid = translation.uuid
    this.createdAt = translation.createdAt.toISOString()
    this.updatedAt = translation.updatedAt.toISOString()
    this.title = translation.title
    this.content = translation.content
    this.language = translation.language
  }
}
