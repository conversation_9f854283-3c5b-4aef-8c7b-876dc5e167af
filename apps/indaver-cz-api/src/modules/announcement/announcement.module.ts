import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from './entities/announcement.entity.js'
import { AnnouncementTranslation } from './entities/announcement-translation.entity.js'
import { CreateAnnouncementModule } from './use-cases/create-announcement/create-announcement.module.js'
import { UpdateAnnouncementModule } from './use-cases/update-announcement/update-announcement.module.js'
import { ViewDashboardAnnouncementIndexModule } from './use-cases/view-dashboard-announcement-index/view-dashboard-announcement-index.module.js'
import { ViewDashboardAnnouncementModule } from './use-cases/view-dashboard-announcement/view-dashboard-announcement.module.js'
import { ViewAnnouncementModule } from './use-cases/view-announcement/view-announcement.module.js'
import { ViewAnnouncementIndexModule } from './use-cases/view-news-item-index/view-announcement-index.module.js'
import { DeleteAnnouncementModule } from './use-cases/delete-announcement/delete-announcement.module.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    CreateAnnouncementModule,
    UpdateAnnouncementModule,
    ViewDashboardAnnouncementIndexModule,
    ViewDashboardAnnouncementModule,
    ViewAnnouncementModule,
    ViewAnnouncementIndexModule,
    DeleteAnnouncementModule
  ]
})
export class AnnouncementModule {}
