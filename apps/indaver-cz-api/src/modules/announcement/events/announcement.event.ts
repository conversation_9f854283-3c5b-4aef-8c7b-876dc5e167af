import { DomainEventSubjectType } from '../../domain-events/domain-event-subject-type.enum.js'
import { DomainEvent, DomainEventOptions } from '../../domain-events/domain-event.js'

export class AnnouncementEvent<Content extends object> extends DomainEvent<Content> {
  constructor (
    options: Omit<DomainEventOptions<Content>, 'subjectType' | 'subjectId'> & { announcementUuid: string }
  ) {
    super({
      ...options,
      subjectId: options.announcementUuid,
      subjectType: DomainEventSubjectType.ANNOUNCEMENT
    })
  }
}
