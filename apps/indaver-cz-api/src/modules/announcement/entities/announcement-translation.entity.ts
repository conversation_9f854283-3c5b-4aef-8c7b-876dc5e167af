import { Column, CreateDateColumn, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, type Relation, Unique, UpdateDateColumn } from 'typeorm'
import { Locale } from '../../localization/enums/locale.enum.js'
import { Announcement } from './announcement.entity.js'

@Entity()
@Unique(['announcementUuid', 'language'])
export class AnnouncementTranslation {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'varchar' })
  title: string

  @Column({ type: 'jsonb' })
  content: object

  @Column({ type: 'enum', enum: Locale, enumName: 'Locale' })
  language: Locale

  @Column({ type: 'uuid' })
  @Index()
  announcementUuid: string

  @ManyToOne(() => Announcement, announcement => announcement.translations)
  @JoinColumn({ name: 'announcement_uuid' })
  announcement?: Relation<Announcement>
}
