import { Column, CreateDateColumn, Entity, Index, Join<PERSON>olumn, ManyToOne, OneToMany, PrimaryGeneratedColumn, type Relation, UpdateDateColumn } from 'typeorm'
import { User } from '../../../app/users/entities/user.entity.js'
import { AnnouncementType } from '../enums/announcement-type.enum.js'
import { PublishStatus } from '../../../utils/abstract/publish-status.abstract.js'
import { AnnouncementSalesOrganisation } from '../types/announcement-sales-organisation.type.js'
import { AnnouncementTranslation } from './announcement-translation.entity.js'

@Entity()
export class Announcement extends PublishStatus {
  @PrimaryGeneratedColumn('uuid')
  uuid: string

  @CreateDateColumn({ precision: 3 })
  createdAt: Date

  @UpdateDateColumn({ precision: 3 })
  updatedAt: Date

  @Column({ type: 'enum', enum: AnnouncementType, default: AnnouncementType.INFORMATIONAL })
  type: AnnouncementType

  @Column({ type: 'timestamp' })
  startDate: Date

  @Column({ type: 'timestamp', nullable: true })
  endDate: Date | null

  @Column({ type: 'uuid' })
  @Index()
  createdByUserUuid: string

  @ManyToOne(() => User, user => user.announcements)
  @JoinColumn({ name: 'created_by_user_uuid' })
  createdByUser?: Relation<User>

  @OneToMany(() => AnnouncementTranslation, translation => translation.announcement)
  translations?: Array<Relation<AnnouncementTranslation>>

  @Column({ type: 'jsonb', default: [] })
  @Index('idx_sales_orgs_gin', { synchronize: false })
  salesOrganisations: AnnouncementSalesOrganisation[]
}
