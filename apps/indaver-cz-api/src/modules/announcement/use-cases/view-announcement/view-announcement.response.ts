import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { PublishStatus } from '../../../news/enums/publish-status.enum.js'
import { ViewNewsItemTranslationResponse } from '../../../news/responses/view-news-item-tranlation.response.js'
import { ViewNewsItemAuthorResponse } from '../../../news/responses/view-news-item-author.response.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { ViewAnnouncementAuthorResponse } from '../../responses/view-announcement-author.response.js'
import { ViewAnnouncementTranslationResponse } from '../../responses/view-announcement-translation.response.js'
import { AnnouncementSalesOrganisation } from '../../types/announcement-sales-organisation.type.js'

export class ViewAnnouncementResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, enum: PublishStatus, enumName: 'PublishStatus' })
  publishStatus: PublishStatus

  @ApiProperty({ type: String, enum: AnnouncementType, enumName: 'AnnouncementType' })
  type: AnnouncementType

  @ApiProperty({ type: String, format: 'date-time' })
  startDate: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  endDate: string | null

  @ApiProperty({ type: ViewNewsItemTranslationResponse, isArray: true })
  translations: ViewNewsItemTranslationResponse[]

  @ApiProperty({ type: ViewNewsItemAuthorResponse })
  author: ViewNewsItemAuthorResponse

  @ApiProperty({ type: AnnouncementSalesOrganisation, isArray: true })
  salesOrganisations: AnnouncementSalesOrganisation[]

  constructor (
    announcement: Announcement
  ) {
    assert(announcement.createdByUser !== undefined)
    assert(announcement.translations !== undefined)

    this.uuid = announcement.uuid
    this.createdAt = announcement.createdAt.toISOString()
    this.updatedAt = announcement.updatedAt.toISOString()
    this.publishStatus = announcement.publishStatus
    this.type = announcement.type
    this.startDate = announcement.startDate.toISOString()
    this.endDate = announcement.endDate?.toISOString() ?? null

    this.translations = announcement.translations.map((translation) => {
      return new ViewAnnouncementTranslationResponse(translation)
    })

    this.author = new ViewAnnouncementAuthorResponse(announcement.createdByUser)
    this.salesOrganisations = announcement.salesOrganisations
  }
}
