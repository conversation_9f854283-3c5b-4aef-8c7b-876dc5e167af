import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { ViewAnnouncementResponse } from './view-announcement.response.js'
import { ViewAnnouncementUseCase } from './view-announcement.use-case.js'

@ApiTags('Announcement')
@Controller('announcements/:uuid')
@ApiOAuth2([])
export class ViewAnnouncementController {
  constructor (
    private readonly useCase: ViewAnnouncementUseCase
  ) {}

  @Get()
  @Permissions(Permission.ANNOUNCEMENT_MANAGE)
  @ApiOkResponse({ type: ViewAnnouncementResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  async viewAnnouncement (
    @UuidParam('uuid') announcementUuid: string
  ): Promise<ViewAnnouncementResponse> {
    return await this.useCase.execute(announcementUuid)
  }
}
