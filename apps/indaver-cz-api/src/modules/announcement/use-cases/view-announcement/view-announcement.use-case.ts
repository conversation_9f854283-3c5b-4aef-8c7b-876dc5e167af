import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { ViewAnnouncementResponse } from './view-announcement.response.js'

@Injectable()
export class ViewAnnouncementUseCase {
  constructor (
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>
  ) {}

  async execute (announcementUuid: string): Promise<ViewAnnouncementResponse> {
    const announcement = await this.announcementRepository.findOneOrFail({
      where: {
        uuid: announcementUuid
      },
      relations: {
        createdByUser: true,
        translations: true
      }
    })

    return new ViewAnnouncementResponse(
      announcement
    )
  }
}
