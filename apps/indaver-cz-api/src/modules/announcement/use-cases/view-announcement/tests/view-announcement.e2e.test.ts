import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'

describe('View announcement e2e test', () => {
  let setup: EndToEndTestSetup

  let announcementRepository: Repository<Announcement>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    announcementRepository = setup.dataSource.getRepository(Announcement)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      setup.authContext.getUser([]),
      setup.authContext.getUser([Permission.ANNOUNCEMENT_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/announcements/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .get(`/announcements/${randomUUID()}`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized', async () => {
    const announcement = await announcementRepository.save(
      new AnnouncementEntityBuilder()
        .createdByUser(authorizedUser.user)
        .build()
    )

    const response = await request(setup.httpServer)
      .get(`/announcements/${announcement.uuid}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body).toMatchObject({
      uuid: expect.uuid(),
      createdAt: announcement.createdAt.toISOString(),
      updatedAt: expect.any(String),
      publishStatus: announcement.publishStatus,
      type: announcement.type,
      endDate: announcement.endDate?.toISOString() ?? null,
      startDate: announcement.startDate?.toISOString() ?? null,
      translations: announcement.translations ?? []
    })
  })
})
