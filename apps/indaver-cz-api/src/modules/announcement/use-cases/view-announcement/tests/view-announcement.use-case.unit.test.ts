import { before, describe, it } from 'node:test'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { ViewAnnouncementUseCase } from '../view-announcement.use-case.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'

describe('View announcement use-case unit test', () => {
  let useCase: ViewAnnouncementUseCase

  let announcement: Announcement

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>

  before(() => {
    TestBench.setupUnitTest()

    const user = new UserEntityBuilder().build()

    const announcementTranslation = new AnnouncementTranslationEntityBuilder()
      .build()

    announcement = new AnnouncementEntityBuilder()
      .createdByUser(user)
      .addTranslations(announcementTranslation)
      .build()

    announcementRepository = createStubInstance<Repository<Announcement>>(
      Repository<Announcement>
    )

    announcementRepository.findOneOrFail.resolves(announcement)

    useCase = new ViewAnnouncementUseCase(
      announcementRepository
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(announcement.uuid)

    assert.calledOnce(announcementRepository.findOneOrFail)
  })
})
