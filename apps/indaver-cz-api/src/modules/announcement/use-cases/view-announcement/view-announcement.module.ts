import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { ViewAnnouncementController } from './view-announcement.controller.js'
import { ViewAnnouncementUseCase } from './view-announcement.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ])
  ],
  controllers: [
    ViewAnnouncementController
  ],
  providers: [
    ViewAnnouncementUseCase
  ]
})
export class ViewAnnouncementModule {}
