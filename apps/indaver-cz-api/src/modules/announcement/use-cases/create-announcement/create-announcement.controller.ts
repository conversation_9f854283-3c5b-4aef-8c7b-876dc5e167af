import { <PERSON>, Post, Body } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiCreatedResponse } from '@nestjs/swagger'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { CreateAnnouncementCommand } from './create-announcement.command.js'
import { CreateAnnouncementResponse } from './create-announcement.response.js'
import { CreateAnnouncementUseCase } from './create-announcement.use-case.js'

@ApiTags('Announcement')
@Controller('announcements')
@ApiOAuth2([])
export class CreateAnnouncementController {
  constructor (
    private readonly useCase: CreateAnnouncementUseCase
  ) {}

  @Post()
  @Permissions(Permission.ANNOUNCEMENT_MANAGE)
  @ApiCreatedResponse({ type: CreateAnnouncementResponse })
  async createAnnouncement (
    @Body() command: CreateAnnouncementCommand
  ): Promise<CreateAnnouncementResponse> {
    return await this.useCase.execute(command)
  }
}
