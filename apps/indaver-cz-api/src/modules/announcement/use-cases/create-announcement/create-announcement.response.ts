import { ApiProperty } from '@nestjs/swagger'
import { Announcement } from '../../entities/announcement.entity.js'

export class CreateAnnouncementResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  constructor (announcement: Announcement) {
    this.uuid = announcement.uuid
    this.createdAt = announcement.createdAt.toISOString()
    this.updatedAt = announcement.updatedAt.toISOString()
  }
}
