import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { CreateAnnouncementUseCase } from './create-announcement.use-case.js'
import { CreateAnnouncementController } from './create-announcement.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    CreateAnnouncementController
  ],
  providers: [
    CreateAnnouncementUseCase
  ]
})
export class CreateAnnouncementModule {}
