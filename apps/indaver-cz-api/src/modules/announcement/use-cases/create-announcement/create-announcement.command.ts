import { ApiProperty } from '@nestjs/swagger'
import { IsNullable, IsSameOrAfterDateString, IsUndefinable } from '@wisemen/validators'
import { ArrayMinSize, ArrayUnique, IsArray, IsDateString, IsEnum, IsNotEmpty, ValidateNested } from 'class-validator'
import dayjs from 'dayjs'
import { Type } from 'class-transformer'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { AnnouncementSalesOrganisation } from '../../types/announcement-sales-organisation.type.js'
import { CreateAnnouncementTranslationCommand } from './create-announcement-translation.command.js'

export class CreateAnnouncementCommand {
  @ApiProperty({ type: String, enum: AnnouncementType, enumName: 'AnnouncementType' })
  @IsEnum(AnnouncementType)
  type: AnnouncementType

  @ApiProperty({ type: String, format: 'date' })
  @IsUndefinable()
  @IsDateString()
  startDate: string

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsUndefinable()
  @IsNullable()
  @IsDateString()
  @IsSameOrAfterDateString((dto: CreateAnnouncementCommand) => dayjs(dto.startDate).toISOString())
  endDate?: string | null

  @ApiProperty({ type: CreateAnnouncementTranslationCommand, isArray: true })
  @Type(() => CreateAnnouncementTranslationCommand)
  @ValidateNested({ each: true })
  @IsArray()
  @IsNotEmpty()
  @ArrayMinSize(1)
  translations: CreateAnnouncementTranslationCommand[]

  @ApiProperty({ type: AnnouncementSalesOrganisation, isArray: true, required: false })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  @ValidateNested({ each: true })
  @Type(() => AnnouncementSalesOrganisation)
  salesOrganisations?: AnnouncementSalesOrganisation[]
}
