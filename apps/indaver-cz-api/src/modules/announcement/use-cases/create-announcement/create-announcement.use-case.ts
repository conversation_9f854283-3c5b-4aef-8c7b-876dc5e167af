import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { AnnouncementCreatedEvent } from './announcement-created.event.js'
import { CreateAnnouncementCommand } from './create-announcement.command.js'
import { CreateAnnouncementResponse } from './create-announcement.response.js'

@Injectable()
export class CreateAnnouncementUseCase {
  constructor (
    private readonly dataSource: DataSource,
    private readonly authContext: AuthContext,
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>,
    @InjectRepository(AnnouncementTranslation)
    private readonly announcementTranslationRepository: Repository<AnnouncementTranslation>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (command: CreateAnnouncementCommand): Promise<CreateAnnouncementResponse> {
    const userUuid = this.authContext.getUserUuidOrFail()

    const announcement = this.announcementRepository.create({
      createdByUserUuid: userUuid,
      type: command.type,
      startDate: command.startDate,
      endDate: command.endDate,
      salesOrganisations: command.salesOrganisations
    })

    await transaction(this.dataSource, async () => {
      await this.announcementRepository.save(announcement)

      const announcementTranslations = command.translations.map((translation) => {
        return this.announcementTranslationRepository.create({
          announcementUuid: announcement.uuid,
          language: translation.language,
          title: translation.title,
          content: translation.content
        })
      })

      await this.announcementTranslationRepository.save(announcementTranslations)

      await this.eventEmitter.emit([new AnnouncementCreatedEvent(announcement)])
    })

    return new CreateAnnouncementResponse(announcement)
  }
}
