import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementEvent } from '../../events/announcement.event.js'

export class AnnouncementCreatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly announcementUuid: string

  constructor (announcementUuid: string) {
    this.announcementUuid = announcementUuid
  }
}

@RegisterDomainEvent(DomainEventType.ANNOUNCEMENT_CREATED, 1)
export class AnnouncementCreatedEvent
  extends AnnouncementEvent<AnnouncementCreatedEventContent> {
  constructor (announcement: Announcement) {
    super({
      announcementUuid: announcement.uuid,
      content: new AnnouncementCreatedEventContent(announcement.uuid)
    })
  }
}
