import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { createStubInstance, SinonStubbedInstance, assert } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import dayjs from 'dayjs'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { CreateAnnouncementUseCase } from '../create-announcement.use-case.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementCreatedEvent } from '../announcement-created.event.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementTranslation } from '../../../entities/announcement-translation.entity.js'
import { AuthContext } from '../../../../auth/auth.context.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { CreateAnnouncementCommandBuilder } from './create-announcement-command.builder.js'

describe('Create announcement use-case unit test', () => {
  let useCase: CreateAnnouncementUseCase

  let userUuid: string

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>
  let announcementTranslationRepository: SinonStubbedInstance<Repository<AnnouncementTranslation>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    userUuid = randomUUID()

    const authContext = createStubInstance(AuthContext, {
      getUserUuidOrFail: userUuid
    })

    announcementRepository = createStubInstance<Repository<Announcement>>(
      Repository<Announcement>, {
        create: new AnnouncementEntityBuilder()
          .createdByUserUuid(userUuid)
          .build()
      }
    )
    announcementTranslationRepository = createStubInstance<Repository<AnnouncementTranslation>>(
      Repository<AnnouncementTranslation>
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new CreateAnnouncementUseCase(
      stubDataSource(),
      authContext,
      announcementRepository,
      announcementTranslationRepository,
      eventEmitter
    )
  })

  it('Calls create', async () => {
    const command = new CreateAnnouncementCommandBuilder().build()

    await useCase.execute(command)

    assert.calledOnce(announcementRepository.create)
    assert.calledOnce(announcementTranslationRepository.create)
  })

  it('Emits a AnnouncementCreated event', async () => {
    const command = new CreateAnnouncementCommandBuilder().build()

    const result = await useCase.execute(command)

    const expectedAnnouncement = new AnnouncementEntityBuilder()
      .withUuid(result.uuid)
      .withType(command.type)
      .withStartDate(dayjs(command.startDate).toDate())
      .withEndDate(dayjs(command.endDate).toDate())
      .build()

    expect(eventEmitter).toHaveEmitted(new AnnouncementCreatedEvent(expectedAnnouncement))
  })
})
