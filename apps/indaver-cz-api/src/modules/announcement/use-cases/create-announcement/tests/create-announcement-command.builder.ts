import { randWord } from '@ngneat/falso'
import { CreateAnnouncementCommand } from '../create-announcement.command.js'
import { AnnouncementType } from '../../../enums/announcement-type.enum.js'
import { Locale } from '../../../../localization/enums/locale.enum.js'

export class CreateAnnouncementCommandBuilder {
  private command: CreateAnnouncementCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new CreateAnnouncementCommand()

    this.command.type = AnnouncementType.INFORMATIONAL
    this.command.startDate = new Date().toISOString()
    this.command.endDate = null
    this.command.translations = [{
      language: Locale.EN_GB,
      title: randWord(),
      content: {}
    }]

    return this
  }

  build (): CreateAnnouncementCommand {
    return this.command
  }
}
