import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { CreateAnnouncementCommandBuilder } from './create-announcement-command.builder.js'

describe('Create announcement e2e test', () => {
  let setup: EndToEndTestSetup

  before(async () => {
    setup = await TestBench.setupEndToEndTest()
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .post(`/announcements`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const user = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .post(`/announcements`)
      .set('Authorization', `Bearer ${user.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 201 when authorized', async () => {
    const user = await setup.authContext.getUser([Permission.ANNOUNCEMENT_MANAGE])

    const command = new CreateAnnouncementCommandBuilder().build()

    const response = await request(setup.httpServer)
      .post(`/announcements`)
      .set('Authorization', `Bearer ${user.token}`)
      .send(command)

    expect(response).toHaveStatus(201)
    expect(response.body).toMatchObject({
      uuid: expect.uuid(),
      createdAt: expect.any(String),
      updatedAt: expect.any(String)
    })
  })
})
