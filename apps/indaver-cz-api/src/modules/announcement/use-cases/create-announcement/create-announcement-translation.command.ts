import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator'
import { Locale } from '../../../localization/enums/locale.enum.js'

export class CreateAnnouncementTranslationCommand {
  @ApiProperty({ type: String })
  @IsString()
  @IsNotEmpty()
  title: string

  @ApiProperty({ type: Object })
  @IsObject()
  @IsNotEmpty()
  content: object

  @ApiProperty({ type: String, enum: Locale, enumName: 'Locale' })
  @IsEnum(Locale)
  language: Locale
}
