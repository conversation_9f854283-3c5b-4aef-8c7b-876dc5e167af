import { Controller, Get, Query } from '@nestjs/common'
import { ApiT<PERSON>s, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewDashboardAnnouncementIndexQuery } from './view-dashboard-announcement-index.query.js'
import { ViewDashboardAnnouncementIndexResponse } from './view-dashboard-announcement-index.response.js'
import { ViewDashboardAnnouncementIndexUseCase } from './view-dashboard-announcement-index.use-case.js'

@ApiTags('Dashboard Announcement')
@ApiOAuth2([])
@Controller('dashboard-announcements')
export class ViewDashboardAnnouncementIndexController {
  constructor (
    private readonly authContext: AuthContext,
    private readonly useCase: ViewDashboardAnnouncementIndexUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @ApiOkResponse({ type: ViewDashboardAnnouncementIndexResponse })
  public async viewDashboardAnnouncementIndex (
    @Query() query: ViewDashboardAnnouncementIndexQuery
  ): Promise<ViewDashboardAnnouncementIndexResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    return await this.useCase.execute(selectedCustomerId, query)
  }
}
