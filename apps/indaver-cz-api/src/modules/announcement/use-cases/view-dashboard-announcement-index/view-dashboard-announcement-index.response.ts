import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination'
import { Announcement } from '../../entities/announcement.entity.js'
import { ViewAnnouncementTranslationResponse } from '../../responses/view-announcement-translation.response.js'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'

class DashboardAnnouncementIndexView {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, enum: AnnouncementType, enumName: 'AnnouncementType' })
  type: AnnouncementType

  @ApiProperty({ type: String, format: 'date-time' })
  startDate: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  endDate: string | null

  @ApiProperty({ type: ViewAnnouncementTranslationResponse })
  translation: ViewAnnouncementTranslationResponse

  constructor (
    announcement: Announcement
  ) {
    assert(announcement.createdByUser !== undefined)
    assert(announcement.translations !== undefined)

    this.uuid = announcement.uuid
    this.startDate = announcement.startDate.toISOString()
    this.endDate = announcement.endDate?.toISOString() ?? null
    this.type = announcement.type

    const translation = announcement.translations.find(
      translation => translation.language === getCurrentLanguage()
    )

    assert(translation !== undefined)

    this.translation = new ViewAnnouncementTranslationResponse(translation)
  }
}

export class ViewDashboardAnnouncementIndexResponse
  extends PaginatedOffsetResponse<DashboardAnnouncementIndexView> {
  @ApiProperty({ type: DashboardAnnouncementIndexView, isArray: true })
  declare items: DashboardAnnouncementIndexView[]

  constructor (items: Announcement[], total: number, limit: number, offset: number) {
    const result = items.map(announcement => new DashboardAnnouncementIndexView(announcement))

    super(result, total, limit, offset)
  }
}
