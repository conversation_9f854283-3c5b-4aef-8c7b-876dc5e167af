import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { Repository } from 'typeorm'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { ViewDashboardAnnouncementIndexUseCase } from '../view-dashboard-announcement-index.use-case.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewDashboardAnnouncementIndexQueryBuilder } from './view-dashboard-announcement-index-query.builder.js'

describe('View dashboard announcement index use-case unit test', () => {
  let useCase: ViewDashboardAnnouncementIndexUseCase

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>
  let salesOrgUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>

  before(() => {
    TestBench.setupUnitTest()

    const user = new UserEntityBuilder().build()

    const announcement = new AnnouncementEntityBuilder()
      .createdByUser(user)
      .addTranslations(new AnnouncementTranslationEntityBuilder().build())
      .build()

    announcementRepository = createStubInstance<Repository<Announcement>>(
        Repository<Announcement>
    )

    announcementRepository.findAndCount.resolves([[announcement], 1])
    salesOrgUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)

    useCase = new ViewDashboardAnnouncementIndexUseCase(
      announcementRepository,
      salesOrgUseCase
    )
  })

  it('Calls all methods once', async () => {
    const query = new ViewDashboardAnnouncementIndexQueryBuilder().build()
    const selectedCustomerId = randomUUID()

    await useCase.execute(selectedCustomerId, query)

    assert.calledOnce(announcementRepository.findAndCount)
    assert.calledOnce(salesOrgUseCase.getOrganisationIdOrFail)
  })
})
