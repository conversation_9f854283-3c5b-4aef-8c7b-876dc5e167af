import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import dayjs from 'dayjs'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../../entities/announcement-translation.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'
import { ViewDashboardAnnouncementIndexQueryBuilder } from './view-dashboard-announcement-index-query.builder.js'

describe('View dashboard announcement index e2e test', () => {
  let setup: EndToEndTestSetup

  let announcementRepository: Repository<Announcement>
  let announcementTranslationRepository: Repository<AnnouncementTranslation>

  let user: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    announcementRepository = setup.dataSource.getRepository(Announcement)
    announcementTranslationRepository = setup.dataSource.getRepository(AnnouncementTranslation)

    user = await setup.authContext.getUser([Permission.ANNOUNCEMENT_MANAGE])
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/dashboard-announcements`)

    expect(response).toHaveStatus(401)
  })

  it('returns announcements when published', async () => {
    const publishedAnnouncement = new AnnouncementEntityBuilder()
      .createdByUser(user.user)
      .withStartDate(dayjs().subtract(1, 'day').toDate())
      .build()
    const publishedAnnouncementTranslation = new AnnouncementTranslationEntityBuilder()
      .forAnnouncement(publishedAnnouncement.uuid)
      .build()

    const unpublishedAnnouncements = new AnnouncementEntityBuilder()
      .createdByUser(user.user)
      .withStartDate(dayjs().add(1, 'day').toDate())
      .build()
    const unpublishedAnnouncementTranslation = new AnnouncementTranslationEntityBuilder()
      .forAnnouncement(unpublishedAnnouncements.uuid)
      .build()

    await announcementRepository.save([
      publishedAnnouncement,
      unpublishedAnnouncements
    ])
    await announcementTranslationRepository.save([
      publishedAnnouncementTranslation,
      unpublishedAnnouncementTranslation
    ])

    const query = new ViewDashboardAnnouncementIndexQueryBuilder().build()

    const response = await request(setup.httpServer)
      .get(`/dashboard-announcements`)
      .set('Authorization', `Bearer ${user.token}`)
      .query(
        query
      )

    expect(response).toHaveStatus(200)
    expect(response.body.items.length).toBe(1)
    expect(response.body.meta.total).toBe(1)
    expect(response.body.meta.limit).toBe(query.pagination?.limit ?? 100)
    expect(response.body.meta.offset).toBe(query.pagination?.offset ?? 0)
  })
})
