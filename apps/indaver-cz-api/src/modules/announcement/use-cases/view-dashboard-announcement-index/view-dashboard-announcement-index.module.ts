import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewDashboardAnnouncementIndexController } from './view-dashboard-announcement-index.controller.js'
import { ViewDashboardAnnouncementIndexUseCase } from './view-dashboard-announcement-index.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    CustomerModule
  ],
  controllers: [
    ViewDashboardAnnouncementIndexController
  ],
  providers: [
    ViewDashboardAnnouncementIndexUseCase
  ]
})
export class ViewDashboardAnnouncementIndexModule {}
