import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { FindOptionsW<PERSON>, <PERSON><PERSON><PERSON>, Less<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>r<PERSON>qua<PERSON>, Raw, Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination'
import { Announcement } from '../../entities/announcement.entity.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewDashboardAnnouncementIndexQuery } from './view-dashboard-announcement-index.query.js'
import { ViewDashboardAnnouncementIndexResponse } from './view-dashboard-announcement-index.response.js'

@Injectable()
export class ViewDashboardAnnouncementIndexUseCase {
  constructor (
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>,
    private readonly salesOrganisationUseCase: GetCustomerDefaultSalesOrganisationIdUseCase
  ) {}

  async execute (
    selectedCustomerId: string | null,
    query: ViewDashboardAnnouncementIndexQuery
  ): Promise<ViewDashboardAnnouncementIndexResponse> {
    const language = getCurrentLanguage()
    const pagination = typeormPagination(query.pagination)
    const today = new Date()
    const salesOrganisationId = await this.getSaleOrganisationId(selectedCustomerId)

    const whereCondition: FindOptionsWhere<Announcement> = {
      startDate: LessThanOrEqual(today),
      translations: {
        language
      },
      salesOrganisations: salesOrganisationId !== undefined
        ? Raw(organisation => `${organisation} @> :id`,
            { id: JSON.stringify([{ id: salesOrganisationId }]) }
          )
        : undefined
    }

    const [announcements, count] = await this.announcementRepository.findAndCount({
      where: [
        {
          ...whereCondition,
          endDate: MoreThanOrEqual(today)
        },
        {
          ...whereCondition,
          endDate: IsNull()
        }
      ],
      order: {
        type: 'DESC'
      },
      relations: {
        translations: true,
        createdByUser: true
      },
      skip: pagination.skip,
      take: pagination.take
    })

    return new ViewDashboardAnnouncementIndexResponse(
      announcements,
      count,
      pagination.take,
      pagination.skip
    )
  }

  private async getSaleOrganisationId (
    selectedCustomerId: string | null
  ): Promise<string | undefined> {
    if (selectedCustomerId === null) {
      return undefined
    }

    return this.salesOrganisationUseCase.getOrganisationIdOrFail(selectedCustomerId)
  }
}
