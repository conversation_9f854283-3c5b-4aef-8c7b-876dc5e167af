import { before, describe, it } from 'node:test'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../../entities/announcement-translation.entity.js'
import { DeleteAnnouncementUseCase } from '../delete-announcement.use-case.js'
import { AnnouncementDeletedEvent } from '../announcement-deleted.event.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'

describe('Delete announcement use-case unit test', () => {
  let useCase: DeleteAnnouncementUseCase

  let announcement: Announcement

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>
  let announcementTranslationRepository: SinonStubbedInstance<Repository<AnnouncementTranslation>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    const user = new UserEntityBuilder().build()

    const translation = new AnnouncementTranslationEntityBuilder().build()

    announcement = new AnnouncementEntityBuilder()
      .createdByUserUuid(user.uuid)
      .addTranslations(translation)
      .build()

    eventEmitter = createStubInstance(DomainEventEmitter)
    announcementRepository = createStubInstance<Repository<Announcement>>(
      Repository<Announcement>
    )
    announcementTranslationRepository = createStubInstance<Repository<AnnouncementTranslation>>(
      Repository<AnnouncementTranslation>
    )

    announcementRepository.findOneOrFail.resolves(announcement)
    announcementRepository.delete.resolves()
    announcementTranslationRepository.delete.resolves()

    useCase = new DeleteAnnouncementUseCase(
      stubDataSource(),
      announcementRepository,
      announcementTranslationRepository,
      eventEmitter
    )
  })

  it('Calls all methods once', async () => {
    await useCase.execute(announcement.uuid)

    assert.calledOnce(announcementRepository.findOneOrFail)
    assert.calledOnce(announcementRepository.delete)
    assert.calledOnce(announcementTranslationRepository.delete)
  })

  it('Emits a AnnouncementDeletedEvent event', async () => {
    await useCase.execute(announcement.uuid)

    expect(eventEmitter).toHaveEmitted(new AnnouncementDeletedEvent(announcement.uuid))
  })
})
