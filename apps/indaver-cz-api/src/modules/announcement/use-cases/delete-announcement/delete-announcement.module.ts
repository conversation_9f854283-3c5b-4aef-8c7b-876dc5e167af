import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { DeleteAnnouncementController } from './delete-announcement.controller.js'
import { DeleteAnnouncementUseCase } from './delete-announcement.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    DeleteAnnouncementController
  ],
  providers: [
    DeleteAnnouncementUseCase
  ]
})
export class DeleteAnnouncementModule {}
