import assert from 'assert'
import { Injectable } from '@nestjs/common'
import { DataSource, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { AnnouncementDeletedEvent } from './announcement-deleted.event.js'

@Injectable()
export class DeleteAnnouncementUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>,
    @InjectRepository(AnnouncementTranslation)
    private readonly announcementTranslationRepository: Repository<AnnouncementTranslation>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    announcementUuid: string
  ): Promise<void> {
    const announcement = await this.announcementRepository.findOneOrFail({
      where: {
        uuid: announcementUuid
      },
      relations: {
        translations: true
      }
    })

    assert(announcement.translations !== undefined)

    const announcementTranslationUuids: string[] = announcement.translations.map((translation) => {
      return translation.uuid
    })

    await transaction(this.dataSource, async () => {
      if (announcementTranslationUuids.length > 0) {
        await this.announcementTranslationRepository.delete(announcementTranslationUuids)
      }

      await this.announcementRepository.delete(announcementUuid)
      await this.eventEmitter.emit([new AnnouncementDeletedEvent(announcementUuid)])
    })
  }
}
