import { ApiProperty } from '@nestjs/swagger'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { AnnouncementEvent } from '../../events/announcement.event.js'

export class AnnouncementDeletedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly announcementUuid: string

  constructor (announcementUuid: string) {
    this.announcementUuid = announcementUuid
  }
}

@RegisterDomainEvent(DomainEventType.ANNOUNCEMENT_DELETED, 1)
export class AnnouncementDeletedEvent
  extends AnnouncementEvent<AnnouncementDeletedEventContent> {
  constructor (announcementUuid: string) {
    super({
      announcementUuid,
      content: new AnnouncementDeletedEventContent(announcementUuid)
    })
  }
}
