import { Controller, Delete } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { DeleteAnnouncementUseCase } from './delete-announcement.use-case.js'

@ApiTags('Announcement')
@Controller('announcements/:uuid')
@ApiOAuth2([])
export class DeleteAnnouncementController {
  constructor (
    private readonly useCase: DeleteAnnouncementUseCase
  ) {}

  @Delete()
  @Permissions(Permission.ANNOUNCEMENT_MANAGE)
  @ApiOkResponse()
  @ApiNotFoundErrorResponse(NotFoundError)
  async deleteAnnouncement (
    @UuidParam('uuid') announcementUuid: string
  ): Promise<void> {
    await this.useCase.execute(announcementUuid)
  }
}
