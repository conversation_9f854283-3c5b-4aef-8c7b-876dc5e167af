import { ApiProperty } from '@nestjs/swagger'
import { IsNullable, IsSameOrAfterDateString, IsUndefinable } from '@wisemen/validators'
import { ValidateNested, IsArray, IsDateString, IsEnum, ArrayUnique } from 'class-validator'
import { Type } from 'class-transformer'
import dayjs from 'dayjs'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { AnnouncementSalesOrganisation } from '../../types/announcement-sales-organisation.type.js'
import { UpdateAnnouncementTranslationCommand } from './update-announcement-translation.command.js'

export class UpdateAnnouncementCommand {
  @ApiProperty({ type: String, required: false, enum: AnnouncementType, enumName: 'AnnouncementType' })
  @IsUndefinable()
  @IsEnum(AnnouncementType)
  type?: AnnouncementType

  @ApiProperty({ type: String, required: false, format: 'date' })
  @IsUndefinable()
  @IsDateString()
  startDate?: string

  @ApiProperty({ type: String, required: false, nullable: true, format: 'date' })
  @IsUndefinable()
  @IsNullable()
  @IsDateString()
  @IsSameOrAfterDateString((dto: UpdateAnnouncementCommand) => dayjs(dto.startDate).toISOString())
  endDate?: string | null

  @ApiProperty({ type: UpdateAnnouncementTranslationCommand, required: false, isArray: true })
  @IsUndefinable()
  @ValidateNested({ each: true })
  @Type(() => UpdateAnnouncementTranslationCommand)
  @IsArray()
  translations?: UpdateAnnouncementTranslationCommand[]

  @ApiProperty({ type: AnnouncementSalesOrganisation, isArray: true, required: false })
  @IsUndefinable()
  @IsArray()
  @ArrayUnique()
  @ValidateNested({ each: true })
  @Type(() => AnnouncementSalesOrganisation)
  salesOrganisations?: AnnouncementSalesOrganisation[]
}
