import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator'
import { Locale } from '../../../localization/enums/locale.enum.js'

export class UpdateAnnouncementTranslationCommand {
  @ApiProperty({ type: String, required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  title?: string

  @ApiProperty({ type: Object, required: false })
  @IsOptional()
  @IsObject()
  @IsNotEmpty()
  content?: object

  @ApiProperty({ type: String, required: true, enum: Locale, enumName: 'Locale' })
  @IsEnum(Locale)
  language: Locale
}
