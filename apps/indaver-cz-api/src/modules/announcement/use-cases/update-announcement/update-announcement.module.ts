import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { DomainEventEmitterModule } from '../../../domain-events/domain-event-emitter.module.js'
import { UpdateAnnouncementUseCase } from './update-announcement.use-case.js'
import { UpdateAnnouncementController } from './update-announcement.controller.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    DomainEventEmitterModule
  ],
  controllers: [
    UpdateAnnouncementController
  ],
  providers: [
    UpdateAnnouncementUseCase
  ]
})
export class UpdateAnnouncementModule {}
