import { Injectable } from '@nestjs/common'
import { DataSource, In, Repository } from 'typeorm'
import { InjectRepository, transaction } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { DomainEventEmitter } from '../../../domain-events/domain-event-emitter.js'
import { AnnouncementUpdatedEvent } from './announcement-updated.event.js'
import { UpdateAnnouncementCommand } from './update-announcement.command.js'
import { UpdateAnnouncementResponse } from './update-announcement.response.js'
import { UpdateAnnouncementTranslationCommand } from './update-announcement-translation.command.js'

@Injectable()
export class UpdateAnnouncementUseCase {
  constructor (
    private readonly dataSource: DataSource,
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>,
    @InjectRepository(AnnouncementTranslation)
    private readonly announcementTranslationRepository: Repository<AnnouncementTranslation>,
    private readonly eventEmitter: DomainEventEmitter
  ) {}

  async execute (
    announcementUuid: string,
    command: UpdateAnnouncementCommand
  ): Promise<UpdateAnnouncementResponse> {
    await transaction(this.dataSource, async () => {
      const updateResult = await this.announcementRepository.update({
        uuid: announcementUuid
      }, {
        type: command.type,
        startDate: command.startDate,
        endDate: command.endDate,
        salesOrganisations: command.salesOrganisations
      })

      if (updateResult.affected === 0) {
        throw new NotFoundError('error.announcement.announcement_not_found')
      }

      if (command.translations !== undefined) {
        await this.syncTranslations(
          announcementUuid,
          command.translations
        )
      }

      await this.eventEmitter.emit([new AnnouncementUpdatedEvent(announcementUuid)])
    })

    const updatedAnnouncement = await this.announcementRepository.findOneOrFail({
      select: {
        uuid: true,
        createdAt: true,
        updatedAt: true
      },
      where: {
        uuid: announcementUuid
      }
    })

    return new UpdateAnnouncementResponse(updatedAnnouncement)
  }

  private async syncTranslations (
    announcementUuid: string,
    translations: UpdateAnnouncementTranslationCommand[]
  ): Promise<void> {
    const existingTranslations = await this.announcementTranslationRepository.findBy({
      announcementUuid
    })

    await this.deleteTranslations(
      existingTranslations,
      translations
    )

    await this.upsertTranslations(
      announcementUuid,
      translations
    )
  }

  private async deleteTranslations (
    existingTranslations: AnnouncementTranslation[],
    commandTranslations: UpdateAnnouncementTranslationCommand[]
  ): Promise<void> {
    const deletedTranslations = existingTranslations.filter(
      existingTranslation => !commandTranslations.some(
        translation => translation.language === existingTranslation.language
      )
    )

    await this.announcementTranslationRepository.delete({
      uuid: In(deletedTranslations.map(translation => translation.uuid))
    })
  }

  private async upsertTranslations (
    announcementUuid: string,
    commandTranslations: UpdateAnnouncementTranslationCommand[]
  ): Promise<void> {
    const translations = commandTranslations.map((translation) => {
      return {
        language: translation.language,
        content: translation.content,
        title: translation.title,
        announcementUuid
      }
    })

    await this.announcementTranslationRepository.upsert(
      translations,
      ['language', 'announcementUuid']
    )
  }
}
