import { <PERSON>, <PERSON>, Patch } from '@nestjs/common'
import { ApiT<PERSON><PERSON>, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { Permissions } from '../../../permission/permission.decorator.js'
import { Permission } from '../../../permission/permission.enum.js'
import { ApiNotFoundErrorResponse, ApiBadRequestErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { MissingRequiredFieldError } from '../../../exceptions/generic/missing-required-field.error.js'
import { DateMustBeAfterError } from '../../../exceptions/generic/date-must-be-after.js'
import { FieldMustBeNullError } from '../../../exceptions/generic/field-must-be-null.error.js'
import { UpdateAnnouncementCommand } from './update-announcement.command.js'
import { UpdateAnnouncementResponse } from './update-announcement.response.js'
import { UpdateAnnouncementUseCase } from './update-announcement.use-case.js'

@ApiTags('Announcement')
@Controller('announcements/:uuid')
@ApiOAuth2([])
export class UpdateAnnouncementController {
  constructor (
    private readonly useCase: UpdateAnnouncementUseCase
  ) {}

  @Patch()
  @Permissions(Permission.ANNOUNCEMENT_MANAGE)
  @ApiOkResponse({ type: UpdateAnnouncementResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  @ApiBadRequestErrorResponse(
    MissingRequiredFieldError,
    DateMustBeAfterError,
    FieldMustBeNullError
  )
  async updateAnnouncement (
    @UuidParam('uuid') announcementUuid: string,
    @Body() command: UpdateAnnouncementCommand
  ): Promise<UpdateAnnouncementResponse> {
    return await this.useCase.execute(announcementUuid, command)
  }
}
