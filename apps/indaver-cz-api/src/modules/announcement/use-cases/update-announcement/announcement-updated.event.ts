import { ApiProperty } from '@nestjs/swagger'
import { RegisterDomainEvent } from '../../../domain-events/register-domain-event.decorator.js'
import { DomainEventType } from '../../../domain-events/domain-event-type.js'
import { AnnouncementEvent } from '../../events/announcement.event.js'

export class AnnouncementUpdatedEventContent {
  @ApiProperty({ format: 'uuid' })
  readonly announcementUuid: string

  constructor (announcementUuid: string) {
    this.announcementUuid = announcementUuid
  }
}

@RegisterDomainEvent(DomainEventType.ANNOUNCEMENT_UPDATED, 1)
export class AnnouncementUpdatedEvent
  extends AnnouncementEvent<AnnouncementUpdatedEventContent> {
  constructor (announcementUuid: string) {
    super({
      announcementUuid,
      content: new AnnouncementUpdatedEventContent(announcementUuid)
    })
  }
}
