import { randWord } from '@ngneat/falso'
import { UpdateAnnouncementTranslationCommand } from '../update-announcement-translation.command.js'
import { Locale } from '../../../../localization/enums/locale.enum.js'

export class UpdateAnnouncementTranslationCommandBuilder {
  private command: UpdateAnnouncementTranslationCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdateAnnouncementTranslationCommand()
    this.command.language = Locale.EN_GB
    this.command.title = randWord()
    this.command.content = {}

    return this
  }

  withTranslationLanguage (language: Locale): this {
    this.command.language = language

    return this
  }

  withTitle (title: string): this {
    this.command.title = title

    return this
  }

  withContent (content: object): this {
    this.command.content = content

    return this
  }

  build (): UpdateAnnouncementTranslationCommand {
    const result = this.command

    this.reset()

    return result
  }
}
