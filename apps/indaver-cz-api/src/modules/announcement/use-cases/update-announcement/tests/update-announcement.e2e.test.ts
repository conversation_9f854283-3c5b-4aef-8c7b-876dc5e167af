import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'

describe('Update announcement e2e test', () => {
  let setup: EndToEndTestSetup

  let announcementRepository: Repository<Announcement>

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    announcementRepository = setup.dataSource.getRepository(Announcement)
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .patch(`/announcements/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const unauthorizedUser = await setup.authContext.getUser([])

    const response = await request(setup.httpServer)
      .patch(`/announcements/${randomUUID()}`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns 200 when authorized', async () => {
    const authorizedUser = await setup.authContext.getUser([Permission.ANNOUNCEMENT_MANAGE])

    const announcement = await announcementRepository.save(
      new AnnouncementEntityBuilder()
        .createdByUserUuid(authorizedUser.user.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .patch(`/announcements/${announcement.uuid}`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)

    expect(response).toHaveStatus(200)
    expect(response.body).toMatchObject({
      uuid: expect.uuid(),
      createdAt: announcement.createdAt.toISOString(),
      updatedAt: expect.any(String)
    })
  })
})
