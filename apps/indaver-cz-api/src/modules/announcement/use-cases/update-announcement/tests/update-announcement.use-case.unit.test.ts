import { before, afterEach, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import Sinon, { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { expect } from 'expect'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { stubDataSource } from '../../../../../../test/utils/stub-datasource.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../../entities/announcement-translation.entity.js'
import { UpdateAnnouncementUseCase } from '../update-announcement.use-case.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementUpdatedEvent } from '../announcement-updated.event.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'
import { NotFoundError } from '../../../../exceptions/generic/not-found.error.js'
import { Locale } from '../../../../localization/enums/locale.enum.js'
import { DomainEventEmitter } from '../../../../domain-events/domain-event-emitter.js'
import { UpdateAnnouncementCommandBuilder } from './update-announcement-command.builder.js'
import { UpdateAnnouncementTranslationCommandBuilder } from './update-announcement-translation.command.builder.js'

describe('Update announcement use-case unit test', () => {
  let useCase: UpdateAnnouncementUseCase

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>
  let announcementTranslationRepository: SinonStubbedInstance<Repository<AnnouncementTranslation>>
  let eventEmitter: SinonStubbedInstance<DomainEventEmitter>

  before(() => {
    TestBench.setupUnitTest()

    announcementRepository = createStubInstance<Repository<Announcement>>(Repository<Announcement>)

    announcementTranslationRepository = createStubInstance<Repository<AnnouncementTranslation>>(
      Repository<AnnouncementTranslation>
    )

    eventEmitter = createStubInstance(DomainEventEmitter)

    useCase = new UpdateAnnouncementUseCase(
      stubDataSource(),
      announcementRepository,
      announcementTranslationRepository,
      eventEmitter
    )

    mockMethods()
  })

  afterEach(() => {
    Sinon.resetHistory()
    mockMethods()
  })

  function mockMethods () {
    announcementRepository.update.resolves({
      raw: {},
      affected: 1,
      generatedMaps: []
    })
    announcementRepository.findOneOrFail.resolves(new AnnouncementEntityBuilder().build())
  }

  it ('Returns error when announcement not found', async () => {
    announcementRepository.update.resolves({
      raw: {},
      affected: 0,
      generatedMaps: []
    })

    const announcementUuid = randomUUID()
    const command = new UpdateAnnouncementCommandBuilder().build()

    await expect(useCase.execute(announcementUuid, command)).rejects.toThrow(NotFoundError)
  })

  it('Calls update', async () => {
    const announcementUuid = randomUUID()
    const command = new UpdateAnnouncementCommandBuilder().build()

    announcementTranslationRepository.findBy.resolves([])

    await useCase.execute(announcementUuid, command)

    assert.calledOnce(announcementRepository.update)
    assert.calledOnce(announcementRepository.findOneOrFail)
  })

  it('Emits a AnnouncementUpdated event', async () => {
    const announcementUuid = randomUUID()
    const command = new UpdateAnnouncementCommandBuilder().build()

    await useCase.execute(announcementUuid, command)

    expect(eventEmitter).toHaveEmitted(new AnnouncementUpdatedEvent(announcementUuid))
  })

  it('Call create when translation doesn\'t exist', async () => {
    announcementTranslationRepository.findBy.resolves([])

    const announcementUuid = randomUUID()

    const command = new UpdateAnnouncementCommandBuilder()
      .addTranslation(new UpdateAnnouncementTranslationCommandBuilder().build())
      .build()

    await useCase.execute(announcementUuid, command)

    assert.calledOnce(announcementTranslationRepository.upsert)
  })

  it('Call update when translation exists', async () => {
    const language = Locale.EN_GB

    announcementTranslationRepository.findBy.resolves([
      new AnnouncementTranslationEntityBuilder()
        .withLanguage(language)
        .build()
    ])

    const command = new UpdateAnnouncementCommandBuilder()
      .addTranslation(
        new UpdateAnnouncementTranslationCommandBuilder()
          .withTranslationLanguage(language)
          .build()
      )
      .build()

    await useCase.execute(randomUUID(), command)

    assert.calledOnce(announcementTranslationRepository.upsert)
  })

  it('Call delete when translation doesn\'t exist in command', async () => {
    const language = Locale.EN_GB

    announcementTranslationRepository.findBy.resolves([
      new AnnouncementTranslationEntityBuilder()
        .withLanguage(language)
        .build()
    ])

    const command = new UpdateAnnouncementCommandBuilder().build()

    await useCase.execute(randomUUID(), command)

    assert.calledOnce(announcementTranslationRepository.delete)
  })
})
