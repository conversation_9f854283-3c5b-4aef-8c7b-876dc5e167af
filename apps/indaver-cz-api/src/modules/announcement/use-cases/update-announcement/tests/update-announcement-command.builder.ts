import { AnnouncementType } from '../../../enums/announcement-type.enum.js'
import { UpdateAnnouncementTranslationCommand } from '../update-announcement-translation.command.js'
import { UpdateAnnouncementCommand } from '../update-announcement.command.js'

export class UpdateAnnouncementCommandBuilder {
  private command: UpdateAnnouncementCommand

  constructor () {
    this.reset()
  }

  reset (): this {
    this.command = new UpdateAnnouncementCommand()

    this.command.translations = []

    return this
  }

  withType (type: AnnouncementType): this {
    this.command.type = type

    return this
  }

  withStartDate (startDate: Date): this {
    this.command.startDate = startDate.toDateString()

    return this
  }

  withEndDate (endDate: Date): this {
    this.command.endDate = endDate.toDateString()

    return this
  }

  addTranslation (translation: UpdateAnnouncementTranslationCommand): this {
    this.command.translations?.push(translation)

    return this
  }

  build (): UpdateAnnouncementCommand {
    const result = this.command

    this.reset()

    return result
  }
}
