import { Controller, Get, Query } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { Permission } from '../../../permission/permission.enum.js'
import { Permissions } from '../../../permission/permission.decorator.js'
import { ViewAnnouncementIndexUseCase } from './view-announcement-index.use-case.js'
import { ViewAnnouncementIndexResponse } from './view-announcement-index.response.js'
import { ViewAnnouncementIndexQuery } from './view-announcement-index.query.js'

@ApiTags('Announcement')
@ApiOAuth2([])
@Controller('announcements')
export class ViewAnnouncementIndexController {
  constructor (
    private readonly useCase: ViewAnnouncementIndexUseCase
  ) { }

  @Get()
  @Permissions(Permission.ANNOUNCEMENT_MANAGE)
  @ApiOkResponse({ type: ViewAnnouncementIndexResponse })
  public async viewAnnouncementIndex (
    @Query() query: ViewAnnouncementIndexQuery
  ): Promise<ViewAnnouncementIndexResponse> {
    return await this.useCase.execute(query)
  }
}
