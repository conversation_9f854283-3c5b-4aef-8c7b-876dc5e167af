import { before, describe, it } from 'node:test'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { ViewAnnouncementIndexUseCase } from '../view-announcement-index.use-case.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { ViewAnnouncementIndexQueryBuilder } from './view-announcement-index-query.builder.js'

describe('View announcement index use-case unit test', () => {
  let useCase: ViewAnnouncementIndexUseCase

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>

  before(() => {
    TestBench.setupUnitTest()

    const user = new UserEntityBuilder().build()

    const announcement = new AnnouncementEntityBuilder()
      .createdByUser(user)
      .build()

    announcementRepository = createStubInstance<Repository<Announcement>>(
      Repository<Announcement>
    )

    announcementRepository.findAndCount.resolves([[announcement], 1])

    useCase = new ViewAnnouncementIndexUseCase(
      announcementRepository
    )
  })

  it('Calls all methods once', async () => {
    const query = new ViewAnnouncementIndexQueryBuilder().build()

    await useCase.execute(query)

    assert.calledOnce(announcementRepository.findAndCount)
  })
})
