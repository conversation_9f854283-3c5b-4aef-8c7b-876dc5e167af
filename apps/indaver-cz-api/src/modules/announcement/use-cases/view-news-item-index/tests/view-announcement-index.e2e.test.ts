import { after, before, describe, it } from 'node:test'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { Permission } from '../../../../permission/permission.enum.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { ViewAnnouncementIndexQueryBuilder } from './view-announcement-index-query.builder.js'

describe('View announcement index e2e test', () => {
  let setup: EndToEndTestSetup

  let announcementRepository: Repository<Announcement>

  let unauthorizedUser: TestUser
  let authorizedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    announcementRepository = setup.dataSource.getRepository(Announcement)

    const [_unauthorizedUser, _authorizedUser] = await Promise.all([
      setup.authContext.getUser([]),
      setup.authContext.getUser([Permission.ANNOUNCEMENT_MANAGE])
    ])

    unauthorizedUser = _unauthorizedUser
    authorizedUser = _authorizedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/announcements`)

    expect(response).toHaveStatus(401)
  })

  it('returns 403 when unauthorized', async () => {
    const response = await request(setup.httpServer)
      .get(`/announcements`)
      .set('Authorization', `Bearer ${unauthorizedUser.token}`)

    expect(response).toHaveStatus(403)
  })

  it('returns  announcements', async () => {
    await announcementRepository.save(
      new AnnouncementEntityBuilder()
        .createdByUser(authorizedUser.user)
        .build()
    )

    const query = new ViewAnnouncementIndexQueryBuilder().build()

    const response = await request(setup.httpServer)
      .get(`/announcements`)
      .set('Authorization', `Bearer ${authorizedUser.token}`)
      .query(
        query
      )

    expect(response).toHaveStatus(200)
    expect(response.body.items.length).toBeGreaterThan(0)
    expect(response.body.meta.total).toBeGreaterThan(0)
    expect(response.body.meta.limit).toBe(query.pagination?.limit ?? 100)
    expect(response.body.meta.offset).toBe(query.pagination?.offset ?? 0)
  })
})
