import { ApiProperty } from '@nestjs/swagger'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { Locale } from '../../../localization/enums/locale.enum.js'

export class ViewAnnouncementTranslationIndexResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  title: string

  @ApiProperty({ type: String, enum: Locale, enumName: 'Locale' })
  language: Locale

  constructor (translation: AnnouncementTranslation) {
    this.uuid = translation.uuid
    this.title = translation.title
    this.language = translation.language
  }
}
