import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { ViewAnnouncementIndexController } from './view-announcement-index.controller.js'
import { ViewAnnouncementIndexUseCase } from './view-announcement-index.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ])
  ],
  controllers: [
    ViewAnnouncementIndexController
  ],
  providers: [
    ViewAnnouncementIndexUseCase
  ]
})
export class ViewAnnouncementIndexModule {}
