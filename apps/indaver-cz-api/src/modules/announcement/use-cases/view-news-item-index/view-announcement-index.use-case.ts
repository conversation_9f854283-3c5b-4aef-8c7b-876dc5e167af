import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { typeormPagination } from '@wisemen/pagination/dist/pagination-mapper.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { ViewAnnouncementIndexResponse } from './view-announcement-index.response.js'
import { ViewAnnouncementIndexQuery } from './view-announcement-index.query.js'

@Injectable()
export class ViewAnnouncementIndexUseCase {
  constructor (
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>
  ) {}

  async execute (query: ViewAnnouncementIndexQuery): Promise<ViewAnnouncementIndexResponse> {
    const pagination = typeormPagination(query.pagination)

    const [announcements, count] = await this.announcementRepository.findAndCount({
      select: {
        uuid: true,
        createdAt: true,
        updatedAt: true,
        type: true,
        startDate: true,
        endDate: true,
        translations: {
          uuid: true,
          title: true,
          language: true
        }
      },
      order: {
        createdAt: 'DESC'
      },
      relations: {
        translations: true,
        createdByUser: true
      },
      skip: pagination.skip,
      take: pagination.take
    })

    return new ViewAnnouncementIndexResponse(
      announcements,
      count,
      pagination.take,
      pagination.skip
    )
  }
}
