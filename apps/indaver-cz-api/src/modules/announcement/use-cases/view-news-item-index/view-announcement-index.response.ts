import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { PaginatedOffsetResponse } from '@wisemen/pagination/dist/offset/paginated-offset.response.js'
import { PublishStatus } from '../../../news/enums/publish-status.enum.js'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { ViewAnnouncementAuthorResponse } from '../../responses/view-announcement-author.response.js'
import { ViewAnnouncementTranslationIndexResponse } from './view-announcement-translation-index.response.js'

class AnnouncementIndexView {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, format: 'date-time' })
  createdAt: string

  @ApiProperty({ type: String, format: 'date-time' })
  updatedAt: string

  @ApiProperty({ type: String, enum: PublishStatus, enumName: 'PublishStatus' })
  publishStatus: PublishStatus

  @ApiProperty({ type: String, enum: AnnouncementType, enumName: 'AnnouncementType' })
  type: AnnouncementType

  @ApiProperty({ type: String, format: 'date-time' })
  startDate: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  endDate: string | null

  @ApiProperty({ type: ViewAnnouncementTranslationIndexResponse, isArray: true })
  translations: ViewAnnouncementTranslationIndexResponse[]

  @ApiProperty({ type: ViewAnnouncementAuthorResponse })
  author: ViewAnnouncementAuthorResponse

  constructor (
    announcement: Announcement
  ) {
    assert(announcement.createdByUser !== undefined)
    assert(announcement.translations !== undefined)

    this.uuid = announcement.uuid
    this.createdAt = announcement.createdAt.toISOString()
    this.updatedAt = announcement.updatedAt.toISOString()
    this.publishStatus = announcement.publishStatus
    this.type = announcement.type
    this.startDate = announcement.startDate.toISOString()
    this.endDate = announcement.endDate?.toISOString() ?? null

    this.translations = announcement.translations.map((translation) => {
      return new ViewAnnouncementTranslationIndexResponse(translation)
    })

    this.author = new ViewAnnouncementAuthorResponse(announcement.createdByUser)
  }
}

export class ViewAnnouncementIndexResponse extends PaginatedOffsetResponse<AnnouncementIndexView> {
  @ApiProperty({ type: AnnouncementIndexView, isArray: true })
  declare items: AnnouncementIndexView[]

  constructor (items: Announcement[], total: number, limit: number, offset: number) {
    const result = items.map(announcement => new AnnouncementIndexView(announcement))

    super(result, total, limit, offset)
  }
}
