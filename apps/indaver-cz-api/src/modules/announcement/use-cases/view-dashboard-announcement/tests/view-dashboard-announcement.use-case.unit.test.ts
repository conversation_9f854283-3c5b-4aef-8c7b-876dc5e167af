import { before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import { assert, createStubInstance, SinonStubbedInstance } from 'sinon'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { UserEntityBuilder } from '../../../../../app/users/tests/user-entity.builder.js'
import { ViewDashboardAnnouncementUseCase } from '../view-dashboard-announcement.use-case.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'

describe('View announcement use-case unit test', () => {
  let useCase: ViewDashboardAnnouncementUseCase

  let announcement: Announcement

  let announcementRepository: SinonStubbedInstance<Repository<Announcement>>
  let salesOrgUseCase: SinonStubbedInstance<GetCustomerDefaultSalesOrganisationIdUseCase>

  before(() => {
    TestBench.setupUnitTest()

    const user = new UserEntityBuilder().build()

    announcement = new AnnouncementEntityBuilder()
      .createdByUser(user)
      .addTranslations(new AnnouncementTranslationEntityBuilder().build())
      .build()

    announcementRepository = createStubInstance<Repository<Announcement>>(
      Repository<Announcement>
    )
    salesOrgUseCase = createStubInstance(GetCustomerDefaultSalesOrganisationIdUseCase)

    announcementRepository.findOneOrFail.resolves(announcement)

    useCase = new ViewDashboardAnnouncementUseCase(
      announcementRepository,
      salesOrgUseCase
    )
  })

  it('Calls all methods once', async () => {
    const selectedCustomerId = randomUUID()

    await useCase.execute(announcement.uuid, selectedCustomerId)

    assert.calledOnce(announcementRepository.findOneOrFail)
    assert.calledOnce(salesOrgUseCase.getOrganisationIdOrFail)
  })
})
