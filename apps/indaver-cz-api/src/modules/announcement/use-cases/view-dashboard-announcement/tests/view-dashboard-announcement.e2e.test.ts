import { after, before, describe, it } from 'node:test'
import { randomUUID } from 'crypto'
import request from 'supertest'
import { expect } from 'expect'
import { Repository } from 'typeorm'
import { TestBench } from '../../../../../../test/setup/test-bench.js'
import { EndToEndTestSetup } from '../../../../../../test/setup/end-to-end-test-setup.js'
import { TestUser } from '../../../../../app/users/tests/setup-user.type.js'
import { Announcement } from '../../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../../entities/announcement-translation.entity.js'
import { AnnouncementEntityBuilder } from '../../../tests/announcement-entity.builder.js'
import { AnnouncementTranslationEntityBuilder } from '../../../tests/announcement-translation-entity.builder.js'

describe('View announcement e2e test', () => {
  let setup: EndToEndTestSetup

  let announcementRepository: Repository<Announcement>
  let announcementTranslationRepository: Repository<AnnouncementTranslation>

  let admin: TestUser
  let authenticatedUser: TestUser

  before(async () => {
    setup = await TestBench.setupEndToEndTest()

    announcementRepository = setup.dataSource.getRepository(Announcement)
    announcementTranslationRepository = setup.dataSource.getRepository(AnnouncementTranslation)

    const [_authenticatedUser, _admin] = await Promise.all([
      setup.authContext.getUser([]),
      setup.authContext.getUser([])
    ])

    admin = _admin
    authenticatedUser = _authenticatedUser
  })

  after(async () => await setup.teardown())

  it('returns 401 when not authenticated', async () => {
    const response = await request(setup.httpServer)
      .get(`/dashboard-announcements/${randomUUID()}`)

    expect(response).toHaveStatus(401)
  })

  it('returns 200', async () => {
    const announcement = await announcementRepository.save(
      new AnnouncementEntityBuilder()
        .createdByUser(admin.user)
        .build()
    )

    await announcementTranslationRepository.save(
      new AnnouncementTranslationEntityBuilder()
        .forAnnouncement(announcement.uuid)
        .build()
    )

    const response = await request(setup.httpServer)
      .get(`/dashboard-announcements/${announcement.uuid}`)
      .set('Authorization', `Bearer ${authenticatedUser.token}`)

    expect(response).toHaveStatus(200)
  })
})
