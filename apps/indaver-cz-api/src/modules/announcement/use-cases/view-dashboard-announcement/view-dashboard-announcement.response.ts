import assert from 'assert'
import { ApiProperty } from '@nestjs/swagger'
import { ViewAnnouncementTranslationResponse } from '../../responses/view-announcement-translation.response.js'
import { AnnouncementType } from '../../enums/announcement-type.enum.js'
import { Announcement } from '../../entities/announcement.entity.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'

export class ViewDashboardAnnouncementResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String, enum: AnnouncementType, enumName: 'AnnouncementType' })
  type: AnnouncementType

  @ApiProperty({ type: String, format: 'date-time' })
  startDate: string

  @ApiProperty({ type: String, format: 'date-time', nullable: true })
  endDate: string | null

  @ApiProperty({ type: ViewAnnouncementTranslationResponse })
  translation: ViewAnnouncementTranslationResponse

  constructor (
    announcement: Announcement
  ) {
    assert(announcement.translations !== undefined)

    this.uuid = announcement.uuid
    this.type = announcement.type
    this.startDate = announcement.startDate.toISOString()
    this.endDate = announcement.endDate?.toISOString() ?? null

    const translation = announcement.translations.find(
      translation => translation.language === getCurrentLanguage()
    )

    assert(translation !== undefined)

    this.translation = new ViewAnnouncementTranslationResponse(translation)
  }
}
