import { InjectRepository } from '@wisemen/nestjs-typeorm'
import { Raw, Repository } from 'typeorm'
import { Injectable } from '@nestjs/common'
import { Announcement } from '../../entities/announcement.entity.js'
import { getCurrentLanguage } from '../../../localization/helpers/translate.helper.js'
import { GetCustomerDefaultSalesOrganisationIdUseCase } from '../../../customer/use-cases/get-customer-default-sales-organisation-id/get-customer-default-sales-organisation-id.use-case.js'
import { ViewDashboardAnnouncementResponse } from './view-dashboard-announcement.response.js'

@Injectable()
export class ViewDashboardAnnouncementUseCase {
  constructor (
    @InjectRepository(Announcement)
    private readonly announcementRepository: Repository<Announcement>,
    private readonly salesOrganisationUseCase: GetCustomerDefaultSalesOrganisationIdUseCase
  ) {}

  async execute (
    selectedCustomerId: string | null,
    announcementUuid: string
  ): Promise<ViewDashboardAnnouncementResponse> {
    const language = getCurrentLanguage()
    const salesOrganisationId = await this.getSaleOrganisationId(selectedCustomerId)

    const announcement = await this.announcementRepository.findOneOrFail({
      where: {
        uuid: announcementUuid,
        translations: {
          language
        },
        salesOrganisations: salesOrganisationId !== undefined
          ? Raw(organisation => `${organisation} @> :id`,
              { id: JSON.stringify([{ id: salesOrganisationId }]) }
            )
          : undefined
      },
      relations: {
        translations: true
      }
    })

    return new ViewDashboardAnnouncementResponse(announcement)
  }

  private async getSaleOrganisationId (
    selectedCustomerId: string | null
  ): Promise<string | undefined> {
    if (selectedCustomerId === null) {
      return undefined
    }

    return this.salesOrganisationUseCase.getOrganisationIdOrFail(selectedCustomerId)
  }
}
