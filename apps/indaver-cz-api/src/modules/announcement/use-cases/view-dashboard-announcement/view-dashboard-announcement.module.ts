import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@wisemen/nestjs-typeorm'
import { Announcement } from '../../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../../entities/announcement-translation.entity.js'
import { CustomerModule } from '../../../customer/customer.module.js'
import { ViewDashboardAnnouncementController } from './view-dashboard-announcement.controller.js'
import { ViewDashboardAnnouncementUseCase } from './view-dashboard-announcement.use-case.js'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Announcement,
      AnnouncementTranslation
    ]),
    CustomerModule
  ],
  controllers: [
    ViewDashboardAnnouncementController
  ],
  providers: [
    ViewDashboardAnnouncementUseCase
  ]
})
export class ViewDashboardAnnouncementModule {}
