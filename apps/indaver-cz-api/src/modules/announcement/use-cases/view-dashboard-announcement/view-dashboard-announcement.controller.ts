import { Controller, Get } from '@nestjs/common'
import { ApiTags, ApiOAuth2, ApiOkResponse } from '@nestjs/swagger'
import { UuidParam } from '@wisemen/decorators'
import { ApiNotFoundErrorResponse } from '../../../exceptions/api-errors/api-error-response.decorator.js'
import { NotFoundError } from '../../../exceptions/generic/not-found.error.js'
import { GlobalCustomerRequired } from '../../../auth/decorators/no-customer-required.decorator.js'
import { AuthContext } from '../../../auth/auth.context.js'
import { ViewDashboardAnnouncementResponse } from './view-dashboard-announcement.response.js'
import { ViewDashboardAnnouncementUseCase } from './view-dashboard-announcement.use-case.js'

@ApiTags('Dashboard Announcement')
@ApiOAuth2([])
@Controller('dashboard-announcements/:uuid')
export class ViewDashboardAnnouncementController {
  constructor (
    private readonly authContext: AuthContext,
    private readonly useCase: ViewDashboardAnnouncementUseCase
  ) { }

  @Get()
  @GlobalCustomerRequired()
  @ApiOkResponse({ type: ViewDashboardAnnouncementResponse })
  @ApiNotFoundErrorResponse(NotFoundError)
  public async viewDashboardAnnouncement (
    @UuidParam('uuid') announcementUuid: string
  ): Promise<ViewDashboardAnnouncementResponse> {
    const selectedCustomerId = this.authContext.getSelectedCustomerId()
    return await this.useCase.execute(selectedCustomerId, announcementUuid)
  }
}
