import { randomUUID } from 'crypto'
import { randWord } from '@ngneat/falso'
import { AnnouncementTranslation } from '../entities/announcement-translation.entity.js'
import { Locale } from '../../localization/enums/locale.enum.js'

export class AnnouncementTranslationEntityBuilder {
  private translation: AnnouncementTranslation

  constructor () {
    this.reset()
  }

  reset (): this {
    this.translation = new AnnouncementTranslation()

    this.translation.uuid = randomUUID()
    this.translation.createdAt = new Date()
    this.translation.updatedAt = new Date()
    this.translation.language = Locale.EN_GB
    this.translation.title = randWord()
    this.translation.content = {}

    return this
  }

  forAnnouncement (announcementUuid: string): this {
    this.translation.announcementUuid = announcementUuid

    return this
  }

  withTitle (title: string): this {
    this.translation.title = title

    return this
  }

  withContent (content: object): this {
    this.translation.content = content

    return this
  }

  withLanguage (language: Locale): this {
    this.translation.language = language

    return this
  }

  build (): AnnouncementTranslation {
    const result = this.translation

    this.reset()

    return result
  }
}
