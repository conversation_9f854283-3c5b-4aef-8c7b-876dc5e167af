import { randomUUID } from 'crypto'
import dayjs from 'dayjs'
import { Announcement } from '../entities/announcement.entity.js'
import { AnnouncementTranslation } from '../entities/announcement-translation.entity.js'
import { User } from '../../../app/users/entities/user.entity.js'
import { AnnouncementType } from '../enums/announcement-type.enum.js'

export class AnnouncementEntityBuilder {
  private announcement: Announcement

  constructor () {
    this.reset()
  }

  reset (): this {
    this.announcement = new Announcement()

    this.announcement.uuid = randomUUID()
    this.announcement.createdAt = new Date()
    this.announcement.updatedAt = new Date()
    this.announcement.type = AnnouncementType.INFORMATIONAL
    this.announcement.startDate = dayjs().add(1, 'day').toDate()
    this.announcement.endDate = null

    this.announcement.translations = []

    return this
  }

  withUuid (uuid: string): this {
    this.announcement.uuid = uuid

    return this
  }

  createdByUser (user: User): this {
    this.announcement.createdByUser = user
    this.announcement.createdByUserUuid = user.uuid

    return this
  }

  createdByUserUuid (userUuid: string): this {
    this.announcement.createdByUserUuid = userUuid

    return this
  }

  withAnnouncementType (type: AnnouncementType): this {
    this.announcement.type = type

    return this
  }

  addTranslations (translation: AnnouncementTranslation): this {
    this.announcement.translations?.push(translation)

    return this
  }

  withType (type: AnnouncementType): this {
    this.announcement.type = type

    return this
  }

  withStartDate (startDate: Date): this {
    this.announcement.startDate = startDate

    return this
  }

  withEndDate (endDate: Date | null): this {
    this.announcement.endDate = endDate

    return this
  }

  build (): Announcement {
    const result = this.announcement

    this.reset()

    return result
  }
}
