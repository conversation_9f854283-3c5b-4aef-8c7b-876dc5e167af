import { randomUUID } from 'crypto'
import { AnnouncementTranslation } from '../entities/announcement-translation.entity.js'
import { Locale } from '../../localization/enums/locale.enum.js'

export class ValidAnnouncementTranslationEntityBuilder {
  private announcementTranslation: AnnouncementTranslation

  constructor () {
    this.init()
  }

  init (): this {
    this.announcementTranslation = new AnnouncementTranslation()

    this.announcementTranslation.uuid = randomUUID()
    this.announcementTranslation.createdAt = new Date()
    this.announcementTranslation.updatedAt = new Date()
    this.announcementTranslation.language = Locale.EN_GB
    this.announcementTranslation.title = 'test'
    this.announcementTranslation.content = { test: 'test' }

    return this
  }

  withAnnouncementUuid (announcementUuid: string): this {
    this.announcementTranslation.announcementUuid = announcementUuid

    return this
  }

  build (): AnnouncementTranslation {
    return this.announcementTranslation
  }
}
