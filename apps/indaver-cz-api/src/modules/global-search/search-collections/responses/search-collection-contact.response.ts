import { ApiProperty } from '@nestjs/swagger'
import { TypesenseContact } from '../../../contact/typesense/typesense-contact.js'

export class SearchCollectionContactResponse {
  @ApiProperty({ type: String, format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  @ApiProperty({ type: String, format: 'email' })
  email: string

  constructor (contact: TypesenseContact) {
    this.uuid = contact.id
    this.firstName = contact.firstName
    this.lastName = contact.lastName
    this.email = contact.email
  }
}
