import { ApiProperty } from '@nestjs/swagger'
import { TypesenseUser } from '../../../../app/users/typesense/typesense-user.js'

export class SearchCollectionUserResponse {
  @ApiProperty({ format: 'uuid' })
  uuid: string

  @ApiProperty({ type: String })
  firstName: string

  @ApiProperty({ type: String })
  lastName: string

  @ApiProperty({ format: 'email' })
  email: string

  constructor (user: TypesenseUser) {
    this.uuid = user.id
    this.firstName = user.firstName
    this.lastName = user.lastName
    this.email = user.email
  }
}
