import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { WasteInquiryStatus } from '@indaver/types'

// Re-export the shared enum for backward compatibility
export { WasteInquiryStatus }

export function mapSapValueToWasteInquiryStatus(
  status: string
): WasteInquiryStatus {
  switch (status) {
    case 'A':
      return WasteInquiryStatus.NEW
    case 'B':
      return WasteInquiryStatus.IN_PROGRESS
    case 'C':
      return WasteInquiryStatus.IN_PROGRESS
    case 'D':
      return WasteInquiryStatus.CONFORMITY_CONFIRMED
    case 'E':
      return WasteInquiryStatus.IN_PROGRESS
    case 'F':
      return WasteInquiryStatus.SOLUTION_DEFINED
    case 'G':
      return WasteInquiryStatus.SOLUTION_DEFINED
    case 'H':
      return WasteInquiryStatus.SOLUTION_DEFINED
    case 'I':
      return WasteInquiryStatus.OFFER_SENT
    case 'J':
      return WasteInquiryStatus.OFFER_APPROVED
    case 'K':
      return WasteInquiryStatus.COMPLETED
    case 'L':
      return WasteInquiryStatus.REJECTED
    default:
      return WasteInquiryStatus.NEW
  }
}

export function mapWasteInquiryStatusToSapValue(
  status: WasteInquiryStatus
): string[] {
  switch (status) {
    case WasteInquiryStatus.DRAFT:
      throw new Error('Waste inquiry status DRAFT cannot be mapped to SAP value')
    case WasteInquiryStatus.NEW:
      return ['A']
    case WasteInquiryStatus.IN_PROGRESS:
      return ['B', 'C', 'E']
    case WasteInquiryStatus.SOLUTION_DEFINED:
      return ['F', 'G', 'H']
    case WasteInquiryStatus.CONFORMITY_CONFIRMED:
      return ['D']
    case WasteInquiryStatus.OFFER_SENT:
      return ['I']
    case WasteInquiryStatus.OFFER_APPROVED:
      return ['J']
    case WasteInquiryStatus.COMPLETED:
      return ['K']
    case WasteInquiryStatus.REJECTED:
      return ['L']
    default:
      exhaustiveCheck(status)
  }
}
