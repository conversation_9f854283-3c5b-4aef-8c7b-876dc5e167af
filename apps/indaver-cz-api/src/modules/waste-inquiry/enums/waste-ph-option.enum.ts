import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapPh } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'
import { WastePhOption } from '@indaver/types'

// Re-export the shared enum for backward compatibility
export { WastePhOption }

export function mapWastePhOptionToSapValue(
  wastePhOption: WastePhOption
): SapPh {
  switch (wastePhOption) {
    case WastePhOption.LESS_THAN_2:
      return '1'
    case WastePhOption.BETWEEN_2_AND_4:
      return '2'
    case WastePhOption.BETWEEN_4_AND_10:
      return '3'
    case WastePhOption.MORE_THAN_10:
      return '4'
    default:
      exhaustiveCheck(wastePhOption)
  }
}

export function mapSapValueToWastePhOption(
  sapValue: SapPh
): WastePhOption | null {
  switch (sapValue) {
    case '1':
      return WastePhOption.LESS_THAN_2
    case '2':
      return WastePhOption.BETWEEN_2_AND_4
    case '3':
      return WastePhOption.BETWEEN_4_AND_10
    case '4':
      return WastePhOption.MORE_THAN_10
    default:
      return null
  }
}
