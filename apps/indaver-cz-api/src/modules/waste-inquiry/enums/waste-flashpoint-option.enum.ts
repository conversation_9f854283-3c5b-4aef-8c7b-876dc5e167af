import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapFlashpoint } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'
import { WasteFlashpointOption } from '@indaver/types'

// Re-export the shared enum for backward compatibility
export { WasteFlashpointOption }

export function mapWasteFlashpointOptionToSapValue(
  wasteFlashpointOption: WasteFlashpointOption
): SapFlashpoint {
  switch (wasteFlashpointOption) {
    case WasteFlashpointOption.LESS_THAN_23:
      return '1'
    case WasteFlashpointOption.BETWEEN_23_AND_60:
      return '2'
    case WasteFlashpointOption.MORE_THAN_60:
      return '3'
    default:
      exhaustiveCheck(wasteFlashpointOption)
  }
}

export function mapSapValueToWasteFlashpointOption(
  sapValue: SapFlashpoint
): WasteFlashpointOption | null {
  switch (sapValue) {
    case '1':
      return WasteFlashpointOption.LESS_THAN_23
    case '2':
      return WasteFlashpointOption.BETWEEN_23_AND_60
    case '3':
      return WasteFlashpointOption.MORE_THAN_60
    default:
      return null
  }
}
