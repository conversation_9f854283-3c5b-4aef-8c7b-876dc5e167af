import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapStateOfMatter } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'
import { StateOfMatter } from '@indaver/types'

// Re-export the shared enum for backward compatibility
export { StateOfMatter }

export function mapStateOfMatterToSapValue(stateOfMatter: StateOfMatter): SapStateOfMatter {
  switch (stateOfMatter) {
    case StateOfMatter.GASEOUS:
      return '01'
    case StateOfMatter.POWDER:
      return '02'
    case StateOfMatter.SLUDGY:
      return '03'
    case StateOfMatter.SOLID:
      return '04'
    case StateOfMatter.LIQUID:
      return '05'
    case StateOfMatter.VISCOUS:
      return '06'
    case StateOfMatter.LIQUID_WITH_SOLIDS:
      return '07'
    case StateOfMatter.NO_DATA_AVAILABLE:
      return '99'
    default:
      exhaustiveCheck(stateOfMatter)
  }
}

export function mapSapValueToStateOfMatter(sapValue: SapStateOfMatter): StateOfMatter | null {
  switch (sapValue) {
    case '01':
      return StateOfMatter.GASEOUS
    case '02':
      return StateOfMatter.POWDER
    case '03':
      return StateOfMatter.SLUDGY
    case '04':
      return StateOfMatter.SOLID
    case '05':
      return StateOfMatter.LIQUID
    case '06':
      return StateOfMatter.VISCOUS
    case '07':
      return StateOfMatter.LIQUID_WITH_SOLIDS
    case '99':
      return StateOfMatter.NO_DATA_AVAILABLE
    default:
      return null
  }
}
