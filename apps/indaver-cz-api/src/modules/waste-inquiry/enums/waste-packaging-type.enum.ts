import { exhaustiveCheck } from '../../../utils/helpers/exhaustive-check.helper.js'
import { SapWasteType } from '../../sap/use-cases/create-waste-inquiry/create-waste-inquiry-sap.command.js'
import { WastePackagingType } from '@indaver/types'

// Re-export the shared enum for backward compatibility
export { WastePackagingType }

export function mapWastePackagingTypeToSapValue(
  wastePackagingType: WastePackagingType
): SapWasteType {
  switch (wastePackagingType) {
    case WastePackagingType.BULK:
      return '1'
    case WastePackagingType.PACKAGED:
      return '2'
    default:
      exhaustiveCheck(wastePackagingType)
  }
}

export function mapSapValueToWastePackagingType(
  sapValue: SapWasteType
): WastePackagingType | null {
  switch (sapValue) {
    case '1':
      return WastePackagingType.BULK
    case '2':
      return WastePackagingType.PACKAGED
    default:
      return null
  }
}
