workspace {
  name "Indaver Customer Zone"
  description "Software architecture documentation of Indaver."
  !identifiers hierarchical

  model {
    clientUser = person "Client User" {
      tags "User"
    }

    clientAdmin = person "Client Admin" {
      tags "User"
    }

    superAdmin = person "Super Admin" {
      tags "Admin"
    }

    sap = softwareSystem "SAP S4/HANA" {
      tags "Sap"
      description "SAP Enterprise Resource Planning"
    }

    azureEntra = softwareSystem "Azure AD" {
      tags "AzureEntra"
      description "Identity and role management"
    }

    azure = softwareSystem "Azure" {
      tags "Azure"

      vm = container "Azure VM" {
        tags "AzureVm"
      }
    }

    application = softwareSystem "Customer Zone" {
      tags "Wisemen"
      description "Software system"

      webapp = container "Customer Zone web app" {
        tags "Vue"
        technology "Vue"
      }

      publicGateway = container "Public gateway" {
        tags "PublicGateway"
        technology "PublicGateway"
      }

      loadBalancer = container "LoadBalancer" {
        tags "LoadBalancer"
        technology "LoadBalancer"
      }

      ingress = container "Ingress" {
        tags "Ingress" "Kubernetes"
        description "Exposes API routes"
        technology "Kubernetes"
      }

      api = container "API" {
        tags "Node.js"
        description "Exposes API routes"
        technology "NodeJS"
      }

      worker = container "Worker" {
        tags "Node.js"
        description "Worker for jobs"
        technology "NodeJS"
      }

      cronjob = container "Cronjob" {
        tags "Node.js"
        description "Cronjob for scheduled tasks"
        technology "NodeJS"
      }

      websocket = container "Websocket" {
        tags "Node.js"
        description "Websocket for real-time communication"
        technology "NodeJS"
      }

      natsOutbox = container "Nats outbox" {
        tags "Node.js"
        description "Publishes events to NATS"
        technology "NodeJS"
      }

      database = container "Database" {
        tags "PostgreSQL"
        description "Stores asset and charging sessions"
        technology "PostgreSQL"
      }

      cache = container "Redis cache" {
        tags "Redis"
        description "Cache for frequently accessed content like system permissions"
        technology "Redis"
      }

      nats = container "Nats" {
        tags "Nats"
        description "Event streaming middleware"
        technology "Nats"
      }

      objectStorage = container "Object storage" {
        tags "ObjectStorage"
        description "Object storage"
        technology "Scaleway"
      }

      signoz = container "Signoz" {
        tags "Signoz"
        description "Telemetry monitoring"
        technology "Signoz"
      }

      sentry = container "Sentry" {
        tags "Sentry"
        description "Error monitoring"
        technology "Sentry"
      }

      zitadel = container "Zitadel" {
        tags "Zitadel"
        description "Identity and Access Management"
        technology "Zitadel"
      }

      webapp -> loadBalancer "Retrieve content for web app" "HTTPS"
      loadBalancer -> ingress "Routes and distributes traffic and handles HTTPS" "HTTPS"
      ingress -> api "Forwards requests to API" "HTTP"
      ingress -> websocket "Forwards requests to Websocket" "HTTP"
      ingress -> zitadel "Forwards requests to Zitadel" "HTTP"
      zitadel -> azureEntra "Authenticates users" "HTTP"
      api -> cache "Caches frequently accessed content" "TCP"
      api -> database "Stores and retrieves data" "TCP"
      api -> objectStorage "Stores and retrieves files" "TCP"
      api -> nats "Publishes and subscribes to events" "TCP"
      api -> sentry "Reports errors"
      api -> signoz "Reports tracing"
      api -> database "Creates jobs" "TCP"
      api -> publicGateway "Communicates to external services" "HTTPS"
      publicGateway -> sap "Retrieves and Stores data" "HTTPS"
      worker -> database "Processes jobs" "TCP"
      worker -> nats "Publishes events" "TCP"
      cronjob -> database "Retrieves and Stores data" "TCP"
      websocket -> database "Stores and retrieves data" "TCP"
      websocket -> nats "Publishes and subscribes to events" "TCP"
      natsOutbox -> nats "Publishes events" "TCP"
      natsOutbox -> database "retrieves outbox events" "TCP"
    }

    superAdmin -> application.webapp "Admin manages web app"
    clientAdmin -> application.webapp "Manages client organisation"
    clientUser -> application.webapp "Uses web app"
    azure.vm -> sap "Retrieves and Stores data" "HTTPS"

    deployment = deploymentEnvironment "Deployment" {
      deploymentNode "User computer" {
        containerInstance application.webApp
      }

      deploymentNode "VPC" {
        deploymentNode "Public Gateway" {
          containerInstance application.publicGateway
        }

        deploymentNode "Load Balancer" {
          containerInstance application.loadBalancer
        }

        deploymentNode "Kubernetes Cluster" {
          deploymentNode "Ingress" {
            containerInstance application.ingress
          }

          deploymentNode "API Pod" {
            containerInstance application.api
          }
          deploymentNode "Websocket Pod" {
            containerInstance application.websocket
          }
          deploymentNode "Worker Pod" {
            containerInstance application.worker
          }
          deploymentNode "CronJob Pod" {
            containerInstance application.cronJob
          }
          deploymentNode "Nats Outbox Pod" {
            containerInstance application.natsOutbox
          }
          deploymentNode "Redis Pod" {
            containerInstance application.cache
          }

          deploymentNode "Zitadel" {
            containerInstance application.zitadel
          }

          deploymentNode "Nats" {
            containerInstance application.nats
          }
        }

        deploymentNode "Managed Database" {
          containerInstance application.database
        }

        deploymentNode "Object Storage" {
          containerInstance application.objectStorage
        }
      }

      deploymentNode "OpenTelemetry" {
        containerInstance application.signoz
      }

      deploymentNode "Sentry" {
        containerInstance application.sentry
      }
    }
  }

  views {
    !include "branding.dsl"
    styles {
      !include "default-styles.dsl"

      element "Wisemen" {
        icon "./images/wisemen.png"
      }
    }

    systemLandscape {
      include *

      default
      title "Default System Landscape view"
    }

    container application "application" {
      include *
      title "Indaver Customer Zone"
    }

    dynamic application {
      title "Data flow (extensive)"
      application.webapp -> application.loadBalancer "HTTPS (OpenID + OAuth2 Web)"
      application.loadBalancer -> application.ingress "HTTPS"
      application.ingress -> application.api "HTTP"
      application.ingress -> application.zitadel "HTTP"
      application.api -> application.publicGateway "HTTPS"
      application.zitadel -> azureEntra "HTTPS"
      application.publicGateway -> sap "HTTPS (Odata + API key + IP whitelist)"
    }

    dynamic application {
      title "Data flow (limited)"
      azure.vm -> sap "HTTPS (Odata)"
    }


    deployment * deployment {
      include *
    }

    deployment * deployment {
      include *
      exclude *->*
    }
  }
}
