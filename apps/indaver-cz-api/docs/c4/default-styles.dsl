element "Element" {
  shape roundedbox
  border solid
  stroke #1168bd
  strokeWidth 4
  background #ffffff
  metadata false
}

element "Container" {
  metadata true
}

element "Group" {
  strokeWidth 3
}

element "Person" {
  shape person
}

element "Boundary:SoftwareSystem" {
  strokeWidth 3
}

element "Website" {
  shape WebBrowser
}

element "Vue" {
  shape WebBrowser
  icon "./images/Vue.png"
  stroke #3FB984
}

element "MobileApp" {
  shape MobileDevicePortrait
}

element "DesktopApp" {
  shape Window
}

element "API" {
  shape RoundedBox
}

element "Nginx" {
  shape RoundedBox
  icon "./images/Nginx.png"
  stroke #1168bd
}

element "Node.js" {
  shape RoundedBox
  icon "./images/Nodejs.png"
  stroke #026E00
}

element "NestJS" {
  shape RoundedBox
  stroke #e11e4d
  icon "./images/nestjs.png"
}

element "DataStore" {
  shape cylinder
}

element "PostgreSQL" {
  shape cylinder
  icon "./images/PostgreSQL.png"
  background #31648C
  color #ffffff
  stroke #000000
}

element "Redis" {
  shape cylinder
  icon "./images/Redis.png"
  stroke #000000
  background #DD3528
  color #ffffff
}

element "MessageQueue" {
  shape pipe
}

element "Azure" {
  icon "./images/Azure.png"
}

element "AzureEntra" {
  icon "./images/AzureEntra.png"
  stroke #0A8191
}

element "AzureVm" {
  icon "./images/AzureVm.png"
}

element "Kubernetes" {
  icon "./images/Kubernetes.png"
  stroke #2E6CE6
}

element "PublicGateway" {
  icon "./images/LoadBalancer.png"
  stroke #4f1696
}

element "LoadBalancer" {
  icon "./images/LoadBalancer.png"
  stroke #4f1696
}

element "Signoz" {
  icon "./images/Signoz.png"
  stroke #F36847
}

element "Zitadel" {
  icon "./images/Zitadel.png"
  stroke #0A8191
}

element "Sentry" {
  icon "./images/Sentry.png"
  stroke #362D59
}

element "Typesense" {
  icon "./images/Typesense.png"
}

element "ObjectStorage" {
  icon "./images/ObjectStorage.png"
  stroke #4f1696
}

element "Twilio" {
  icon "./images/Twilio.jpg"
  stroke #F22E45
}

element "Nats" {
  icon "./images/Nats.png"
}

element "Sap" {
  icon "./images/Sap.png"
}
