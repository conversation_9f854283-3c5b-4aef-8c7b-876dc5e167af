table authorization.user_customer {
  user_id varchar [pk, not null]
  customer_id varchar [pk, not null]
}

table authorization.user_waste_producer {
  user_id varchar [pk, not null]
  customer_id varchar [pk, not null]
  waste_producer_id varchar [pk, not null]
}

table authorization.customer_waste_producer {
  customer_id varchar [pk, not null]
  waste_producer_id varchar [pk, not null]
}

table user {
  uuid uuid [pk, not null, default: 'uuid_generate_v4()']
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  deleted_at timestamp(3)
  zitadel_sub varchar [unique, not null]
  email varchar [not null]
  first_name varchar
  last_name varchar

  Indexes {
    zitadel_sub [unique]
    email [unique]
  }
}

table role {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  name varchar [not null, unique]
  permissions varchar [not null]
}

table user_role {
  uuid uuid [pk, not null]
  user_uuid uuid [ref: > user.uuid]
  role_uuid uuid [ref: > role.uuid]
}

table file {
  uuid uuid [pk, not null, default: 'uuid_generate_v4()']
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  deleted_at timestamp(3)
  name varchar [not null]
  mime_type mime_type
  is_upload_confirmed boolean [not null, default: false]
  user_uuid uuid [ref: > user.uuid]
}

table file_link {
  uuid uuid [pk, not null, default: 'uuid_generate_v4()']
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  file_uuid uuid [ref: > file.uuid, not null]
  entity_type varchar [not null]
  entity_uuid uuid [not null]
  entity_part varchar [not null]
  order smallint

  Indexes {
    (entity_type, entity_uuid)
    entity_part
  }
}

table log {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  deleted_at timestamp(3) []
  type varchar [not null]
  user_uuid uuid [ref: > user.uuid]
  impersonated_by_user_uuid uuid
  mocked_user_uuid uuid [ref: > user.uuid]
  meta jsonb [not null]
  entity_type varchar
  entity_uuid uuid
  

  Indexes {
    type
    entity_type
    entity_uuid
  }
}

table dynamic_table {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  name varchar [unique, not null]
}

table dynamic_table_column {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  dynamic_table_uuid uuid [ref: > dynamic_table.uuid, not null]
  name varchar [not null] // Friendly name for user

  is_hidable boolean [not null]
  applicable_fields jsonb [not null]
  searchable_fields jsonb [not null]
  filterable_field jsonb
  sortable_fields jsonb [not null]

  Indexes {
    (dynamic_table_uuid, name) [unique]
  }
}

table dynamic_table_view {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  dynamic_table_uuid uuid [ref: > dynamic_table.uuid, not null]
  created_by_user_uuid uuid [ref: > user.uuid, not null]
  name varchar [not null]
  is_global boolean [default: false]
  is_global_default boolean [default: false]
  
  configuration jsonb [not null]

  Indexes {
    (created_by_user_uuid, name) [unique]
  }
}

table user_default_dynamic_table_view {
  user_uuid uuid [pk, ref: > user.uuid, not null]
  dynamic_table_uuid uuid [pk, ref: > dynamic_table_view.uuid, not null]
  dynamic_table_view_uuid uuid [ref: > dynamic_table_view.uuid, not null]
}

table waste_inquiry {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  waste_stream_name varchar
  waste_stream_description varchar
  ewc_level_1 varchar
  ewc_level_2 varchar
  ewc_level_3 varchar
  state_of_matter waste_state_of_matter
  packaging_type waste_packaging_type
  flashpoint flashpoint_enum
  ph ph_enum
  specific_gravity real
  stable_temperature_type temperature_type
  min_stable_temperature real
  max_stable_temperature real
  average_stable_temperature real
  no_sds boolean [default: false, not null]
  no_analysis_report boolean [default: false, not null]
  composition jsonb
  is_sample_available boolean
  selected_legislation_options waste_legislation_option[] [default: '[]', not null]
  svhc_extra svhc_extra_enum
  legislation_remarks varchar
  selected_property_options waste_property_option[] [default: '[]', not null]
  property_remarks varchar
  expected_yearly_volume_amount real
  expected_yearly_volume_unit waste_unit
  expected_per_collection_quantity real
  expected_per_collection_unit waste_unit
  discharge_frequency discharge_frequency
  campaign_name varchar
  first_collection_date date
  expected_end_date date
  collection_remarks varchar
  is_transport_by_indaver boolean
  is_regulated_transport boolean
  un_numbers jsonb [not null, default: '[]']
  transport_type transport_type
  container_loading_type container_loading_type
  transport_volume_amount real
  transport_volume_unit waste_unit
  remarks varchar
  send_copy_to_contacts jsonb [not null, default: '[]']
  submitted_on timestamp(3)

  customer_id varchar
  waste_producer_id varchar
  pick_up_address_id uuid

  created_by_user_uuid uuid [ref: > user.uuid, not null]

  Indexes {
    customer_id
    created_by_user_uuid
  }
}

table contact {
  uuid uuid [pk, not null, default: 'uuid_generate_v4()']
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  user_uuid uuid [ref: > user.uuid, not null]
  first_name varchar [not null]
  last_name varchar [not null]
  email varchar [not null]

  Indexes {
    (user_uuid, email) [unique]
  }
}

table news_item {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  is_published boolean
  start_date timestamp
  end_date timestamp
  created_by_user_uuid uuid [ref: > user.uuid, not null]

  Indexes {
    created_by_user_uuid
  }
}

table news_item_translation {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  title varchar
  content jsonb
  language translation_language [not null]
  news_item_uuid uuid [ref: > news_item.uuid, not null]

  Indexes {
    news_item_uuid
    (news_item_uuid, language) [unique]
  }
}

table announcement {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  type announcement_type [default: 'informational', not null]
  is_published boolean
  start_date timestamp
  end_date timestamp
  created_by_user_uuid uuid [ref: > user.uuid, not null]

  Indexes {
    created_by_user_uuid
  }
}

table announcement_translation {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]
  title varchar
  content jsonb
  language translation_language [not null]
  announcement_uuid uuid [ref: > announcement.uuid, not null]

  Indexes {
    announcement_uuid
    (announcement_uuid, language) [unique]
  }
}

table weekly_planning_request {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  customer_id varchar
  waste_producer_id varchar
  is_unknown_waste_producer boolean [not null, default: false]
  pick_up_address_ids uuid[] [default: '[]', not null]  
  is_unknown_pick_up_address boolean [not null, default: false]

  remarks varchar
  send_copy_to_contacts jsonb [not null, default: '[]']
  submitted_on timestamp(3)

  created_by_user_uuid uuid [ref: > user.uuid, not null]
  inquiry_number varchar
}

table pick_up_request {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  customer_id varchar
  waste_producer_id varchar
  pick_up_address_ids uuid[] [default: '[]', not null]
  transport_mode pick_up_transport_mode
  materials object[]
  start_date date
  end_date date
  

  remarks varchar
  send_copy_to_contacts jsonb [not null, default: '[]']
  submitted_on timestamp(3)
  start_time time

  weekly_planning_request_uuid uuid [ref: > weekly_planning_request.uuid]

  created_by_user_uuid uuid [ref: > user.uuid, not null]

  Indexes {
    customer_id
    created_by_user_uuid
  }
}

table un_number {
  number char(4) [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  adr_class varchar
  packaging_group varchar
  danger_label1 varchar
  danger_label2 varchar
  danger_label3 varchar
  is_hazardous boolean [not null, default: false]
}

table un_number_translation {
  uuid uuid [pk, not null]
  created_at timestamp(3) [default: 'now()', not null]
  updated_at timestamp(3) [default: 'now()', not null]

  description varchar [not null]
  language translation_language [not null]
  un_number_number char(4) [ref: > un_number.number, not null]

  Indexes {
    un_number_number
    (un_number_number, language) [unique]
  }
}

enum waste_unit {
  m3
  pc
  to
}

enum waste_state_of_matter {
  gaseous
  powder
  sludgy
  solid
  liquid
  viscous
  "liquid-with-solids"
  "no-data-available"
}

enum waste_packaging_type {
  bulk
  packaged
}

enum flashpoint_enum {
  "< 23°"
  "23° - 60°"
  "> 60°"
}

enum ph_enum {
  "< 2"
  "2 - 4"
  "4 - 10"
  "> 10"
}

enum temperature_type {
  ambient
  other
}

enum waste_legislation_option {
  none
  radioactive
  cwc
  "controlled-drugs"
  "drug-precursor"
  "hg-containing"
  "animal-byproduct"
  "infectious-waste"
  svhc
}

enum svhc_extra_enum {
  other
  "< 1 mg/kg"
  "> 1 mg/kg"
}

enum waste_property_option {
  none
  explosive
  gaseous
  peroxide
  "polymerisation-senitive"
  pyrophoric
  "strong-oxidizing"
  "reactive-with-t-gas"
  "reactive-with-f-gas"
  "high-acute-toxic"
  "thermal-unstable"
}

enum discharge_frequency {
  "once-off-stream"
  "regular-stream"
  "once-off-campaign"
  "regular-campaign"
}

enum transport_type {
  container
  skip
  "tipper-truck"
  "rel-truck"
  other
}

enum container_loading_type {
  hoop
  chain
}

enum translation_language {
  en
  nl
  fr
  es
  de
}

enum announcement_type {
  informational
  urgent
}

enum pick_up_transport_mode {
  "packaged-curtain-sider-truck"
  "bulk-skips-container"
  "bulk-vacuum-tankers-road-tankers"
  "bulk-iso-tank"
}