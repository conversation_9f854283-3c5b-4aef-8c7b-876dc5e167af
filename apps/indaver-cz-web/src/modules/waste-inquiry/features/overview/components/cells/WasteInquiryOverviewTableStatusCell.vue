<script setup lang="ts">
import type { VcBadgeProps } from '@wisemen/vue-core-components'
import { VcBadge } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { WasteInquiryStatus } from '@indaver/types'
import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { WasteInquiryStatusEnumUtil } from '@/models/enums/wasteInquiryStatus.enum'

const props = defineProps<{
  status: WasteInquiryStatus
}>()

const i18n = useI18n()

const statusBars = [
  [
    WasteInquiryStatus.NEW,
  ],
  [
    WasteInquiryStatus.IN_PROGRESS,
  ],
  [
    WasteInquiryStatus.SOLUTION_DEFINED,
  ],
  [
    WasteInquiryStatus.OFFER_SENT,
  ],
  [
    WasteInquiryStatus.OFFER_APPROVED,
  ],
  [
    WasteInquiryStatus.COMPLETED,
    WasteInquiryStatus.REJECTED,
    WasteInquiryStatus.CONFORMITY_CONFIRMED,
  ],
]
const statusColor = computed<VcBadgeProps['color']>(() => {
  switch (props.status) {
    case WasteInquiryStatus.DRAFT:
      return 'light-blue'
    case WasteInquiryStatus.NEW:
      return 'moss'
    case WasteInquiryStatus.IN_PROGRESS:
    case WasteInquiryStatus.SOLUTION_DEFINED:
      return 'purple'
    case WasteInquiryStatus.OFFER_SENT:
      return 'warning'
    case WasteInquiryStatus.OFFER_APPROVED:
      return 'success'
    case WasteInquiryStatus.COMPLETED:
      return 'gray'
    case WasteInquiryStatus.REJECTED:
      return 'error'
    case WasteInquiryStatus.CONFORMITY_CONFIRMED:
      return 'gray'
    default:
      return 'gray'
  }
})

function isStatusBarActive(status: WasteInquiryStatus[]): boolean {
  return status.includes(props.status)
}
</script>

<template>
  <DataTableCell>
    <AppGroup gap="2xl">
      <div class="gap-x-sm flex items-center">
        <template
          v-for="(statusBarStatus, index) of statusBars"
          :key="index"
        >
          <div
            v-if="!isStatusBarActive(statusBarStatus)"
            class="bg-tertiary h-0.5 w-3 rounded-full"
          />
          <VcBadge
            v-else
            :class-config="{
              root: 'px-sm',
            }"
            :color="statusColor"
            variant="translucent"
            size="sm"
          >
            {{ index + 1 }}
          </VcBadge>
        </template>
      </div>

      <VcBadge
        :color="statusColor"
        variant="translucent"
        size="sm"
      >
        {{ i18n.t(WasteInquiryStatusEnumUtil.getI18nKey(props.status)) }}
      </VcBadge>
    </AppGroup>
  </DataTableCell>
</template>
