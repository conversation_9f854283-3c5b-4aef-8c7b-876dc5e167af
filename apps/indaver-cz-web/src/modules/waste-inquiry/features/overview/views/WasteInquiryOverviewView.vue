<script setup lang="ts">
import {
  useVcDial<PERSON>,
  useVcToast,
  Vc<PERSON>utt<PERSON>,
  Vc<PERSON>ab<PERSON>,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import {
  DynamicColumnNames,
  Permission,
} from '@/client/types.gen'
import { WasteInquiryStatus } from '@indaver/types'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTableBulkActions from '@/components/app/table/AppTableBulkActions.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import {
  createAutocompleteFilter,
  createDateRangeFilter,
  createMultiSelectFilter,
} from '@/components/filters/createFilters'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useCustomBooleanFilter } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useEwcFilter } from '@/composables/ewc-filter/ewcFilter.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { useSearch } from '@/composables/search/search.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import { TEST_ID } from '@/constants/testId.constant'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { WasteInquiryStatusEnumUtil } from '@/models/enums/wasteInquiryStatus.enum'
import type { PickUpAddressIndexQueryParams } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.model'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import { WasteInquiryIndexTableTabs } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import type { WasteInquiryIndexQueryParams } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { useWasteInquiryBulkDeleteMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryBulkDelete.mutation'
import { useWasteInquiryCopyMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryCopy.mutation'
import { useWasteInquiryCreateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryCreate.mutation'
import { useWasteInquirySapSummaryDownloadMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquirySapSummaryDownload.mutation'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { usePickUpAddressIndexQuery } from '@/modules/waste-inquiry/api/queries/pickupAddressIndex.query'
import { useWasteInquiryIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteInquiryIndex.query'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'
import WasteInquiryOverviewTableActionsCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableActionsCell.vue'
import WasteInquiryOverviewTableCheckboxCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableCheckboxCell.vue'
import { useWasteInquiryTableColumns } from '@/modules/waste-inquiry/features/overview/composables/wasteInquiryTableColumnsV2.composable'
import { useAuthStore } from '@/stores/auth.store'
import type { DataTableColumn } from '@/types/table.type'
import { DownloadUtil } from '@/utils/download.util'

interface StatusTab {
  id: string
  label: string
  statuses: WasteInquiryStatus[]
}

const authStore = useAuthStore()
const i18n = useI18n()
const router = useRouter()
const toast = useVcToast()
const apiErrorToast = useApiErrorToast()
const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })

const documentTitle = useDocumentTitle()

documentTitle.set(i18n.t('module.waste_inquiry.overview.title'))

const wasteInquiryCopyMutation = useWasteInquiryCopyMutation()
const wasteInquiryCreateMutation = useWasteInquiryCreateMutation()
const wasteInquiryBulkDeleteMutation = useWasteInquiryBulkDeleteMutation()
const wasteInquirySapSummaryDownloadMutation = useWasteInquirySapSummaryDownloadMutation()

const isCopyingId = ref<string | null>(null)
const itemsSelectedInBulk = ref<WasteInquiryUuid[]>([])
const isCreatingWasteInquiry = ref<boolean>(false)
const isDeletingWasteInquiries = ref<boolean>(false)
const downloadingInquiryNumber = ref<string | null>(null)

const statusTabs = computed<StatusTab[]>(() => {
  return [
    {
      id: WasteInquiryIndexTableTabs.SUBMITTED,
      label: i18n.t('module.waste_inquiry.overview.tab.submitted'),
      statuses: [
        WasteInquiryStatus.NEW,
        WasteInquiryStatus.IN_PROGRESS,
        WasteInquiryStatus.SOLUTION_DEFINED,
        WasteInquiryStatus.OFFER_SENT,
        WasteInquiryStatus.OFFER_APPROVED,
        WasteInquiryStatus.COMPLETED,
        WasteInquiryStatus.REJECTED,
        WasteInquiryStatus.CONFORMITY_CONFIRMED,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.DRAFTS,
      label: i18n.t('module.waste_inquiry.overview.tab.drafts'),
      statuses: [
        WasteInquiryStatus.DRAFT,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.PENDING,
      label: i18n.t('module.waste_inquiry.overview.tab.pending'),
      statuses: [
        WasteInquiryStatus.NEW,
        WasteInquiryStatus.IN_PROGRESS,
        WasteInquiryStatus.SOLUTION_DEFINED,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.OFFERS,
      label: i18n.t('module.waste_inquiry.overview.tab.offers'),
      statuses: [
        WasteInquiryStatus.OFFER_SENT,
        WasteInquiryStatus.OFFER_APPROVED,
      ],
    },
    {
      id: WasteInquiryIndexTableTabs.COMPLETED,
      label: i18n.t('module.waste_inquiry.overview.tab.completed'),
      statuses: [
        WasteInquiryStatus.COMPLETED,
        WasteInquiryStatus.REJECTED,
        WasteInquiryStatus.CONFORMITY_CONFIRMED,
      ],
    },
  ]
})

const activeStatusTab = ref<StatusTab>(statusTabs.value[0])

const statusTabValue = computed<string>({
  get: () => activeStatusTab.value.id,
  set: (id: string) => {
    const selectedTab = statusTabs.value.find((tab) => tab.id === id) ?? null

    if (selectedTab !== null) {
      activeStatusTab.value = selectedTab
    }
  },
})

const hasWasteInquiryManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.WASTE_INQUIRY_MANAGE)
})

const globalCustomer = useGlobalCustomer()
const customerIndexSearch = useSearch({ persistInUrl: false })
const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch.debouncedSearch } })

const wasteProducerSearch = ref<string>('')
const wasteProducerIndexFilters = ref<WasteProducerIndexQueryParams['filters']>({})
const wasteProducerIndexQuery = useWasteProducerIndexQuery({
  params: {
    filters: wasteProducerIndexFilters,
    search: wasteProducerSearch,
  },
})

const selectedCustomerForPickUpAddressFilter = ref<WasteInquiryIndexQueryParams['filters']['customerId']>(null)
const effectiveCustomerForPickUpAddressFilter = computed<WasteInquiryIndexQueryParams['filters']['customerId']>(() => {
  if (globalCustomer.globalCustomer.value !== null) {
    return {
      id: globalCustomer.globalCustomer.value.id,
      name: globalCustomer.globalCustomer.value.name,
    }
  }

  return selectedCustomerForPickUpAddressFilter.value
})

const pickUpAddressSearch = useSearch({ persistInUrl: false })
const pickUpAddressFilters = ref<PickUpAddressIndexQueryParams['filters']>({ customerId: null })

interface PickUpAddressFilterOption {
  id: string
  name: string
}

const pickUpAddressIndexQuery = usePickUpAddressIndexQuery({
  params: {
    filters: pickUpAddressFilters,
    search: pickUpAddressSearch.debouncedSearch,
  },
})
const pickUpAddressOptions = computed<PickUpAddressFilterOption[]>(() => {
  if (effectiveCustomerForPickUpAddressFilter.value === null) {
    return []
  }

  return pickUpAddressIndexQuery.data.value.data.map((pickUpAddress) => ({
    id: pickUpAddress.id,
    name: pickUpAddress.name,
  }))
})
const isPickUpAddressLoading = computed<boolean>(() => {
  if (effectiveCustomerForPickUpAddressFilter.value === null) {
    return false
  }

  return pickUpAddressIndexQuery.isFetching.value || pickUpAddressIndexQuery.isLoading.value
})

const wasteProducerOptions = computed<Array<{ id: string
  name: string }>>(() => {
  if (effectiveCustomerForPickUpAddressFilter.value === null) {
    return []
  }

  return wasteProducerIndexQuery.data.value?.data.map((wasteProducer) => ({
    id: wasteProducer.id,
    name: wasteProducer.name,
  })) ?? []
})
const isWasteProducerLoading = computed<boolean>(() => {
  if (effectiveCustomerForPickUpAddressFilter.value === null) {
    return false
  }

  return wasteProducerIndexQuery.isFetching.value || wasteProducerIndexQuery.isLoading.value
})

const conformityCheckFilter = useCustomBooleanFilter('conformityCheck', i18n.t('enum.dynamic_table_column_name.conformity_check'))

const ewcFilter = useEwcFilter()

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        createMultiSelectFilter({
          defaultValue: [],
          displayFn: (status) => i18n.t(WasteInquiryStatusEnumUtil.getI18nKey(status)),
          key: 'statuses',
          keyboardShortcutKeys: [
            's',
          ],
          label: i18n.t('enum.dynamic_table_column_name.status'),
          options: [
            WasteInquiryStatus.NEW,
            WasteInquiryStatus.IN_PROGRESS,
            WasteInquiryStatus.CONFORMITY_CONFIRMED,
            WasteInquiryStatus.SOLUTION_DEFINED,
            WasteInquiryStatus.OFFER_SENT,
            WasteInquiryStatus.OFFER_APPROVED,
            WasteInquiryStatus.COMPLETED,
            WasteInquiryStatus.REJECTED,
          ],
        }),
        createAutocompleteFilter({
          isDisabled: globalCustomer.globalCustomer.value !== null,
          isLoading: customerIndexQuery.isLoading.value,
          defaultValue: null,
          disabledTooltip: globalCustomer.globalCustomer.value !== null
            ? i18n.t('module.pickup_request.overview.filter.customer_disabled_tooltip')
            : null,
          displayFn: (option) => option.name,
          key: 'customerId',
          label: i18n.t('enum.dynamic_table_column_name.customer_name'),
          options: customerIndexQuery.data.value?.data.map((customer) => ({
            id: customer.id,
            name: customer.name,
          })) ?? [],
          onSearch: (value) => {
            if (globalCustomer.globalCustomer.value !== null) {
              return
            }

            customerIndexSearch.updateSearch(value)
          },
        }),
        createAutocompleteFilter({
          isDisabled: effectiveCustomerForPickUpAddressFilter.value === null,
          isLoading: isPickUpAddressLoading.value,
          defaultValue: null,
          disabledTooltip: effectiveCustomerForPickUpAddressFilter.value === null
            ? i18n.t('module.pickup_request.overview.filter.customer_required_tooltip')
            : null,
          displayFn: (option) => option.name,
          emptyState: effectiveCustomerForPickUpAddressFilter.value === null
            ? i18n.t('component.filters.empty_state.pickup_address_requires_customer')
            : null,
          key: 'pickupAddressId',
          label: i18n.t('enum.dynamic_table_column_name.pick_up_address_name'),
          options: pickUpAddressOptions,
          onSearch: (value) => {
            if (effectiveCustomerForPickUpAddressFilter.value === null) {
              return
            }

            pickUpAddressSearch.updateSearch(value)
          },
        }),
        createAutocompleteFilter({
          isDisabled: effectiveCustomerForPickUpAddressFilter.value === null,
          isLoading: isWasteProducerLoading.value,
          defaultValue: null,
          disabledTooltip: effectiveCustomerForPickUpAddressFilter.value === null
            ? i18n.t('module.pickup_request.overview.filter.customer_required_tooltip')
            : null,
          displayFn: (option) => option.name,
          emptyState: effectiveCustomerForPickUpAddressFilter.value === null
            ? i18n.t('component.filters.empty_state.waste_producer_requires_customer')
            : null,
          key: 'wasteProducerId',
          label: i18n.t('enum.dynamic_table_column_name.waste_producer'),
          options: wasteProducerOptions,
          onSearch: (value) => {
            if (effectiveCustomerForPickUpAddressFilter.value === null) {
              return
            }

            wasteProducerSearch.value = value
          },
        }),
        conformityCheckFilter,
        ewcFilter,
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'date',
          label: i18n.t('enum.dynamic_table_column_name.date_of_request'),
        }),
      ],
    },
  ],
  persistInUrl: false,
})

watch(() => filters.values.value.customerId, (customer) => {
  selectedCustomerForPickUpAddressFilter.value = customer
}, { immediate: true })

watch(() => globalCustomer.globalCustomer.value, (customer) => {
  if (customer !== null && filters.values.value.customerId !== null) {
    filters.values.value.customerId = null
  }
}, { immediate: true })

watch(effectiveCustomerForPickUpAddressFilter, (customer, previousCustomer) => {
  if (customer === null) {
    pickUpAddressFilters.value = { customerId: null }
    wasteProducerIndexFilters.value = {}

    if (filters.values.value.pickupAddressId !== null) {
      filters.values.value.pickupAddressId = null
    }

    if (filters.values.value.wasteProducerId !== null) {
      filters.values.value.wasteProducerId = null
    }

    pickUpAddressSearch.clearSearch()
    wasteProducerSearch.value = ''

    return
  }

  pickUpAddressFilters.value = { customerId: customer.id }
  wasteProducerIndexFilters.value = { customerId: customer.id }

  if (previousCustomer === undefined || previousCustomer === null || previousCustomer.id !== customer.id) {
    if (filters.values.value.pickupAddressId !== null) {
      filters.values.value.pickupAddressId = null
    }

    if (filters.values.value.wasteProducerId !== null) {
      filters.values.value.wasteProducerId = null
    }

    pickUpAddressSearch.clearSearch()
    wasteProducerSearch.value = ''
  }
}, { immediate: true })

const searchableColumns = useSearchableTableColumns({
  keys: [
    { key: 'contractId' },
    { key: 'contractItem' },
    { key: 'inquiryNumber' },
    {
      key: 'wasteStreamName',
      match: 'partial',
    },
    {
      key: 'requestorName',
      match: 'partial',
    },
  ],
})

const sort = useSort({
  enableMultiSort: true,
  keys: [
    'contractId',
    'contractItem',
    'customerName',
    'date',
    'inquiryNumber',
    'pickUpAddressId',
    'salesOrganisationId',
    'conformityCheck',
    'wasteProducerId',
    'wasteProducerName',
    'wasteStreamName',
  ],
  persistInUrl: false,
})

const {
  isFetching,
  isLoading,
  data,
  error,
  fetchNextPage,
} = useWasteInquiryIndexQuery({
  params: {
    filters: computed<WasteInquiryIndexQueryParams['filters']>(() => ({
      ...searchableColumns.values.value,
      ...filters.values.value,
      statuses: activeStatusTab.value.id === WasteInquiryIndexTableTabs.SUBMITTED
        ? filters.values.value.statuses
        : activeStatusTab.value.statuses,
    }) as WasteInquiryIndexQueryParams['filters']),
    sort: sort.values,
  },
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.WASTE_INQUIRY,
  filters,
  searchableColumns,
  sort,
})

const wasteInquiryTableColumns = useWasteInquiryTableColumns()

const dynamicColumns = computed<DataTableColumn<WasteInquiryIndex>[]>(() => {
  return dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .filter((column) => {
      // Exclude 'status' column when viewing drafts
      return column.name !== DynamicColumnNames.STATUS || activeStatusTab.value.id !== WasteInquiryIndexTableTabs.DRAFTS
    })
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.STATUS:
          return wasteInquiryTableColumns.status(column.name, column.label)
        case DynamicColumnNames.EWC_CODE:
          return wasteInquiryTableColumns.ewcCode(column.name, column.label)
        case DynamicColumnNames.PICK_UP_ADDRESS_ID:
          return useGenericColumn(column.name, column.label, (row) => row.pickupAddressId)
        case DynamicColumnNames.PICK_UP_ADDRESS_NAME:
          return useGenericColumn(column.name, column.label, (row) => row.pickupAddressName)
        default:
          return useGenericColumn(column.name, column.label)
      }
    })
})

const columns = computed<DataTableColumn<WasteInquiryIndex>[]>(() => {
  const columns = [
    ...dynamicColumns.value,
    {
      cell: (row: WasteInquiryIndex): VNode => h(WasteInquiryOverviewTableActionsCell, {
        isCopying: isCopyingId.value !== null && isCopyingId.value === row.inquiryNumber,
        isDownloading: downloadingInquiryNumber.value === row.inquiryNumber
          && wasteInquirySapSummaryDownloadMutation.isLoading.value,
        hideCopy: row.status === WasteInquiryStatus.DRAFT,
        hideDownload: row.status === WasteInquiryStatus.DRAFT,
        status: row.status,
        onCopy: () => onCopy(row),
        onDownload: () => onDownload(row),
        onEdit: () => onEdit(row),
      }),
      headerLabel: '',
      key: 'actions',
      maxWidth: 'max-content',
    },
  ]

  if (activeStatusTab.value.id === WasteInquiryIndexTableTabs.DRAFTS && hasWasteInquiryManagePermission.value) {
    columns.unshift({
      cell: (row): VNode => h(WasteInquiryOverviewTableCheckboxCell, {
        rowUuid: row.uuid ?? '' as WasteInquiryUuid,
        selectedItems: itemsSelectedInBulk.value,
        onUpdate: (value: boolean) => {
          if (row.uuid === null) {
            return
          }
          if (value) {
            itemsSelectedInBulk.value?.push(row.uuid)
          }
          else {
            itemsSelectedInBulk.value = itemsSelectedInBulk.value.filter((uuid) =>
              uuid !== row.uuid) ?? []
          }
        },
      }),
      headerLabel: '',
      key: 'checkbox',
      width: 'auto',
    })
  }

  return columns
})

function onEdit(wasteInquiry: WasteInquiryIndex): void {
  redirectToDetailView(wasteInquiry)
}

async function onDownload(wasteInquiry: WasteInquiryIndex): Promise<void> {
  const inquiryNumber = wasteInquiry.inquiryNumber

  if (!inquiryNumber) {
    return
  }

  downloadingInquiryNumber.value = inquiryNumber

  try {
    const {
      blob, disposition,
    } = await wasteInquirySapSummaryDownloadMutation.execute({ body: { inquiryNumber } })

    DownloadUtil.downloadBlob(blob, disposition)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    downloadingInquiryNumber.value = null
  }
}

function redirectToDetailView(wasteInquiry: WasteInquiryIndex): void {
  if (wasteInquiry.status === WasteInquiryStatus.DRAFT) {
    if (wasteInquiry.uuid === null) {
      return
    }

    router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: wasteInquiry.uuid },
    })

    return
  }

  if (wasteInquiry.inquiryNumber === null) {
    return
  }

  router.push({
    name: 'waste-inquiry-detail',
    params: { inquiryNumber: wasteInquiry.inquiryNumber },
  })
}

async function onCopy(wasteInquiry: WasteInquiryIndex): Promise<void> {
  if (wasteInquiry.inquiryNumber === null) {
    return
  }
  try {
    isCopyingId.value = wasteInquiry.inquiryNumber

    const response = await wasteInquiryCopyMutation.execute({ body: wasteInquiry.inquiryNumber })

    router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: response },
    })

    toast.success({
      title: i18n.t('shared.copy_success_title'),
      description: i18n.t('shared.copy_success_description'),
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    isCopyingId.value = null
  }
}

async function onNewWasteInquiry(): Promise<void> {
  isCreatingWasteInquiry.value = true

  try {
    const wasteInquiry = await wasteInquiryCreateMutation.execute()

    await router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: wasteInquiry.uuid },
    })
  }
  catch (error) {
    isCreatingWasteInquiry.value = false
    apiErrorToast.show(error)
  }
}

function onDeleteInBulk(uuids: WasteInquiryUuid[]): void {
  isDeletingWasteInquiries.value = true

  confirmDialog.open(
    {
      title: i18n.t('module.waste_inquiry.overview.bulk.delete_draft'),
      isDestructive: true,
      cancelText: i18n.t('shared.cancel'),
      confirmText: i18n.t('shared.delete'),
      description: i18n.t('module.waste_inquiry.overview.bulk.delete_draft_description'),
      onConfirm: async () => {
        try {
          await wasteInquiryBulkDeleteMutation.execute({ params: { wasteInquiryUuids: uuids } })

          isDeletingWasteInquiries.value = false
          itemsSelectedInBulk.value = []
          confirmDialog.close()
        }
        catch (error) {
          apiErrorToast.show(error)
        }
      },
    },
  )
}

function onCancelBulkSelection(): void {
  itemsSelectedInBulk.value = []
}
</script>

<template>
  <AppPage
    :title="i18n.t('module.waste_inquiry.overview.title')"
    class="pb-xl"
  >
    <template #header-actions>
      <VcButton
        v-if="hasWasteInquiryManagePermission"
        :is-loading="isCreatingWasteInquiry"
        :test-id="TEST_ID.WASTE_INQUIRY.OVERVIEW.CREATE_BUTTON"
        icon-left="plus"
        @click="onNewWasteInquiry"
      >
        {{ i18n.t('module.waste_inquiry.overview.new_waste_inquiry') }}
      </VcButton>
    </template>

    <VcTabs
      v-model="statusTabValue"
      :class-config="{
        base: 'pl',
        content: '',
        indicator: 'hidden',
        item: 'border h-9 border-b-0 border-primary rounded-xl rounded-b-none min-w-36 data-[state=inactive]:bg-secondary !m-0 data-[state=active]:text-primary data-[state=inactive]:font-regular enabled:data-[state=active]:hover:bg-transparent',
        list: 'gap-x-0 inline-flex',
        scrollContainer: 'p-xxs -ml-xxs pb-0',
      }"
    >
      <template #items>
        <VcTabsItem
          v-for="(tab, statusIndex) of statusTabs"
          :key="tab.id"
          :value="tab.id"
          :class="{
            '!-ml-px': statusIndex !== 0,
          }"
        >
          {{ tab.label }}
        </VcTabsItem>
      </template>
    </VcTabs>

    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :is-fetching="isFetching"
      :data="data.data"
      :get-key="(index) => `${index}`"
      :columns="columns"
      :sort="sort"
      :error="error"
      :is-first-column-sticky="true"
      :is-last-column-sticky="true"
      :disable-top-left-border-radius="true"
      :searchable-columns="searchableColumns"
      @next-page="fetchNextPage"
    >
      <template
        #top
      >
        <AppTableBulkActions
          :count="itemsSelectedInBulk.length"
          @cancel-bulk-selection="onCancelBulkSelection"
        >
          <template #actions>
            <VcButton
              :is-loading="wasteInquiryBulkDeleteMutation.isLoading.value"
              variant="destructive-tertiary"
              @click="onDeleteInBulk(itemsSelectedInBulk)"
            >
              {{ i18n.t('module.waste_inquiry.overview.bulk.delete_draft') }}
            </VcButton>
          </template>
        </AppTableBulkActions>

        <template v-if="activeStatusTab.id === WasteInquiryIndexTableTabs.SUBMITTED">
          <FiltersRoot :filters="filters">
            <AppGroup
              justify="between"
              class="px-xl h-14"
            >
              <FiltersActive />

              <AppGroup>
                <DynamicTableViews :dynamic-table="dynamicTable" />
                <FiltersDropdownMenu />
                <DynamicTableSettings :dynamic-table="dynamicTable" />
              </AppGroup>
            </AppGroup>
          </FiltersRoot>
        </template>
      </template>
    </DataTable>
  </AppPage>
</template>
