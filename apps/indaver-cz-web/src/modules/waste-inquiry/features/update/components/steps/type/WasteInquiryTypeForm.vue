<script setup lang="ts">
import {
  VcRadioGroup,
  VcSelect,
  VcSelectItem,
  VcTextarea,
  VcTextField,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import {
  StateOfMatter,
  WastePackagingType,
} from '@indaver/types'
import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { StateOfMatterEnumUtil } from '@/models/enums/stateOfMatter.enum'
import { WastePackagingTypeEnumUtil } from '@/models/enums/wastePackagingType.enum'
import type { wasteInquiryTypeFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryTypeForm.model'
import WasteInquiryTypeFormEwcField from '@/modules/waste-inquiry/features/update/components/steps/type/WasteInquiryTypeFormEwcField.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof wasteInquiryTypeFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)

const { form } = wizardFormStep

const name = form.register('name')
const description = form.register('description')
const ewcLevel1 = form.register('ewcLevel1')
const ewcLevel2 = form.register('ewcLevel2')
const ewcLevel3 = form.register('ewcLevel3')
const stateOfMatter = form.register('stateOfMatter')
const packagingType = form.register('packagingType', WastePackagingType.BULK)

const stateOfMatters = Object.values(StateOfMatter)
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormGrid :cols="2">
      <VcTextField
        v-bind="toFormField(name)"
        :is-required="true"
        :is-disabled="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.NAME_TEXT_FIELD"
        :label="i18n.t('module.waste_inquiry.update.type.name.label')"
        :placeholder="i18n.t('module.waste_inquiry.update.type.name.placeholder')"
      />

      <VcTextarea
        v-bind="toFormField(description)"
        :is-required="true"
        :is-disabled="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.DESCRIPTION_TEXT_FIELD"
        :label="i18n.t('module.waste_inquiry.update.type.description.label')"
        :placeholder="i18n.t('module.waste_inquiry.update.type.description.placeholder')"
        :class-config="{
          input: 'h-[8rem]',
        }"
        class="row-span-2 h-full"
      />

      <WasteInquiryTypeFormEwcField
        :is-readonly="props.isReadonly"
        :ewc-level-1-field="ewcLevel1"
        :ewc-level-2-field="ewcLevel2"
        :ewc-level-3-field="ewcLevel3"
      />

      <VcSelect
        v-bind="toFormField(stateOfMatter)"
        :display-fn="(value) => i18n.t(StateOfMatterEnumUtil.getI18nKey(value))"
        :is-required="true"
        :is-disabled="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.STATE_OF_MATTER.SELECT"
        :label="i18n.t('module.waste_inquiry.update.type.state_of_matter.label')"
        :placeholder="i18n.t('module.waste_inquiry.update.type.state_of_matter.placeholder')"
        :filter="{
          isEnabled: true,
        }"
        class="col-span-full"
      >
        <VcSelectItem
          v-for="som of stateOfMatters"
          :key="som"
          :value="som"
        >
          {{ i18n.t(StateOfMatterEnumUtil.getI18nKey(som)) }}
        </VcSelectItem>
      </VcSelect>

      <VcRadioGroup
        :is-required="true"
        :is-disabled="props.isReadonly"
        :label="i18n.t('module.waste_inquiry.update.type.packaging_type.label')"
        v-bind="toFormField(packagingType)"
        class="col-span-full"
      >
        <FormRadioGroupLayout :cols="2">
          <FormRadioGroupItem
            v-for="item of [WastePackagingType.BULK, WastePackagingType.PACKAGED]"
            :key="item"
            :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.PACKAGING_TYPE_RADIO_GROUP_ITEM"
            :value="item"
          >
            <AppCardWithIcon
              :icon="WastePackagingTypeEnumUtil.getIcon(item)"
              :title="i18n.t(WastePackagingTypeEnumUtil.getI18nKey(item))"
              :description="i18n.t(WastePackagingTypeEnumUtil.getDescriptionI18nKey(item))"
              :is-selected="item === packagingType.value.value"
              :is-disabled="props.isReadonly"
              variant="gray-light"
              class="
                inset-ring-brand-500
                group-focus-visible/radio-group-item:inset-ring
              "
            />
          </FormRadioGroupItem>
        </FormRadioGroupLayout>
      </VcRadioGroup>
    </FormGrid>
  </WizardFormStep>
</template>
