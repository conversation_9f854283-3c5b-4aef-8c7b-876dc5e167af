<script setup lang="ts">
import {
  VcTableCell,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import { AnnouncementType } from '@indaver/types'

const props = defineProps<{
  type: AnnouncementType
}>()
const i18n = useI18n()
</script>

<template>
  <VcTableCell class="flex justify-center !pr-0">
    <VcTooltip
      v-if="props.type === AnnouncementType.URGENT"
      popover-side="right"
    >
      <template #trigger>
        <div class="bg-error-solid size-md rounded-full" />
      </template>
      <template #content>
        <p class="py-md px-lg text-xs font-medium">
          {{ i18n.t('module.news.announcement_overview.urgent_announcement') }}
        </p>
      </template>
    </VcTooltip>
  </VcTableCell>
</template>
