<script setup lang="ts">
import type { VcBadgeProps } from '@wisemen/vue-core-components'
import { VcBadge } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { InvoiceStatus } from '@indaver/types'
import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { InvoiceStatusEnumUtil } from '@/models/enums/invoiceStatus.enum'

const props = defineProps<{
  status: InvoiceStatus
}>()

const i18n = useI18n()

const statusBars = [
  [
    InvoiceStatus.OUTSTANDING,
  ],
  [
    InvoiceStatus.OVERDUE,
  ],
  [
    InvoiceStatus.CLEARED,
  ],

]

const statusColor = computed<VcBadgeProps['color']>(() => {
  switch (props.status) {
    case InvoiceStatus.OUTSTANDING:
      return 'purple'
    case InvoiceStatus.CLEARED:
      return 'success'
    case InvoiceStatus.OVERDUE:
      return 'error'
    default:
      return 'gray'
  }
})

function isStatusBarActive(statuses: InvoiceStatus[]): boolean {
  return statuses.includes(props.status)
}
</script>

<template>
  <DataTableCell>
    <AppGroup gap="2xl">
      <div class="gap-x-sm flex items-center">
        <template
          v-for="(statusBarStatus, index) of statusBars"
          :key="index"
        >
          <div
            v-if="!isStatusBarActive(statusBarStatus)"
            class="bg-tertiary h-0.5 w-3 rounded-full"
          />
          <VcBadge
            v-else
            :class-config="{
              root: 'px-sm',
            }"
            :color="statusColor"
            variant="translucent"
            size="sm"
          >
            {{ index + 1 }}
          </VcBadge>
        </template>
      </div>

      <VcBadge
        :color="statusColor"
        variant="translucent"
        size="sm"
      >
        {{ i18n.t(InvoiceStatusEnumUtil.getI18nKey(props.status)) }}
      </VcBadge>
    </AppGroup>
  </DataTableCell>
</template>
