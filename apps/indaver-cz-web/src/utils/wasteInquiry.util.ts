import { WasteInquiryStatus } from '@indaver/types'

export class WasteInquiryUtil {
  static getIconStyleFromStatus(status: WasteInquiryStatus): string {
    switch (status) {
      case WasteInquiryStatus.DRAFT:
        return 'bg-light-blue-25 text-light-blue-700'
      case WasteInquiryStatus.NEW:
        return 'bg-moss-25 text-moss-700'
      case WasteInquiryStatus.IN_PROGRESS:
        return 'bg-purple-25 text-purple-700'
      case WasteInquiryStatus.OFFER_SENT:
        return 'bg-warning-25 text-warning-700'
      case WasteInquiryStatus.OFFER_APPROVED:
        return 'bg-success-25 text-success-700'
      case WasteInquiryStatus.COMPLETED:
        return 'bg-gray-25 text-gray-700'
      case WasteInquiryStatus.REJECTED:
        return 'bg-error-25 text-error-700'
      case WasteInquiryStatus.CONFORMITY_CONFIRMED:
        return 'bg-gray-25 text-gray-700'
      default:
        return 'bg-gray-25 text-gray-700'
    }
  }
}
