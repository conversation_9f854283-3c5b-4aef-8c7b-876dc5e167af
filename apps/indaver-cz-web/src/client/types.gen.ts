// This file is auto-generated by @hey-api/openapi-ts

export enum Permission {
  ALL_PERMISSIONS = 'all_permissions',
  ANNOUNCEMENT_MANAGE = 'announcement.manage',
  CERTIFICATE_MANAGE = 'certificate.manage',
  CERTIFICATE_READ = 'certificate.read',
  CONTACT_MANAGE = 'contact.manage',
  CONTACT_READ = 'contact.read',
  CONTRACT_LINE_MANAGE = 'contract-line.manage',
  CONTRACT_LINE_READ = 'contract-line.read',
  DOCUMENT_BSC = 'document.bsc',
  DOCUMENT_CONTRACT = 'document.contract',
  DOCUMENT_MANUAL = 'document.manual',
  DOCUMENT_MASTER_TABLE = 'document.master-table',
  DOCUMENT_MINUTES_AND_PRESENTATIONS = 'document.minutes-and-presentations',
  DOCUMENT_QUOTATION = 'document.quotation',
  DOCUMENT_TFS = 'document.tfs',
  DOCUMENT_TRANSPORT = 'document.transport',
  DYNAMIC_TABLE_VIEW_MANAGE = 'dynamic-table-view.manage',
  EVENT_LOG_READ = 'event-log.read',
  GUI<PERSON>NCE_LETTER_READ = 'guidance-letter.read',
  INVOICE_MANAGE = 'invoice.manage',
  INVOICE_READ = 'invoice.read',
  JOBS_READ_DETAIL = 'jobs.read.detail',
  JOBS_READ_INDEX = 'jobs.read.index',
  NEWS_ITEM_MANAGE = 'news-item.manage',
  NEWSLETTER_SUBSCRIBE = 'newsletter.subscribe',
  PACKAGING_REQUEST_MANAGE = 'packaging-request.manage',
  PACKAGING_REQUEST_READ = 'packaging-request.read',
  PICK_UP_REQUEST_MANAGE = 'pick-up-request.manage',
  PICK_UP_REQUEST_READ = 'pick-up-request.read',
  POWER_BI_READ = 'power-bi.read',
  ROLE_MANAGE = 'role.manage',
  ROLE_READ = 'role.read',
  SEND_PUSH_NOTIFICATION = 'send_push_notification',
  TYPESENSE = 'typesense',
  USEFUL_LINK_ECMR = 'useful-link.ecmr',
  USEFUL_LINK_INDASCAN = 'useful-link.indascan',
  USEFUL_LINK_PERMITS = 'useful-link.permits',
  USEFUL_LINK_REPORTING = 'useful-link.reporting',
  USER_IMPERSONATE = 'user.impersonate',
  USER_MANAGE = 'user.manage',
  USER_READ = 'user.read',
  WASTE_INQUIRY_MANAGE = 'waste-inquiry.manage',
  WASTE_INQUIRY_READ = 'waste-inquiry.read',
  WEEKLY_PLANNING_REQUEST_MANAGE = 'weekly-planning-request.manage',
  WEEKLY_PLANNING_REQUEST_READ = 'weekly-planning-request.read',
}

export interface ViewRoleDetailResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  isDefault: boolean
  isSystemAdmin: boolean
  name: string
  permissions: Array<Permission>
}

export interface ViewMeResponse {
  uuid: string
  isInternalUser: boolean
  email: string
  firstName: string | null
  lastName: string | null
  roles: Array<ViewRoleDetailResponse>
}

export interface ViewUserDetailResponse {
  uuid: string
  email: string
  firstName: string | null
  lastName: string | null
  roles: Array<ViewRoleDetailResponse>
}

export interface PaginatedOffsetQuery {
  limit: number
  offset: number
}

export interface UserIndexRoleView {
  uuid: string
  isSystemAdmin: boolean
  name: string
}

export interface UserIndexView {
  uuid: string
  email: string
  firstName: string | null
  lastName: string | null
  roles: Array<UserIndexRoleView>
  upn: string
}

export interface PaginatedOffsetResponseMeta {
  /**
   * the amount of items per response
   */
  limit: number
  /**
   * the amount of items skipped
   */
  offset: number
  /**
   * the total amount of items that exist
   */
  total: number
}

export interface ViewUserIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<UserIndexView>
  meta: PaginatedOffsetResponseMeta
}

export interface StartUserImpersonationResponse {
  /**
   * Impersonation token to be used in x-impersonate-user header
   */
  impersonationToken: string
}

export interface ViewPermissionIndexPermissionResponse {
  name: string
  description: string
  key: Permission
}

export interface ViewPermissionIndexGroupResponse {
  name: string
  permissions: Array<ViewPermissionIndexPermissionResponse>
}

export interface ViewPermissionIndexResponse {
  groups: Array<ViewPermissionIndexGroupResponse>
}

export interface CoordinatesResponse {
  latitude: number
  longitude: number
}

export interface AddressResponse {
  /**
   * Generalized street address
   */
  addressLine1: string
  /**
   * Additional address details (apartment, floor, etc.)
   */
  addressLine2: string | null
  coordinates: CoordinatesResponse | null
  /**
   * ISO 3166-1 alpha-2
   */
  countryCode: string | null
  /**
   * City, town or village
   */
  locality: string
  postalCode: string
}

export interface CustomerResponse {
  id: string
  name: string
  address: string | unknown | null
}

export interface ViewCustomerIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<CustomerResponse>
  meta: PaginatedOffsetResponseMeta
}

export enum MimeType {
  APPLICATION_MSWORD = 'application/msword',
  APPLICATION_OCTET_STREAM = 'application/octet-stream',
  APPLICATION_PDF = 'application/pdf',
  APPLICATION_RTF = 'application/rtf',
  APPLICATION_VND_MS_EXCEL = 'application/vnd.ms-excel',
  APPLICATION_VND_MS_OUTLOOK = 'application/vnd.ms-outlook',
  APPLICATION_VND_MS_POWERPOINT = 'application/vnd.ms-powerpoint',
  APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_PRESENTATIONML_PRESENTATION = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_PRESENTATIONML_SLIDESHOW = 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_SPREADSHEETML_SHEET = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  APPLICATION_VND_OPENXMLFORMATS_OFFICEDOCUMENT_WORDPROCESSINGML_DOCUMENT = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  IMAGE_BMP = 'image/bmp',
  IMAGE_GIF = 'image/gif',
  IMAGE_HEIC = 'image/heic',
  IMAGE_JPEG = 'image/jpeg',
  IMAGE_PNG = 'image/png',
  IMAGE_TIFF = 'image/tiff',
  IMAGE_WEBP = 'image/webp',
  TEXT_CSV = 'text/csv',
  TEXT_HTML = 'text/html',
  TEXT_PLAIN = 'text/plain',
}

export interface CreateFileCommand {
  name: string
  mimeType: MimeType
}

export interface CreateFileResponse {
  uuid: string
  name: string
  mimeType: MimeType | null
  uploadUrl: string
}

export enum RequestType {
  PICK_UP = 'pick-up',
  WASTE = 'waste',
}

export interface ViewSuggestedCustomersFilterQuery {
  requestType: RequestType
}

export interface ViewSuggestedCustomersResponse {
  items: Array<CustomerResponse>
}

export interface CustomerNotFoundError {
  code: 'customer_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface ViewCustomerCountryResponse {
  countryCode: string
}

export enum SubjectType {
  ANNOUNCEMENT = 'announcement',
  CONTACT = 'contact',
  DYNAMIC_TABLE_VIEW = 'dynamic-table-view',
  FILE = 'file',
  NEWS_ITEM = 'news-item',
  PACKAGING_REQUEST = 'packaging-request',
  PICK_UP_REQUEST = 'pick-up-request',
  ROLE = 'role',
  USER = 'user',
  WASTE_INQUIRY = 'waste-inquiry',
  WEEKLY_PLANNING_REQUEST = 'weekly-planning-request',
}

export interface ViewDomainEventLogIndexFilterQuery {
  subjectId?: string
  userUuid?: string
  subjectType?: SubjectType
}

export interface ViewDomainEventLogIndexQueryKey {
  uuid: string
  createdAt: string
}

export interface ViewDomainEventLogIndexPaginationQuery {
  key?: ViewDomainEventLogIndexQueryKey | null
  limit: number
}

export interface UserCreatedEventContent {
  userUuid: string
}

export interface UserCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: UserCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'user.created'
  version: number
}

export interface UserUpdatedEventContent {
  userUuid: string
}

export interface UserUpdatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: UserUpdatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'user.updated'
  version: number
}

export interface UserSyncedEventContent {
  userUuid: string
}

export interface UserSyncedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: UserSyncedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'user.synced'
  version: number
}

export interface RoleCreatedEventContent {
  roleUuid: string
  roleName: string
}

export interface RoleCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: RoleCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'role.created'
  version: number
}

export interface RoleDeletedEventContent {
  roleUuid: string
  roleName: string
}

export interface RoleDeletedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: RoleDeletedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'role.deleted'
  version: number
}

export interface RoleRenamedEventContent {
  roleUuid: string
  newName: string
  previousName: string
}

export interface RoleRenamedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: RoleRenamedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'role.renamed'
  version: number
}

export interface RolePermissionsUpdatedEventContent {
  roleUuid: string
  newPermissions: Array<Permission>
  roleName: string
}

export interface RolePermissionsUpdatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: RolePermissionsUpdatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'role.permissions.updated'
  version: number
}

export interface RolePermissionsCacheClearedEventContent {
  roleUuids: string
}

export interface RolePermissionsCacheClearedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: RolePermissionsCacheClearedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'role.permissions.cache.cleared'
  version: number
}

export interface FileCreatedEventContent {
  fileUuid: string
  fileName: string
}

export interface FileCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: FileCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'file.created'
  version: number
}

export interface FileUploadedEventContent {
  fileUuid: string
  fileName: string
}

export interface FileUploadedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: FileUploadedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'file.uploaded'
  version: number
}

export enum NotificationType {
  TEST_NOTIFICATION = 'test-notification',
  USER_CREATED = 'user.created',
}

export interface NotificationCreatedEventContent {
  uuid: string
  type: NotificationType
}

export interface NotificationCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: NotificationCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.created'
  version: number
}

export enum NotificationChannel {
  APP = 'app',
  EMAIL = 'email',
  PUSH = 'push',
  SMS = 'sms',
}

export interface UserNotificationCreatedEventContent {
  notificationUuid: string
  userUuid: string
  channel: NotificationChannel
}

export interface UserNotificationCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: UserNotificationCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'user.notification.created'
  version: number
}

export interface ContactCreatedEventContent {
  contactUuid: string
}

export interface ContactCreatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: ContactCreatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'contact.created'
  version: number
}

export interface ContactUpdatedEventContent {
  contactUuid: string
}

export interface ContactUpdatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: ContactUpdatedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'contact.updated'
  version: number
}

export interface ContactDeletedEventContent {
  contactUuid: string
}

export interface ContactDeletedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: ContactDeletedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'contact.deleted'
  version: number
}

export interface NotificationReadEventContent {
  notificationUuid: string
  userUuid: string
}

export interface NotificationReadDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: NotificationReadEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.read'
  version: number
}

export interface NotificationUnreadEventContent {
  notificationUuid: string
  userUuid: string
}

export interface NotificationUnreadDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: NotificationUnreadEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.unread'
  version: number
}

export enum NotificationPreset {
  ALL = 'all',
  CUSTOM = 'custom',
  DEFAULT = 'default',
  NONE = 'none',
}

export interface NotificationPreferencePresetEventContent {
  userUuid: string
  preset: NotificationPreset
}

export interface NotificationPreferencePresetUpdatedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: NotificationPreferencePresetEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.preference.preset.updated'
  version: number
}

export interface NotificationTypesMigratedEventContent {
  types: Array<NotificationType>
}

export interface NotificationTypesMigratedDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: NotificationTypesMigratedEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.types.migrated'
  version: number
}

export interface TestNotificationSentEventContent {
  message: string
}

export interface TestNotificationSentDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: TestNotificationSentEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'test-notification.sent'
  version: number
}

export interface AllNotificationsMarkedAsReadEventContent {
  userUuid: string
}

export interface NotificationReadAllDomainEventLog {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  content: AllNotificationsMarkedAsReadEventContent
  message: string
  source: string
  subjectType: SubjectType | null
  type: 'notification.read.all'
  version: number
}

export interface DomainEventLogResponse {
  uuid: string
  subjectId: string | null
  userUuid: string | null
  createdAt: string
  message: string
  source: string
  subjectType: SubjectType | null
  version: number
}

export interface ViewDomainEventLogIndexResponseMeta {
  next: ViewDomainEventLogIndexQueryKey | null
}

export interface ViewDomainEventLogIndexResponse {
  items: Array<ContactCreatedDomainEventLog | ContactDeletedDomainEventLog | ContactUpdatedDomainEventLog | FileCreatedDomainEventLog | FileUploadedDomainEventLog | NotificationCreatedDomainEventLog | NotificationPreferencePresetUpdatedDomainEventLog | NotificationReadAllDomainEventLog | NotificationReadDomainEventLog | NotificationTypesMigratedDomainEventLog | NotificationUnreadDomainEventLog | RoleCreatedDomainEventLog | RoleDeletedDomainEventLog | RolePermissionsCacheClearedDomainEventLog | RolePermissionsUpdatedDomainEventLog | RoleRenamedDomainEventLog | TestNotificationSentDomainEventLog | UserCreatedDomainEventLog | UserNotificationCreatedDomainEventLog | UserSyncedDomainEventLog | UserUpdatedDomainEventLog>
  meta: ViewDomainEventLogIndexResponseMeta
}

export enum GlobalSearchCollectionName {
  CONTACT = 'contact',
  USER = 'user',
}

export interface SearchCollectionsFilterQuery {
  collections?: Array<GlobalSearchCollectionName>
}

export interface SearchCollectionUserResponse {
  uuid: string
  email: string
  firstName: string
  lastName: string
}

export interface SearchCollectionContactResponse {
  uuid: string
  email: string
  firstName: string
  lastName: string
}

export interface SearchCollectionsResponseItem {
  collection: GlobalSearchCollectionName
  entity: SearchCollectionContactResponse | SearchCollectionUserResponse
  text_match: number
}

export interface SearchCollectionsResponse {
  items: Array<SearchCollectionsResponseItem>
}

export enum ViewJobsIndexSortQueryKey {
  CREATED_AT = 'createdAt',
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

export interface ViewJobsIndexSortQuery {
  key: ViewJobsIndexSortQueryKey
  order: SortDirection
}

export enum QueueName {
  SYSTEM = 'system',
}

export interface ViewJobsIndexFilterQuery {
  archived?: boolean
  queueNames?: Array<QueueName>
}

export interface ViewJobsIndexQueryKey {
  id: string
  createdAt?: string
}

export interface ViewJobsIndexPaginationQuery {
  key?: ViewJobsIndexQueryKey | null
  limit: number
}

export enum JobStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  CREATED = 'created',
  FAILED = 'failed',
  RETRY = 'retry',
}

export interface ViewJobsIndexItemResponse {
  id: string
  completedAt: string | null
  createdAt: string
  name: string
  queueName: QueueName
  status: JobStatus
}

export interface ViewJobsIndexResponseMeta {
  next: ViewJobsIndexQueryKey | null
}

export interface ViewJobsIndexResponse {
  items: Array<ViewJobsIndexItemResponse>
  meta: ViewJobsIndexResponseMeta
}

export interface ViewJobDetailResponse {
  id: string
  completedAt: string | null
  createdAt: string
  singletonOn: string | null
  startedAt: string | null
  name: string
  data: {
    [key: string]: unknown
  }
  deadLetter: string | null
  expireIn: {
    [key: string]: unknown
  }
  keepUntil: string
  output: {
    [key: string]: unknown
  } | null
  policy: string | null
  priority: number
  queueName: QueueName
  retryBackoff: boolean
  retryCount: number
  retryDelay: number
  retryLimit: number
  singletonKey: string | null
  startAfter: string
  status: JobStatus
}

export interface PreferenceTypes {
  app: Array<NotificationType>
  email: Array<NotificationType>
  push: Array<NotificationType>
  sms: Array<NotificationType>
}

export interface GetMyNotificationPreferencesResponse {
  appEnabled: boolean
  emailEnabled: boolean
  preferences: PreferenceTypes
  preset: NotificationPreset
  pushEnabled: boolean
  smsEnabled: boolean
}

export interface NotificationTypeChannelConfig {
  isSupported: boolean
  channel: NotificationChannel
  defaultValue: boolean
}

export interface NotificationTypeConfig {
  channelConfigs: Array<NotificationTypeChannelConfig>
  type: NotificationType
}

export interface GetNotificationTypesConfigResponse {
  items: Array<NotificationTypeConfig>
}

export interface UpdateMyChannelNotificationPreferenceCommand {
  isEnabled: boolean
  channel: NotificationChannel
}

export interface SendTestNotificationCommand {
  message: string
}

export interface GetMyNotificationsFilterQuery {
  onlyUnread?: string
}

export interface GetMyNotificationsQueryKey {
  notificationUuid: string
  createdAt: string
}

export interface GetMyNotificationsPaginationQuery {
  key?: GetMyNotificationsQueryKey
  limit: number
}

export interface CreatedByUserResponse {
  uuid: string
  name: string
}

export interface TestNotificationContent {
  message: string
}

export interface TestNotificationNotification {
  notificationUuid: string
  createdAt: string
  readAt: string | null
  createdByUser: CreatedByUserResponse | null
  message: string
  type: 'test-notification'
  meta: TestNotificationContent
}

export interface GetMyNotificationsResponseMeta {
  next: GetMyNotificationsQueryKey | null
}

export interface GetMyNotificationsResponse {
  items: Array<TestNotificationNotification>
  meta: GetMyNotificationsResponseMeta
}

export interface ViewUnreadNotificationsCountResponse {
  amount: number
  exceedsLimit: boolean
}

export interface UpdateMyNotificationTypePreferenceCommand {
  isEnabled: boolean
  channel: NotificationChannel
  types: Array<NotificationType>
}

export interface UserNotificationNotFoundError {
  code: 'user_notification_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface UpdateMyNotificationPreferencePresetCommand {
  preset: NotificationPreset
}

export interface ErrorSource {
  pointer: string
}

export interface MigrationAlreadyPerformedErrorMeta {
  type: Array<NotificationType>
}

export interface MigrationAlreadyPerformedError {
  code: 'migration_already_performed'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: MigrationAlreadyPerformedErrorMeta
}

export interface MigrateNotificationTypesCommand {
  types: Array<NotificationType>
}

export interface CreateRoleCommand {
  /**
   * The name of the role
   */
  name: string
}

export interface CreateRoleResponse {
  uuid: string
}

export interface ClearRolePermissionsCacheCommand {
  /**
   * clears the cache for all roles when omitted or null
   */
  roleUuids?: Array<string> | null
}

export interface UpdateRoleCommand {
  /**
   * The name of the role
   */
  name: string
}

export interface RoleResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  isDefault: boolean
  isSystemAdmin: boolean
  name: string
  permissions: Array<Permission>
}

export interface ViewRoleIndexResponse {
  items: Array<RoleResponse>
}

export interface RoleNotFoundError {
  code: 'role_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface UpdateRolesPermissionsCommandItem {
  roleUuid: string
  permissions: Array<Permission>
}

export interface UpdateRolesPermissionsCommand {
  roles: Array<UpdateRolesPermissionsCommandItem>
}

export interface GetApiInfoResponse {
  /**
   * Commit SHA of the current build
   */
  commit: string
  environment: string
  /**
   * Timestamp of the current build
   */
  timestamp: string
  /**
   * Version of the current build
   */
  version: string
}

export enum UiTheme {
  DARK = 'dark',
  LIGHT = 'light',
  SYSTEM = 'system',
}

export enum Locale {
  DE_DE = 'de-DE',
  EN_GB = 'en-GB',
  ES_ES = 'es-ES',
  FR_FR = 'fr-FR',
  NL_BE = 'nl-BE',
}

export enum FontSize {
  DEFAULT = 'default',
  LARGE = 'large',
  LARGER = 'larger',
  SMALL = 'small',
  SMALLER = 'smaller',
}

export interface UpdateUiPreferencesCommand {
  fontSize?: FontSize
  highContrast?: boolean
  language?: Locale
  reduceMotion?: boolean
  showShortcuts?: boolean
  theme?: UiTheme
}

export interface ViewUiPreferencesResponse {
  fontSize: FontSize
  highContrast: boolean
  language: Locale
  reduceMotion: boolean
  showShortcuts: boolean
  theme: UiTheme
}

export enum AnnouncementType {
  INFORMATIONAL = 'informational',
  URGENT = 'urgent',
}

export interface CreateAnnouncementTranslationCommand {
  title: string
  content: {
    [key: string]: unknown
  }
  language: Locale
}

export interface AnnouncementSalesOrganisation {
  id: string
  name: string
}

export interface CreateAnnouncementCommand {
  endDate?: string | null
  startDate: string
  salesOrganisations?: Array<AnnouncementSalesOrganisation>
  translations: Array<CreateAnnouncementTranslationCommand>
  type: AnnouncementType
}

export interface CreateAnnouncementResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface MissingRequiredFieldError {
  code: 'missing_required_field'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface DateMustBeAfterError {
  code: 'date_must_be_after'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface FieldMustBeNullError {
  code: 'field_must_be_null'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NotFoundError {
  code: 'not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface UpdateAnnouncementTranslationCommand {
  title?: string
  content?: {
    [key: string]: unknown
  }
  language: Locale
}

export interface UpdateAnnouncementCommand {
  endDate?: string | null
  startDate?: string
  salesOrganisations?: Array<AnnouncementSalesOrganisation>
  translations?: Array<UpdateAnnouncementTranslationCommand>
  type?: AnnouncementType
}

export interface UpdateAnnouncementResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface ViewAnnouncementTranslationResponse {
  uuid: string
  title: string
  createdAt: string
  updatedAt: string
  content: {
    [key: string]: unknown
  }
  language: Locale
}

export interface DashboardAnnouncementIndexView {
  uuid: string
  endDate: string | null
  startDate: string
  translation: ViewAnnouncementTranslationResponse
  type: AnnouncementType
}

export interface ViewDashboardAnnouncementIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<DashboardAnnouncementIndexView>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewDashboardAnnouncementResponse {
  uuid: string
  endDate: string | null
  startDate: string
  translation: ViewAnnouncementTranslationResponse
  type: AnnouncementType
}

export enum PublishStatus {
  ARCHIVED = 'archived',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
}

export interface ViewNewsItemTranslationResponse {
  uuid: string
  title: string | null
  createdAt: string
  updatedAt: string
  content: {
    [key: string]: unknown
  } | null
  language: Locale
}

export interface ViewNewsItemAuthorResponse {
  uuid: string
  email: string
  firstName: string | null
  lastName: string | null
}

export interface ViewAnnouncementResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string
  updatedAt: string
  author: ViewNewsItemAuthorResponse
  publishStatus: PublishStatus
  salesOrganisations: Array<AnnouncementSalesOrganisation>
  translations: Array<ViewNewsItemTranslationResponse>
  type: AnnouncementType
}

export interface ViewAnnouncementTranslationIndexResponse {
  uuid: string
  title: string
  language: Locale
}

export interface ViewAnnouncementAuthorResponse {
  uuid: string
  email: string
  firstName: string | null
  lastName: string | null
}

export interface AnnouncementIndexView {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string
  updatedAt: string
  author: ViewAnnouncementAuthorResponse
  publishStatus: PublishStatus
  translations: Array<ViewAnnouncementTranslationIndexResponse>
  type: AnnouncementType
}

export interface ViewAnnouncementIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<AnnouncementIndexView>
  meta: PaginatedOffsetResponseMeta
}

export interface CertificateFileNotFoundError {
  code: 'certificate_file_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface CertificateNotFoundError {
  code: 'certificate_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export enum CertificateDocType {
  BLENDING_CONFIRMATION = 'blending-confirmation',
  CERTIFICATE_OF_TREATMENT = 'certificate-of-treatment',
  RECEIPT_CONFIRMATION = 'receipt-confirmation',
  TF_CERTIFICATES = 'tf-certificates',
  TREATMENT_CERTIFICATES = 'treatment-certificates',
}

export interface DownloadCertificateCommand {
  docType: CertificateDocType
  invoiceNumber: string
}

export enum ViewCertificateIndexSortKey {
  COLLECTION_DATE = 'collectionDate',
  CONTRACT = 'contract',
  CONTRACT_ITEM = 'contractItem',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  DELIVERY_DATE = 'deliveryDate',
  DISPOSAL_DATE = 'disposalDate',
  DISPOSITION_DELIVERY_DATE = 'dispositionDeliveryDate',
  DISPOSITION_PICK_UP_DATE = 'dispositionPickUpDate',
  END_TREATMENT_CENTRE = 'endTreatmentCentre',
  EWC = 'ewc',
  INVOICE = 'invoice',
  PICK_UP_ADDRESS = 'pickUpAddress',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  PRINT_DATE = 'printDate',
  SALES_ORDER = 'salesOrder',
  SALES_ORDER_LINE = 'salesOrderLine',
  TFS = 'tfs',
  TREATMENT_CENTRE = 'treatmentCentre',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  WTF_FORM = 'wtfForm',
}

export interface ViewCertificateIndexSortQuery {
  key: ViewCertificateIndexSortKey
  order: SortDirection
}

export interface DateRange {
  from: string
  to: string
}

export interface ViewCertificateIndexFilterQuery {
  customerId?: string
  pickUpAddressId?: string
  wasteProducerId?: string
  collectionDate?: DateRange
  deliveryDate?: DateRange
  disposalDate?: DateRange
  dispositionDeliveryDate?: DateRange
  dispositionPickUpDate?: DateRange
  printDate?: DateRange
  contract?: string
  contractItem?: string
  description?: string
  docTypes?: Array<CertificateDocType>
  endTreatmentCentre?: string
  ewc?: string
  invoice?: string
  salesOrder?: string
  salesOrderLine?: string
  tfs?: string
  treatmentCentre?: string
  wtfForm?: string
}

export interface CertificateIndexResponse {
  customerId: string | null
  pickUpAddressId: string | null
  wasteProducerId: string | null
  collectionDate: string | null
  deliveryDate: string | null
  disposalDate: string | null
  dispositionDeliveryDate: string | null
  dispositionPickUpDate: string | null
  printDate: string | null
  contract: string | null
  contractItem: string | null
  customerName: string | null
  description: string | null
  docType: CertificateDocType | null
  endTreatmentCentre: string | null
  ewcCode: string | null
  fileName: string | null
  invoice: string | null
  pickUpAddressName: string | null
  salesOrder: string | null
  salesOrderLine: string | null
  tfs: string | null
  treatmentCentre: string | null
  wasteProducerName: string | null
  wtfForm: string | null
}

export interface ViewCertificateIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<CertificateIndexResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ContactResponse {
  uuid: string
  email: string
  firstName: string
  lastName: string
}

export interface ViewContactIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<ContactResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface CreateContactCommand {
  email: string
  firstName: string
  lastName: string
}

export interface CreateContactResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface UpdateContactCommand {
  email?: string
  firstName?: string
  lastName?: string
}

export interface ViewContainerTypeIndexFilterQuery {
  customerId: string
}

export interface ContainerTypeResponse {
  id: string
  name: string
}

export interface ViewContainerTypeIndexResponse {
  items: Array<ContainerTypeResponse>
}

export enum ViewContractLineIndexSortQueryKey {
  ASN = 'asn',
  CONTRACT_ITEM = 'contractItem',
  CONTRACT_NUMBER = 'contractNumber',
  END_TREATMENT_CENTER_ID = 'endTreatmentCenterId',
  END_TREATMENT_CENTER_NAME = 'endTreatmentCenterName',
  ESN_NUMBER = 'esnNumber',
  EWC_CODE = 'ewcCode',
  INSTALLATION_NAME = 'installationName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  PROCESS_CODE = 'processCode',
  TC_NUMBER = 'tcNumber',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  WASTE_MATERIAL = 'wasteMaterial',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
}

export interface ViewContractLineIndexSortQuery {
  key: ViewContractLineIndexSortQueryKey
  order: SortDirection
}

export enum ContractLinePackagingType {
  BULK = 'bulk',
  PACKAGED = 'packaged',
}

export interface ViewContractLineIndexFilterQuery {
  customerId?: string
  endTreatmentCenterId?: string
  wasteProducerId?: string
  isHazardous?: boolean
  asn?: string
  contractItem?: string
  contractNumber?: string
  customerReference?: string
  deliveryInfo?: string
  endTreatmentCenterName?: string
  esnNumber?: string
  ewcCode?: string
  installationName?: string
  materialAnalysis?: string
  materialNumber?: string
  packaged?: ContractLinePackagingType
  pickUpAddressIds?: Array<string>
  processCode?: string
  tcNumber?: string
  tfs?: boolean
  treatmentCenterName?: string
  wasteMaterial?: string
}

export interface ContractLineResponse {
  contractLineId: string
  customerId: string | null
  endTreatmentCenterId: string | null
  pickUpAddressId: string | null
  wasteProducerId: string | null
  isHazardous: boolean | null
  asn: string | null
  contractItem: string
  contractNumber: string
  customerName: string | null
  customerReference: string | null
  deliveryInfo: string | null
  endTreatmentCenterName: string | null
  esnNumber: string | null
  ewcCode: string | null
  installationName: string | null
  materialAnalysis: string | null
  materialNumber: string | null
  packaged: ContractLinePackagingType | null
  pickUpAddressName: string | null
  processCode: string | null
  remarks: string | null
  tcNumber: string | null
  tfs: boolean | null
  treatmentCenterName: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
}

export interface ViewContractLineIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<ContractLineResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewWprContractLineIndexFilterQuery {
  customerId: string
  wasteProducerId?: string
  pickUpAddressIds?: Array<string>
}

export interface WprContractLineResponse {
  contractLineId: string
  customerId: string | null
  endTreatmentCenterId: string | null
  pickUpAddressId: string | null
  pickUpRequestUuid: string
  wasteProducerId: string | null
  isHazardous: boolean | null
  asn: string | null
  contractItem: string
  contractNumber: string
  customerName: string | null
  customerReference: string | null
  deliveryInfo: string | null
  endTreatmentCenterName: string | null
  esnNumber: string | null
  ewcCode: string | null
  installationName: string | null
  materialAnalysis: string | null
  materialNumber: string | null
  packaged: string | null
  pickUpAddressName: string | null
  processCode: string | null
  remarks: string | null
  tcNumber: string | null
  tfs: boolean | null
  treatmentCenterName: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
}

export interface ViewWprContractLineIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<WprContractLineResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewPackagingRequestContractLineIndexFilterQuery {
  customerId: string
  wasteProducerId: string
  isSales?: boolean
  deliveryAddressIds?: Array<string>
  wasteMaterial?: string
}

export interface PackagingRequestContractLineResponse {
  contractLineId: string
  isSales: boolean | null
  contractItem: string
  contractNumber: string
  imageUrl: string | null
  materialNumber: string | null
  wasteMaterial: string | null
}

export interface ViewPackagingRequestContractLineIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<PackagingRequestContractLineResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ContractLineNotAccessibleErrorMeta {
  contractLineNumber: string
  contractNumber: string
  tcNumber: string | null
}

export interface ContractLineNotAccessibleError {
  code: 'contract_line_not_accessible'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: ContractLineNotAccessibleErrorMeta
}

export interface ContractLinesSelection {
  contractItem: string
  contractNumber: string
}

export interface ContractLineQuerySelection {
  excludeSelection?: Array<ContractLinesSelection>
  filter: ViewContractLineIndexFilterQuery
}

export interface GenerateContractLinesPdfCommand {
  querySelection?: ContractLineQuerySelection
  selection?: Array<ContractLinesSelection>
}

export interface GenerateContractLinesPdfResponse {
  /**
   * File name
   */
  name: string
  /**
   * Base64 encoded PDF content
   */
  content: string
  /**
   * File mime type
   */
  mimeType: string
  /**
   * File size in bytes
   */
  size: number
}

export interface DocumentNotFoundErrorMeta {
  customerUuid: string
  documentId: string
}

export interface DocumentNotFoundError {
  code: 'document_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: DocumentNotFoundErrorMeta
}

export interface DownloadDocumentCommand {
  customerUuid: string
  documentId: string
}

export interface ForbiddenError {
  code: 'forbidden'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '403'
}

export enum SharepointDocumentViewName {
  BSC = 'bsc',
  CONTRACT = 'contract',
  MANUAL = 'manual',
  MASTERTABLE = 'mastertable',
  MEETINGS = 'meetings',
  QUOTATION = 'quotation',
  TFS = 'tfs',
  TRANSPORT = 'transport',
}

export interface GetDocumentFiltersFilterQuery {
  customerUuid: string
  viewName: SharepointDocumentViewName
  wasteProducerIds: Array<string>
}

export interface DocumentFilterValue {
  /**
   * The internal key of the filter
   */
  key: string
  /**
   * The display value of the filter
   */
  value: string
}

export interface DocumentFilterResponse {
  filterName: string
  filterValues: Array<DocumentFilterValue>
}

export interface GetDocumentFiltersResponse {
  filters: Array<DocumentFilterResponse>
}

export interface ViewDocumentIndexPaginationQuery {
  key?: string | null
  limit: number
}

export enum SharepointDocumentStatus {
  ACTIVE = 'Active',
  APPROVED = 'Approved',
  ARCHIVED = 'Archived',
}

export interface ViewDocumentIndexFilterQuery {
  customerUuid: string
  refExt?: string
  status?: SharepointDocumentStatus
  transportType?: string
  viewName: SharepointDocumentViewName
  wasteProducerIds: Array<string>
  year?: string
}

export enum SharepointDocumentType {
}

export interface ViewDocumentIndexItemResponse {
  id: string
  actionAt: string | null
  name: string
  applicableFrom: string | null
  applicableTill: string | null
  status: SharepointDocumentStatus | null
  tfsType: SharepointDocumentType | null
  wasteProducer: string
}

export interface ViewDocumentIndexResponseMeta {
  next: string | null
}

export interface ViewDocumentIndexResponse {
  items: Array<ViewDocumentIndexItemResponse>
  meta: ViewDocumentIndexResponseMeta
}

export interface ViewUserSiteIndexWasteProducerResponse {
  id: number
  name: string
}

export interface ViewUserSiteIndexResponse {
  uuid: string
  name: string
  wasteProducers: Array<ViewUserSiteIndexWasteProducerResponse>
}

export enum DynamicColumnNames {
  ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
  ACCOUNT_MANAGER = 'accountManager',
  ACCOUNT_MANAGER_NAME = 'accountManagerName',
  ASN = 'asn',
  AUTO_APPROVED_ON = 'autoApprovedOn',
  COLLECTION_DATE = 'collectionDate',
  COMPANY_NAME = 'companyName',
  CONFIRMED_TRANSPORT_DATE = 'confirmedTransportDate',
  CONFORMITY_CHECK = 'conformityCheck',
  CONTAINER_NUMBER = 'containerNumber',
  CONTAINER_TRANSPORT_TYPE = 'containerTransportType',
  CONTAINER_TYPE = 'containerType',
  CONTAINER_VOLUME_SIZE = 'containerVolumeSize',
  CONTRACT = 'contract',
  CONTRACT_ID = 'contractId',
  CONTRACT_ITEM = 'contractItem',
  CONTRACT_NUMBER = 'contractNumber',
  COST_CENTER = 'costCenter',
  CURRENCY = 'currency',
  CUSTOMER_APPROVAL_BY = 'customerApprovalBy',
  CUSTOMER_APPROVAL_DATE = 'customerApprovalDate',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  CUSTOMER_REFERENCE = 'customerReference',
  DANGER_LABEL1 = 'dangerLabel1',
  DANGER_LABEL2 = 'dangerLabel2',
  DANGER_LABEL3 = 'dangerLabel3',
  DATE = 'date',
  DATE_OF_REQUEST = 'dateOfRequest',
  DELIVERY_DATE = 'deliveryDate',
  DELIVERY_INFO = 'deliveryInfo',
  DESCRIPTION = 'description',
  DISPOSAL_CERTIFICATE_NUMBER = 'disposalCertificateNumber',
  DISPOSAL_DATE = 'disposalDate',
  DISPOSITION_DELIVERY_DATE = 'dispositionDeliveryDate',
  DISPOSITION_PICK_UP_DATE = 'dispositionPickUpDate',
  DOC_TYPE = 'docType',
  DUE_ON = 'dueOn',
  END_TREATMENT_CENTER_ID = 'endTreatmentCenterId',
  END_TREATMENT_CENTER_NAME = 'endTreatmentCenterName',
  END_TREATMENT_CENTRE = 'endTreatmentCentre',
  ESN_NUMBER = 'esnNumber',
  ESTIMATED_WEIGHT_OR_VOLUME_UNIT = 'estimatedWeightOrVolumeUnit',
  ESTIMATED_WEIGHT_OR_VOLUME_VALUE = 'estimatedWeightOrVolumeValue',
  EWC = 'ewc',
  EWC_CODE = 'ewcCode',
  FIRST_REMINDER_MAIL_STATUS = 'firstReminderMailStatus',
  FIRST_REMINDER_ON = 'firstReminderOn',
  HAZARD_INDUCERS = 'hazardInducers',
  INQUIRY_NUMBER = 'inquiryNumber',
  INSTALLATION_NAME = 'installationName',
  INVOICE = 'invoice',
  INVOICE_NUMBER = 'invoiceNumber',
  IS_CONTAINER_COVERED = 'isContainerCovered',
  IS_HAZARDOUS = 'isHazardous',
  IS_RETURN_PACKAGING = 'isReturnPackaging',
  IS_TRANSPORT_BY_INDAVER = 'isTransportByIndaver',
  ISSUED_ON = 'issuedOn',
  MATERIAL_ANALYSIS = 'materialAnalysis',
  MATERIAL_NUMBER = 'materialNumber',
  MATERIAL_TYPE = 'materialType',
  NAME_INSTALLATION = 'nameInstallation',
  NAME_OF_APPLICANT = 'nameOfApplicant',
  NET_AMOUNT = 'netAmount',
  ORDER_NUMBER = 'orderNumber',
  PACKAGED = 'packaged',
  PACKAGING_INDICATOR = 'packagingIndicator',
  PACKAGING_REMARK = 'packagingRemark',
  PACKAGING_TYPE = 'packagingType',
  PACKING_GROUP = 'packingGroup',
  PAYER_ID = 'payerId',
  PAYER_NAME = 'payerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  PO_NUMBER = 'poNumber',
  PRINT_DATE = 'printDate',
  PROCESS_CODE = 'processCode',
  QUANTITY_CONTAINERS = 'quantityContainers',
  QUANTITY_LABELS = 'quantityLabels',
  QUANTITY_PACKAGES = 'quantityPackages',
  QUANTITY_PALLETS = 'quantityPallets',
  RECONCILIATION_NUMBER = 'reconciliationNumber',
  REMARKS = 'remarks',
  REQUEST_NUMBER = 'requestNumber',
  REQUESTED_END_DATE = 'requestedEndDate',
  REQUESTED_START_DATE = 'requestedStartDate',
  REQUESTOR_NAME = 'requestorName',
  SALES_DOC = 'salesDoc',
  SALES_ORDER = 'salesOrder',
  SALES_ORDER_LINE = 'salesOrderLine',
  SALES_ORGANISATION_ID = 'salesOrganisationId',
  SALES_ORGANISATION_NAME = 'salesOrganisationName',
  SECOND_REMINDER_MAIL_STATUS = 'secondReminderMailStatus',
  SECOND_REMINDER_ON = 'secondReminderOn',
  SERIAL_NUMBER = 'serialNumber',
  SHIPMENT_ID = 'shipmentId',
  STATUS = 'status',
  TANKER_TYPE = 'tankerType',
  TC_NUMBER = 'tcNumber',
  TFS = 'tfs',
  TFS_NUMBER = 'tfsNumber',
  THIRD_REMINDER_MAIL_STATUS = 'thirdReminderMailStatus',
  TOTAL_QUANTITY_PALLETS = 'totalQuantityPallets',
  TRANSPORT_DATE = 'transportDate',
  TRANSPORT_MODE = 'transportMode',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  TREATMENT_CENTRE = 'treatmentCentre',
  TYPE = 'type',
  UN_NUMBER = 'unNumber',
  UNIT = 'unit',
  VAT_AMOUNT = 'vatAmount',
  WASTE_MATERIAL = 'wasteMaterial',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  WASTE_STREAM_NAME = 'wasteStreamName',
  WEIGHT_OR_VOLUME = 'weightOrVolume',
  WTF_FORM = 'wtfForm',
}

export interface DynamicTableColumnIndexView {
  uuid: string
  createdAt: string
  updatedAt: string
  isHidable: boolean
  name: DynamicColumnNames
  applicableFields: Array<string>
  filterableField: string | null
  searchableFields: Array<string>
  sortableFields: Array<string>
}

export interface DynamicTableIndexColumnResponse {
  items: Array<DynamicTableColumnIndexView>
}

export interface ColumnNotFoundError {
  code: 'column_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface ColumnNotFilterableError {
  code: 'column_not_filterable'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface ColumnNotSortableError {
  code: 'column_not_sortable'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface DuplicateColumnError {
  code: 'duplicate_column'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface DynamicTableViewFilterCommand {
  columnUuid: string
  value: boolean | string | Array<string> | Array<{
    key?: string
    value?: string
  }> | {
    key?: string
    value?: string
  }
}

export interface DynamicTableViewSortCommand {
  columnUuid: string
  direction: SortDirection
}

export interface DynamicTableViewVisibleColumnsCommand {
  columnUuid: string
}

export interface CreateDynamicTableViewCommand {
  isDefault?: boolean
  /**
   * Field only allowed for admins
   */
  isGlobal?: boolean
  /**
   * Field only allowed for admins
   */
  isGlobalDefault?: boolean
  filters: Array<DynamicTableViewFilterCommand>
  sorts: Array<DynamicTableViewSortCommand>
  viewName: string
  visibleColumns: Array<DynamicTableViewVisibleColumnsCommand>
}

export interface CreateDynamicTableViewResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface VisibilityConfigurationResponse {
  uuid: string
  order: number
}

export interface SortConfigurationResponse {
  uuid: string
  direction: SortDirection
  order: number
}

export interface FilterConfigurationResponse {
  uuid: string
  value: boolean | string | Array<string> | Array<{
    key?: string
    value?: string
  }> | {
    key?: string
    value?: string
  }
}

export interface ViewDefaultDynamicTableViewResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  isDefaultGlobal: boolean
  isGlobal: boolean
  isUserDefault: boolean
  name: string
  filters: Array<FilterConfigurationResponse>
  sorts: Array<SortConfigurationResponse>
  visibleColumns: Array<VisibilityConfigurationResponse>
}

export interface UpdateDynamicTableViewCommand {
  isDefault?: boolean
  isGlobal?: boolean
  isGlobalDefault?: boolean
  filters?: Array<DynamicTableViewFilterCommand>
  sorts?: Array<DynamicTableViewSortCommand>
  viewName?: string
  visibleColumns?: Array<DynamicTableViewVisibleColumnsCommand>
}

export interface UpdateDynamicTableViewResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface DynamicTableViewResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  isDefaultGlobal: boolean
  isGlobal: boolean
  isUserDefault: boolean
  name: string
  filters: Array<FilterConfigurationResponse>
  sorts: Array<SortConfigurationResponse>
  visibleColumns: Array<VisibilityConfigurationResponse>
}

export interface DynamicTableViewIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<DynamicTableViewResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface GlobalDefaultViewNotDeletable {
  code: 'global_default_view_not_deletable'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface LastGlobalViewNotDeletable {
  code: 'last_global_view_not_deletable'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface EwcCodeResponse {
  /**
   * Code existing out of 1, 2 or 3 levels
   */
  code: string
  description: string
}

export interface ViewEwcCodeIndexResponse {
  items: Array<EwcCodeResponse>
}

export enum DownloadGuidanceLetterType {
  ATTACHMENT = 'attachment',
  PREVIEW = 'preview',
  PRINT = 'print',
}

export interface GuidanceLetterDownloadTypeNotFoundErrorMeta {
  type: DownloadGuidanceLetterType
}

export interface GuidanceLetterDownloadTypeNotFoundError {
  code: 'guidance_letter_download_type_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: GuidanceLetterDownloadTypeNotFoundErrorMeta
}

export interface GuidanceLetterNotFoundErrorMeta {
  id: string
}

export interface GuidanceLetterNotFoundError {
  code: 'guidance_letter_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: GuidanceLetterNotFoundErrorMeta
}

export enum ViewGuidanceLetterIndexSortQueryKey {
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  PICK_UP_ADDRESS_NAME = 'pickUpAddressName',
  REQUEST_NUMBER = 'requestNumber',
  SALES_DOC = 'salesDoc',
  SHIPMENT_ID = 'shipmentId',
  TRANSPORT_DATE = 'transportDate',
  WASTE_MATERIAL = 'wasteMaterial',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
}

export interface ViewGuidanceLetterIndexSortQuery {
  key: ViewGuidanceLetterIndexSortQueryKey
  order: SortDirection
}

export interface ViewGuidanceLetterIndexFilterQuery {
  customerId?: string
  pickUpAddressId?: string
  shipmentId?: string
  wasteProducerId?: string
  transportDate?: DateRange
  requestNumber?: string
  salesDoc?: string
  wasteMaterial?: string
}

export interface GuidanceLetterResponse {
  customerId: string | null
  pickUpAddressId: string | null
  shipmentId: string
  wasteProducerId: string | null
  transportDate: string | null
  attachment: boolean
  customerName: string | null
  guidanceLetter: boolean
  pickUpAddressName: string | null
  requestNumber: string | null
  salesDoc: string
  unit: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
  weightOrVolume: number | null
}

export interface ViewGuidanceLetterIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<GuidanceLetterResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface DownloadInvoiceCertificateCommand {
  fileName: string
}

export interface InvoiceDocumentNotFoundErrorMeta {
  invoiceNumber: string
}

export interface InvoiceDocumentNotFoundError {
  code: 'invoice_document_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: InvoiceDocumentNotFoundErrorMeta
}

export interface InvoiceNotFoundErrorMeta {
  invoiceNumber: string
}

export interface InvoiceNotFoundError {
  code: 'invoice_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: InvoiceNotFoundErrorMeta
}

export enum ViewInvoiceIndexSortKey {
  ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
  COMPANY_NAME = 'companyName',
  CUSTOMER_ID = 'customerId',
  CUSTOMER_NAME = 'customerName',
  CUSTOMER_REFERENCE = 'customerReference',
  DUE_DATE = 'dueDate',
  INVOICE_NUMBER = 'invoiceNumber',
  ISSUE_DATE = 'issueDate',
  NET_AMOUNT = 'netAmount',
  PAYER_ID = 'payerId',
  PAYER_NAME = 'payerName',
}

export interface ViewInvoiceIndexSortQuery {
  key: ViewInvoiceIndexSortKey
  order: SortDirection
}

export enum InvoiceStatus {
  CLEARED = 'cleared',
  OUTSTANDING = 'outstanding',
  OVERDUE = 'overdue',
}

export enum InvoiceColumnName {
  ACCOUNT_DOCUMENT_NUMBER = 'accountDocumentNumber',
  ACCOUNT_MANAGER_NAME = 'accountManagerName',
  COMPANY_NAME = 'companyName',
  CURRENCY = 'currency',
  CUSTOMER_NAME = 'customerName',
  CUSTOMER_REFERENCE = 'customerReference',
  DUE_ON = 'dueOn',
  INVOICE_NUMBER = 'invoiceNumber',
  ISSUED_ON = 'issuedOn',
  NET_AMOUNT = 'netAmount',
  PAYER_ID = 'payerId',
  PAYER_NAME = 'payerName',
  STATUS = 'status',
  TYPE = 'type',
  VAT_AMOUNT = 'vatAmount',
}

export interface ExportInvoicesExcelFilterQuery {
  columns: Array<InvoiceColumnName>
  statuses: Array<InvoiceStatus>
  /**
   * Translated column names for Excel header
   */
  translatedColumns: Array<string>
}

export enum InvoiceFilterType {
  CREDIT_MEMO = 'credit_memo',
  DEBIT_MEMO = 'debit_memo',
  INVOICE = 'invoice',
}

export interface ViewInvoiceIndexFilterQuery {
  customerId?: string
  payerId?: string
  dueDate?: DateRange
  issueDate?: DateRange
  accountDocumentNumber?: string
  accountManagerName?: string
  companyName?: string
  customerReference?: string
  invoiceNumber?: string
  statuses: Array<InvoiceStatus>
  type?: InvoiceFilterType
}

export enum InvoiceType {
  CREDIT_MEMO = 'credit_memo',
  DEBIT_MEMO = 'debit_memo',
  INVOICE = 'invoice',
  UNKNOWN = 'unknown',
}

export interface InvoiceResponse {
  customerId: string
  payerId: string
  dueOn: string | null
  issuedOn: string
  accountDocumentNumber: string | null
  accountManagerName: string | null
  companyName: string
  currency: string
  customerName: string
  customerReference: string | null
  invoiceNumber: string
  netAmount: string
  payerName: string
  status: InvoiceStatus
  type: InvoiceType
  vatAmount: string
}

export interface ViewInvoiceIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<InvoiceResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewInvoiceResponse {
  payerId: string
  dueOn: string | null
  issuedOn: string
  accountDocumentNumber: string | null
  accountManagerName: string | null
  companyName: string
  currency: string
  customerName: string
  customerReference: string | null
  invoiceNumber: string
  netAmount: string
  payerName: string
  status: InvoiceStatus
  type: InvoiceType
  vatAmount: string
}

export enum DraftInvoiceStatus {
  APPROVED_BY_CUSTOMER = 'approved_by_customer',
  AUTO_APPROVED = 'auto_approved',
  INTERNAL_APPROVED = 'internal_approved',
  REJECTED_BY_CUSTOMER = 'rejected_by_customer',
  REJECTED_BY_INDAVER = 'rejected_by_indaver',
  TO_BE_APPROVED_BY_CUSTOMER = 'to_be_approved_by_customer',
  TO_BE_APPROVED_BY_INDAVER = 'to_be_approved_by_indaver',
}

export enum MailStatus {
  NOT_SENT = 'not_sent',
  SENT = 'sent',
}

export interface ViewDraftInvoiceDetailResponse {
  customerId: string
  payerId: string
  autoApprovedOn: string | null
  customerApprovalDate: string | null
  firstReminderOn: string | null
  issuedOn: string
  secondReminderOn: string | null
  accountDocumentNumber: string | null
  currency: string
  customerApprovalBy: string | null
  firstReminderMailStatus: MailStatus
  invoiceNumber: string
  netAmount: string
  payerName: string
  poNumber: string | null
  secondReminderMailStatus: MailStatus
  status: DraftInvoiceStatus
  thirdReminderMailStatus: MailStatus
  vatAmount: string
}

export enum ViewDraftInvoiceIndexSortQueryKey {
  INVOICE_NUMBER = 'invoiceNumber',
}

export interface ViewDraftInvoiceIndexSortQuery {
  key: ViewDraftInvoiceIndexSortQueryKey
  order: SortDirection
}

export enum DraftInvoiceFilterStatus {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  TO_BE_APPROVED = 'to_be_approved',
}

export interface ViewDraftInvoiceIndexFilterQuery {
  customerId?: string
  payerId?: string
  issuedOn?: DateRange
  invoiceNumber?: string
  statuses: Array<DraftInvoiceFilterStatus>
}

export interface DraftInvoiceResponse {
  customerId: string
  payerId: string
  autoApprovedOn: string | null
  customerApprovalDate: string | null
  firstReminderOn: string | null
  issuedOn: string
  secondReminderOn: string | null
  accountDocumentNumber: string | null
  currency: string
  customerApprovalBy: string | null
  firstReminderMailStatus: MailStatus
  invoiceNumber: string
  netAmount: string
  payerName: string
  poNumber: string | null
  secondReminderMailStatus: MailStatus
  status: DraftInvoiceStatus
  thirdReminderMailStatus: MailStatus
  vatAmount: string
}

export interface ViewDraftInvoiceIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<DraftInvoiceResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ExportDraftInvoicesExcelFilterQuery {
  columns: Array<InvoiceColumnName>
  statuses: Array<DraftInvoiceFilterStatus>
  /**
   * Translated column names for CSV header
   */
  translatedColumns: Array<string>
}

export interface NonApproveOrRejectableDraftInvoiceError {
  code: 'non_approve_or_rejectable_draft_invoice'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface ApproveDraftInvoiceCommand {
  poNumber: string | null
  remark: string | null
}

export interface RejectDraftInvoiceCommand {
  remark: string
}

export interface AlreadySubscribedError {
  code: 'already_subscribed'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface OptInAlreadyRequestedError {
  code: 'opt_in_already_requested'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface SubscribeToNewsletterCommand {
  email: string
}

export interface CreateNewsItemTranslationCommand {
  title: string
  content: {
    [key: string]: unknown
  }
  language: Locale
}

export interface NewsItemSalesOrganisation {
  id: string
  name: string
}

export interface CreateNewsItemCommand {
  imageUuid: string
  endDate?: string | null
  startDate: string
  newsItemTranslations: Array<CreateNewsItemTranslationCommand>
  salesOrganisations: Array<NewsItemSalesOrganisation>
  videoUrl?: string | null
}

export interface CreateNewsItemResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface NewsItemTranslationExistsError {
  code: 'news-item-translation-exists'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NoStartDateOrEndDateExpectedError {
  code: 'no-start-date-or-end-date-expected'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface UpdateNewsItemTranslationCommand {
  title?: string
  content?: {
    [key: string]: unknown
  }
  language: Locale
}

export interface UpdateNewsItemCommand {
  imageUuid?: string
  endDate?: string | null
  startDate?: string
  newsItemTranslations?: Array<UpdateNewsItemTranslationCommand>
  salesOrganisations?: Array<NewsItemSalesOrganisation>
  videoUrl?: string | null
}

export interface UpdateNewsItemResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface NewsItemNotFoundErrorMeta {
  uuid: string
}

export interface NewsItemNotFoundError {
  code: 'news_item_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: NewsItemNotFoundErrorMeta
}

export interface FileResponse {
  uuid: string
  name: string
  mimeType: MimeType
  url: string
}

export interface ViewNewsItemResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string
  updatedAt: string
  author: ViewNewsItemAuthorResponse
  image: FileResponse
  publishStatus: PublishStatus
  salesOrganisations: Array<NewsItemSalesOrganisation>
  translations: Array<ViewNewsItemTranslationResponse>
  videoUrl: string | null
}

export interface ViewNewsItemTranslationIndexResponse {
  uuid: string
  title: string | null
  language: Locale
}

export interface NewsItemIndexView {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string
  updatedAt: string
  author: ViewNewsItemAuthorResponse
  publishStatus: PublishStatus
  translations: Array<ViewNewsItemTranslationIndexResponse>
}

export interface ViewNewsIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<NewsItemIndexView>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewDashboardNewItemFilterQuery {
  excludeNewsItemUuids?: Array<string>
}

export interface DashboardNewsItemIndexView {
  uuid: string
  endDate: string | null
  startDate: string | null
  image: FileResponse | null
  translation: ViewNewsItemTranslationResponse
  videoUrl: string | null
}

export interface ViewDashboardNewsIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<DashboardNewsItemIndexView>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewDashboardNewsItemResponse {
  uuid: string
  endDate: string | null
  startDate: string | null
  image: FileResponse | null
  translation: ViewNewsItemTranslationResponse
  videoUrl: string | null
}

export interface CreatePackagingRequestCommand {
  [key: string]: unknown
}

export interface CreatePackagingRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface WasteProducerResponse {
  id: string
  name: string
  address: string | unknown | null
}

export interface PickUpAddressResponse {
  id: string
  name: string
  address: string | unknown | null
}

export interface PackagingRequestMaterialResponse {
  contractLineId: string
  isSales: boolean | null
  contractItem: string
  /**
   * When false, the contract line could not be retrieved from SAP. This occurs when the contract line is expired or rejected.
   */
  contractLineAccessible?: boolean
  contractNumber: string
  costCenter: string | null
  materialNumber: string | null
  poNumber: string | null
  quantity: number
  wasteMaterial: string | null
}

export interface ContactTypeResponse {
  email: string
  firstName: string
  lastName: string
}

export interface ViewPackagingRequestResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string | null
  updatedAt: string
  customer: CustomerResponse | null
  deliveryAddress: PickUpAddressResponse | null
  packagingRequestMaterials: Array<PackagingRequestMaterialResponse>
  remarks: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  wasteProducer: WasteProducerResponse | null
}

export interface ViewWasteProducerIndexFilterQuery {
  /**
   * Optional for internal users
   */
  customerId?: string
}

export interface ViewWasteProducerIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<WasteProducerResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewSuggestedWasteProducersFilterQuery {
  customerId: string
  requestType: RequestType
}

export interface ViewSuggestedWasteProducersResponse {
  items: Array<WasteProducerResponse>
}

export interface ViewPickUpAddressIndexFilterQuery {
  /**
   * Optional for internal users
   */
  customerId?: string
}

export interface ViewPickUpAddressIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<PickUpAddressResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewSuggestedPickUpAddressesFilterQuery {
  customerId: string
  requestType: RequestType
}

export interface ViewSuggestedPickUpAddressesResponse {
  items: Array<PickUpAddressResponse>
}

export interface CustomerNotAccessibleError {
  code: 'customer_not_accessible'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface WasteProducerNotAccessibleError {
  code: 'waste_producer_not_accessible'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface PickUpAddressNotAccessibleError {
  code: 'pick_up_address_not_accessible'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface PackagingRequestMaterialCommand {
  contractLineId: string
  isSales: boolean | null
  contractItem: string
  contractNumber: string
  costCenter: string | null
  materialNumber: string
  poNumber: string | null
  quantity: number
  wasteMaterial: string | null
}

export interface Contact {
  email: string
  firstName: string
  lastName: string
}

export interface UpdatePackagingRequestCommand {
  customerId?: string | null
  deliveryAddressId?: string | null
  wasteProducerId?: string | null
  endDate?: string | null
  startDate?: string | null
  packagingRequestMaterials?: Array<PackagingRequestMaterialCommand>
  remarks?: string | null
  sendCopyToContacts?: Array<Contact>
}

export interface UpdatePackagingRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface ContractLineNotOfCustomerErrorMeta {
  customerId: string
  contractLineNumber: string
  contractNumber: string
  tcNumber: string | null
}

export interface ContractLineNotOfCustomerError {
  code: 'contract_line_not_of_customer'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: ContractLineNotOfCustomerErrorMeta
}

export interface ContractLineNotOfPickUpAddressesErrorMeta {
  contractLineNumber: string
  contractNumber: string
  pickUpAddressIds: Array<string>
  tcNumber: string | null
}

export interface ContractLineNotOfPickUpAddressesError {
  code: 'contract_line_not_of_pick_up_addresses'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: ContractLineNotOfPickUpAddressesErrorMeta
}

export interface UpdateOnlyPackagingRequestError {
  code: 'update_only_packaging_request'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface PackagingRequestAlreadySubmitted {
  code: 'packaging_request_already_submitted'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface SubmitPackagingRequestResponse {
  uuid: string
  createdAt: string
  submittedOn: string
  updatedAt: string
  requestNumber: string
}

export interface BulkDeletePackagingRequestCommand {
  packagingRequestUuids: Array<string>
}

export interface PickUpRequestNotFoundErrorMeta {
  uuid?: string
  requestNumber?: string
}

export interface PickUpRequestNotFoundError {
  code: 'pick_up_request_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: PickUpRequestNotFoundErrorMeta
}

export interface CopyNonSubmittedPickUpRequestError {
  code: 'copy_non_submitted_pick_up_request'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface CopyPackagingRequestSapResponse {
  uuid: string
}

export interface ViewPackagingTypeIndexFilterQuery {
  customerId: string
}

export interface PackagingTypeResponse {
  id: string
  name: string
}

export interface ViewPackagingTypeIndexResponse {
  items: Array<PackagingTypeResponse>
}

export interface ViewPayerIndexFilterQuery {
  customerId: string
}

export interface PayerResponse {
  id: string
  name: string
}

export interface ViewPayerIndexResponse {
  items: Array<PayerResponse>
}

export interface GetIsPoNumberAndCostCenterRequiredFilterQuery {
  customerId: string
}

export interface GetIsPoNumberAndCostCenterRequiredResponse {
  isCostCenterRequired: boolean
  isPoNumberRequired: boolean
}

export enum ViewPickUpRequestIndexSortKey {
  CONFIRMED_TRANSPORT_DATE = 'confirmedTransportDate',
  CONTAINER_NUMBER = 'containerNumber',
  CONTRACT_ITEM = 'contractItem',
  CONTRACT_NUMBER = 'contractNumber',
  CUSTOMER_REFERENCE = 'customerReference',
  DATE_OF_REQUEST = 'dateOfRequest',
  DISPOSAL_CERTIFICATE_NUMBER = 'disposalCertificateNumber',
  INSTALLATION_NAME = 'installationName',
  ORDER_NUMBER = 'orderNumber',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  REQUEST_NUMBER = 'requestNumber',
  REQUESTED_START_DATE = 'requestedStartDate',
  SALES_ORDER = 'salesOrder',
  TREATMENT_CENTER_NAME = 'treatmentCenterName',
  WASTE_MATERIAL = 'wasteMaterial',
  WASTE_PRODUCER_ID = 'wasteProducerId',
}

export interface ViewPickUpRequestIndexSortQuery {
  key: ViewPickUpRequestIndexSortKey
  order: SortDirection
}

export enum PickUpRequestStatus {
  CANCELLED = 'cancelled',
  CONFIRMED = 'confirmed',
  DRAFT = 'draft',
  INDASCAN_DRAFT = 'indascan_draft',
  PENDING = 'pending',
}

export enum TransportMode {
  BULK_ISO_TANK = 'bulk-iso-tank',
  BULK_SKIPS_CONTAINER = 'bulk-skips-container',
  BULK_VACUUM_TANKERS_ROAD_TANKERS = 'bulk-vacuum-tankers-road-tankers',
  PACKAGED_CURTAIN_SIDER_TRUCK = 'packaged-curtain-sider-truck',
  PACKAGING_REQUEST_ORDER = 'packaging-request-order',
}

export interface ViewPickUpRequestIndexFilterQuery {
  customerId?: string
  pickUpAddressId?: string
  wasteProducerId?: string
  confirmedTransportDate?: DateRange
  requestDate?: DateRange
  isHazardous?: boolean
  isTransportByIndaver?: boolean
  accountManager?: string
  containerNumber?: string
  contractItem?: string
  contractNumber?: string
  costCenter?: string
  customerReference?: string
  dateOfRequest?: DateRange
  deliveryInfo?: string
  disposalCertificateNumber?: string
  ewc?: string
  materialAnalysis?: string
  nameInstallation?: string
  nameOfApplicant?: string
  orderNumber?: string
  requestNumber?: string
  salesOrder?: string
  statuses: Array<PickUpRequestStatus>
  transportMode?: TransportMode
  treatmentCenterName?: string
  wasteMaterial?: string
}

export interface PickUpRequestResponse {
  /**
   * UUID is null when retrieved from SAP
   */
  uuid: string | null
  customerId: string | null
  pickUpAddressId: Array<string>
  wasteProducerId: string | null
  confirmedTransportDate: string | null
  /**
   * Only for drafts
   */
  createdAt: string | null
  requestedEndDate: string | null
  requestedStartDate: string | null
  isHazardous: Array<boolean>
  isTransportByIndaver: boolean | null
  accountManager: string | null
  containerNumber: Array<string>
  contractItem: Array<string>
  contractNumber: Array<string>
  costCenter: Array<string>
  customerName: string | null
  customerReference: string | null
  dateOfRequest: string | null
  deliveryInfo: string | null
  disposalCertificateNumber: string | null
  ewc: string | null
  materialAnalysis: string | null
  nameInstallation: string | null
  nameOfApplicant: string | null
  orderNumber: string | null
  pickUpAddressName: Array<string>
  requestNumber: string | null
  salesOrder: string | null
  status: PickUpRequestStatus
  tfsNumber: string | null
  transportMode: TransportMode | null
  treatmentCenterName: string | null
  wasteMaterial: Array<string>
  wasteProducerName: string | null
}

export interface ViewPickUpRequestIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<PickUpRequestResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface CreatePickUpRequestCommand {
  [key: string]: unknown
}

export interface CreatePickUpRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export enum PickUpTransportMode {
  BULK_ISO_TANK = 'bulk-iso-tank',
  BULK_SKIPS_CONTAINER = 'bulk-skips-container',
  BULK_VACUUM_TANKERS_ROAD_TANKERS = 'bulk-vacuum-tankers-road-tankers',
  PACKAGED_CURTAIN_SIDER_TRUCK = 'packaged-curtain-sider-truck',
}

export enum WasteMeasurementUnit {
  KG = 'kg',
  M3 = 'm3',
  PC = 'pc',
  TO = 'to',
  YD3 = 'yd3',
}

export interface MaterialResponse {
  contractLineId: string
  customerId: string | null
  endTreatmentCenterId: string | null
  pickUpAddressId: string | null
  wasteProducerId: string | null
  isContainerCovered: boolean | null
  isHazardous: boolean | null
  adrClass: string | null
  asn: string | null
  containerNumber: string | null
  containerTransportType: string | null
  containerType: string | null
  containerVolumeSize: string | null
  contractItem: string
  /**
   * When false, the contract line could not be retrieved from SAP. This occurs when the contract line is expired or rejected.
   */
  contractLineAccessible: boolean | null
  contractNumber: string
  costCenter: string | null
  customerName: string | null
  customerReference: string | null
  dangerLabel1: string | null
  dangerLabel2: string | null
  dangerLabel3: string | null
  deliveryInfo: string | null
  endTreatmentCenterName: string | null
  esnNumber: string | null
  estimatedWeightOrVolumeUnit: WasteMeasurementUnit | null
  estimatedWeightOrVolumeValue: number | null
  ewcCode: string | null
  hazardInducers: string | null
  installationName: string | null
  materialAnalysis: string | null
  materialNumber: string | null
  materialType: string | null
  packaged: ContractLinePackagingType | null
  packagingIndicator: string | null
  packagingType: string | null
  packingGroup: string | null
  pickUpAddressName: string | null
  poNumber: string | null
  position: string | null
  processCode: string | null
  quantityContainers: number | null
  quantityLabels: number | null
  quantityPackages: number | null
  quantityPallets: number | null
  reconciliationNumber: string | null
  remarks: string | null
  serialNumber: string | null
  tankerType: string | null
  tcNumber: string | null
  tfs: boolean | null
  tfsNumber: string | null
  treatmentCenterName: string | null
  unNumber: string | null
  unNumberDescription: string | null
  unNumberHazardous: boolean | null
  wasteMaterial: string | null
  wasteProducerName: string | null
}

export interface FileLinkResponse {
  /**
   * Null when file is from Sharepoint
   */
  uuid: string | null
  name: string
  mimeType: MimeType | null
  order: number | null
  url: string | null
}

export interface ViewPickUpRequestResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string | null
  startTime: string | null
  updatedAt: string
  isReturnPackaging: boolean | null
  isTransportByIndaver: boolean | null
  isWicConfirmed: boolean | null
  additionalFiles: Array<FileLinkResponse>
  customer: CustomerResponse | null
  materials: Array<MaterialResponse>
  needsWicConfirmation: boolean
  packagingRemark: string | null
  packagingRequestMaterials: Array<PackagingRequestMaterialResponse>
  pickUpAddresses: Array<PickUpAddressResponse>
  remarks: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  status: PickUpRequestStatus
  totalQuantityPallets: number | null
  transportMode: PickUpTransportMode | null
  wasteProducer: WasteProducerResponse | null
}

export interface MissingEwcLevelsError {
  code: 'missing_ewc_levels'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export enum PackingGroup {
  NOT_APPLICABLE = 'not-applicable',
  ONE = 'one',
  THREE = 'three',
  TWO = 'two',
}

export interface PickUpRequestMaterialCommand {
  contractLineId: string
  pickUpAddressId?: string | null
  isContainerCovered?: boolean | null
  isHazardous?: boolean | null
  adrClass?: string | null
  asn?: string | null
  containerNumber?: string | null
  /**
   * Transport type id
   */
  containerTransportType?: string | null
  containerType?: string | null
  containerVolumeSize?: string | null
  contractItem: string
  contractNumber: string
  costCenter?: string | null
  customerReference?: string | null
  dangerLabel1?: string | null
  dangerLabel2?: string | null
  dangerLabel3?: string | null
  estimatedWeightOrVolumeUnit?: WasteMeasurementUnit | null
  estimatedWeightOrVolumeValue?: number | null
  ewcCode?: string | null
  hazardInducers?: string | null
  materialNumber?: string | null
  packagingType?: string | null
  packingGroup?: PackingGroup | null
  poNumber?: string | null
  processCode?: string | null
  quantityContainers?: number | null
  quantityLabels?: number | null
  quantityPackages?: number | null
  quantityPallets?: number | null
  reconciliationNumber?: string | null
  serialNumber?: string | null
  /**
   * Tanker type id
   */
  tankerType?: string | null
  tcNumber?: string | null
  tfs?: boolean | null
  tfsNumber?: string | null
  unNumber?: string | null
  unNumberDescription?: string | null
  unNumberHazardous?: boolean | null
  wasteMaterial?: string | null
}

export interface CreateFileLinkCommand {
  fileUuid: string
  order: number
}

export interface UpdatePickUpRequestCommand {
  customerId?: string | null
  wasteProducerId?: string | null
  endDate?: string | null
  startDate?: string | null
  startTime?: string | null
  isReturnPackaging?: boolean | null
  isTransportByIndaver?: boolean | null
  isWicConfirmed?: boolean | null
  additionalFiles?: Array<CreateFileLinkCommand>
  customerName?: string | null
  materials?: Array<PickUpRequestMaterialCommand>
  packagingRemark?: string | null
  packagingRequestMaterials?: Array<PackagingRequestMaterialCommand>
  pickUpAddressIds?: Array<string>
  pickUpAddressNames?: Array<string>
  remarks?: string | null
  sendCopyToContacts?: Array<Contact>
  totalQuantityPallets?: number | null
  transportMode?: PickUpTransportMode | null
  wasteProducerName?: string | null
}

export interface UpdatePickUpRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  isWicConfirmed: boolean | null
  needsWicConfirmation: boolean
}

export interface PickUpRequestAlreadySubmitted {
  code: 'pick_up_request_already_submitted'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface SubmitPickUpRequestResponse {
  uuid: string
  createdAt: string
  submittedOn: string
  updatedAt: string
  requestNumber: string
}

export interface BulkDeletePickUpRequestCommand {
  pickUpRequestUuids: Array<string>
}

export interface ViewPickUpRequestSapResponse {
  confirmedDate: string | null
  endDate: string | null
  startDate: string | null
  startTime: string | null
  submittedOn: string | null
  isReturnPackaging: boolean | null
  isTransportByIndaver: boolean | null
  isWicConfirmed: boolean | null
  additionalFiles: Array<FileLinkResponse>
  createdBy: string | null
  customer: CustomerResponse | null
  materials: Array<MaterialResponse>
  needsWicConfirmation: boolean
  packagingRemark: string | null
  packagingRequestMaterials: Array<PackagingRequestMaterialResponse>
  pickUpAddresses: Array<PickUpAddressResponse>
  remarks: string | null
  requestNumber: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  status: PickUpRequestStatus
  totalQuantityPallets: number | null
  transportMode: PickUpTransportMode | null
  wasteProducer: WasteProducerResponse | null
}

export interface CreatePickUpRequestTemplateCommand {
  name: string
}

export interface CreatePickUpRequestTemplateResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface UpdatedByUserResponse {
  uuid: string
  email: string
  firstName: string | null
  lastName: string | null
}

export interface ViewPickUpRequestTemplateResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string | null
  startTime: string | null
  updatedAt: string
  isReturnPackaging: boolean | null
  isTransportByIndaver: boolean | null
  isWicConfirmed: boolean | null
  additionalFiles: Array<FileLinkResponse>
  customer: CustomerResponse | null
  materials: Array<MaterialResponse>
  needsWicConfirmation: boolean
  packagingRemark: string | null
  packagingRequestMaterials: Array<PackagingRequestMaterialResponse>
  pickUpAddresses: Array<PickUpAddressResponse>
  remarks: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  status: PickUpRequestStatus
  templateName: string
  templateUpdatedByUser?: UpdatedByUserResponse
  totalQuantityPallets: number | null
  transportMode: PickUpTransportMode | null
  wasteProducer: WasteProducerResponse | null
}

export enum ViewPickUpRequestTemplateIndexSortQueryKey {
  CREATED_AT = 'createdAt',
  CREATED_BY = 'createdBy',
  NAME = 'name',
  UPDATED_AT = 'updatedAt',
  UPDATED_BY = 'updatedBy',
}

export interface ViewPickUpRequestTemplateIndexSortQuery {
  key: ViewPickUpRequestTemplateIndexSortQueryKey
  order: SortDirection
}

export interface PickUpRequestTemplateResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  name: string
  /**
   * Name of the user who created the template
   */
  createdBy: string
  /**
   * Name of the last user who updated the template
   */
  updatedBy: string
}

export interface ViewPickUpRequestTemplateIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<PickUpRequestTemplateResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface UpdatePickUpRequestTemplateCommand {
  customerId?: string | null
  wasteProducerId?: string | null
  endDate?: string | null
  startDate?: string | null
  startTime?: string | null
  isReturnPackaging?: boolean | null
  isTransportByIndaver?: boolean | null
  isWicConfirmed?: boolean | null
  customerName?: string | null
  materials?: Array<PickUpRequestMaterialCommand>
  packagingRemark?: string | null
  packagingRequestMaterials?: Array<PackagingRequestMaterialCommand>
  pickUpAddressIds?: Array<string>
  pickUpAddressNames?: Array<string>
  remarks?: string | null
  sendCopyToContacts?: Array<Contact>
  templateName?: string | null
  totalQuantityPallets?: number | null
  transportMode?: PickUpTransportMode | null
  wasteProducerName?: string | null
}

export interface UpdatePickUpRequestTemplateResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface BulkDeletePickUpRequestTemplatesCommand {
  pickUpRequestUuids: Array<string>
}

export interface CreatePickUpRequestFromTemplateCommand {
  [key: string]: unknown
}

export interface CreatePickUpRequestFromTemplateResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface FileNotFoundError {
  code: 'file_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface UploadDocumentSubmittedPickUpRequestCommand {
  fileUuids: Array<string>
}

export interface DownloadDocumentSubmittedPickUpRequestCommand {
  fileName: string
}

export interface InvalidPickUpRequestCopyErrorMeta {
  requestNumber: string
}

export interface InvalidPickUpRequestCopyError {
  code: 'invalid_pick_up_request_copy'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: InvalidPickUpRequestCopyErrorMeta
}

export interface CopyPickUpRequestSapResponse {
  uuid: string
}

export interface InvalidUpdateSapPickUpRequestError {
  code: 'invalid_update_sap_pick_up_request'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface PickUpRequestContractLineNotFoundError {
  code: 'pick_up_request_contract_line_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
}

export interface UpdatePickUpRequestMaterialSapCommand {
  costCenter?: string | null
  poNumber?: string | null
  position: string
}

export interface UpdatePickUpRequestPackagingMaterialSapCommand {
  contractLineId: string
  isSales: boolean | null
  contractItem: string
  contractNumber: string
  costCenter: string | null
  materialNumber: string
  poNumber: string | null
  position: string | null
  quantity: number
  wasteMaterial: string | null
}

export interface UpdatePickUpRequestPackagingSapCommand {
  customerId?: string | null
  deliveryAddressId?: string | null
  wasteProducerId?: string | null
  packagingRequestMaterials?: Array<UpdatePickUpRequestPackagingMaterialSapCommand>
}

export interface UpdatePickUpRequestSapCommand {
  endDate?: string | null
  startDate?: string | null
  isReturnPackaging?: boolean | null
  additionalFiles?: Array<CreateFileLinkCommand>
  contacts?: Array<Contact>
  materials?: Array<UpdatePickUpRequestMaterialSapCommand>
  packagingRemark?: string | null
  packagingRequest?: UpdatePickUpRequestPackagingSapCommand
  remarks?: string | null
}

export interface InvalidIndascanSubmitStatusError {
  code: 'invalid_indascan_submit_status'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface ViewSalesOrganisationResponse {
  id: string
  name: string
}

export interface ViewSalesOrganisationIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<ViewSalesOrganisationResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface TankerTypeResponse {
  id: string
  name: string
}

export interface ViewTankerTypeIndexResponse {
  items: Array<TankerTypeResponse>
}

export interface TransportTypeResponse {
  id: string
  name: string
}

export interface ViewTransportTypeIndexResponse {
  items: Array<TransportTypeResponse>
}

export interface ViewUnNumberFilterQuery {
  keys?: Array<string>
}

export interface ViewUnNumberIndexQueryKey {
  number: string
}

export interface ViewUnNumberIndexPaginationQuery {
  key?: ViewUnNumberIndexQueryKey | null
  limit: number
}

export interface UnNumberResponse {
  isHazardous: boolean | null
  dangerLabel1: string | null
  dangerLabel2: string | null
  dangerLabel3: string | null
  description: string | null
  number: string
  packingGroup: string | null
}

export interface ViewUnNumberIndexResponseMeta {
  next: ViewUnNumberIndexQueryKey | null
}

export interface ViewUnNumberIndexResponse {
  items: Array<UnNumberResponse>
  meta: ViewUnNumberIndexResponseMeta
}

export interface ViewUnNumberIndexForPickUpRequestFilterQuery {
  contractItem: string
  contractNumber: string
  tcNumber?: string
}

export interface ViewUnNumberIndexForPickUpRequestResponse {
  items: Array<UnNumberResponse>
}

export interface CreateWasteInquiryCommand {
  [key: string]: unknown
}

export interface CreateWasteInquiryResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface WasteInquirySummaryDocumentNotFoundErrorMeta {
  wasteInquiryId: string
}

export interface WasteInquirySummaryDocumentNotFoundError {
  code: 'waste_inquiry_summary_document_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: WasteInquirySummaryDocumentNotFoundErrorMeta
}

export interface WasteInquiryNotFoundErrorMeta {
  uuid?: string
  inquiryNumber?: string
}

export interface WasteInquiryNotFoundError {
  code: 'waste_inquiry_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: WasteInquiryNotFoundErrorMeta
}

export interface WasteInquiryDocumentNotFoundErrorMeta {
  wasteInquiryId: string
}

export interface WasteInquiryDocumentNotFoundError {
  code: 'waste_inquiry_document_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: WasteInquiryDocumentNotFoundErrorMeta
}

export interface SapFileNotFoundErrorMeta {
  sapFileUuid: string
}

export interface SapFileNotFoundError {
  code: 'sap_file_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '404'
  /**
   * a meta object containing non-standard meta-information about the error
   */
  meta: SapFileNotFoundErrorMeta
}

export enum WasteInquiryStatus {
  COMPLETED = 'completed',
  CONFORMITY_CONFIRMED = 'conformity_confirmed',
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  NEW = 'new',
  OFFER_APPROVED = 'offer_approved',
  OFFER_SENT = 'offer_sent',
  REJECTED = 'rejected',
  SOLUTION_DEFINED = 'solution_defined',
}

export enum StateOfMatter {
  GASEOUS = 'gaseous',
  LIQUID = 'liquid',
  LIQUID_WITH_SOLIDS = 'liquid-with-solids',
  NO_DATA_AVAILABLE = 'no-data-available',
  POWDER = 'powder',
  SLUDGY = 'sludgy',
  SOLID = 'solid',
  VISCOUS = 'viscous',
}

export enum WastePackagingType {
  BULK = 'bulk',
  PACKAGED = 'packaged',
}

export enum WasteFlashpointOption {
  '23°_60°' = '23° - 60°',
  '<_23°' = '< 23°',
  '>_60°' = '> 60°',
}

export enum WastePhOption {
  '2_4' = '2 - 4',
  '4_10' = '4 - 10',
  '<_2' = '< 2',
  '>_10' = '> 10',
}

export enum StableTemperatureType {
  AMBIENT = 'ambient',
  OTHER = 'other',
}

export interface WasteCompositionResponse {
  name: string | null
  maxWeight: number | null
  minWeight: number | null
}

export enum WasteLegislationOption {
  ANIMAL_BYPRODUCT = 'animal-byproduct',
  CONTROLLED_DRUGS = 'controlled-drugs',
  CWC = 'cwc',
  DRUG_PRECURSOR = 'drug-precursor',
  HG_CONTAINING = 'hg-containing',
  INFECTIOUS_WASTE = 'infectious-waste',
  NONE = 'none',
  OZON_DEPLETING_SUBSTANCE = 'ozon-depleting-substance',
  RADIOACTIVE = 'radioactive',
  SVHC = 'svhc',
}

export enum SvhcExtraOption {
  '<_1_MG_KG' = '< 1 mg/kg',
  '>_1_MG_KG' = '> 1 mg/kg',
  'OTHER' = 'other',
}

export enum WastePropertyOption {
  EXPLOSIVE = 'explosive',
  GASEOUS = 'gaseous',
  HIGH_ACUTE_TOXIC = 'high-acute-toxic',
  NONE = 'none',
  PEROXIDE = 'peroxide',
  POLYMERISATION_SENSITIVE = 'polymerisation-sensitive',
  PYROPHORIC = 'pyrophoric',
  REACTIVE_WITH_F_GAS = 'reactive-with-f-gas',
  REACTIVE_WITH_T_GAS = 'reactive-with-t-gas',
  STRONG_OXIDIZING = 'strong-oxidizing',
  THERMAL_UNSTABLE = 'thermal-unstable',
}

export enum WasteDischargeFrequency {
  ONCE_OFF_CAMPAIGN = 'once-off-campaign',
  ONCE_OFF_STREAM = 'once-off-stream',
  REGULAR_CAMPAIGN = 'regular-campaign',
  REGULAR_STREAM = 'regular-stream',
}

export enum RegulatedTransportOption {
  NO = 'no',
  UNKNOWN = 'unknown',
  YES = 'yes',
}

export interface WasteInquiryUnNumberResponse {
  isHazardous: boolean
  packingGroup: PackingGroup | null
  unNumber: string | null
}

export enum WastePackagingOption {
  ASF = 'asf',
  ASP = 'asp',
  BIG_BAG = 'big-bag',
  CARDBOARD_BOX = 'cardboard-box',
  IBC = 'ibc',
  METAL_DRUM = 'metal-drum',
  OTHER = 'other',
  OVERSIZED_DRUM = 'oversized-drum',
  PLASTIC_DRUM = 'plastic-drum',
}

export enum WeightUnit {
  KG = 'kg',
}

export interface WastePackagingResponse {
  hasInnerPackaging: boolean | null
  remarks: string | null
  size: string | null
  type: WastePackagingOption | null
  weightPerPieceUnit: WeightUnit | null
  weightPerPieceValue: number | null
}

export enum WasteTransportType {
  CONTAINER = 'container',
  OTHER = 'other',
  REL_TRUCK = 'rel-truck',
  SKIP = 'skip',
  TIPPER_TRUCK = 'tipper-truck',
}

export enum ContainerLoadingType {
  CHAIN = 'chain',
  HOOK = 'hook',
}

export enum WasteLoadingType {
  BEFORE_WASTE_COLLECTION = 'before-waste-collection',
  ON_WASTE_COLLECTION = 'on-waste-collection',
}

export enum WasteTransportInOption {
  OTHER = 'other',
  SLUDGE_VACUUM_TRUCK = 'sludge-vacuum-truck',
  TANK_CONTAINER = 'tank-container',
  TANK_TRAILER = 'tank-trailer',
  TANK_TRAILER_WITH_PUMP = 'tank-trailer-with-pump',
  VACUUM_TRUCK = 'vacuum-truck',
}

export enum WasteLoadingMethod {
  GRAVITATIONAL = 'gravitational',
  PUMP_FROM_CUSTOMER = 'pump-from-customer',
  PUMP_FROM_HAULIER = 'pump-from-haulier',
}

export enum WasteStoredInOption {
  DRUMS = 'drums',
  IBCS = 'ibcs',
  OTHER = 'other',
  STORAGE_TANK = 'storage-tank',
  TANK_CONTAINER = 'tank-container',
}

export enum CollectionRequirementOption {
  TRACTOR = 'tractor',
  TRACTOR_TRAILER = 'tractor-trailer',
  TRACTOR_TRAILER_TANK = 'tractor-trailer-tank',
}

export interface ViewWasteInquiryResponse {
  uuid: string
  createdAt: string
  expectedEndDate: string | null
  firstCollectionDate: string | null
  submittedOn: string | null
  updatedAt: string
  isLoadingByIndaver: boolean | null
  isRegulatedTransport: RegulatedTransportOption | null
  isSampleAvailable: boolean | null
  isTankOwnedByCustomer: boolean | null
  isTransportByIndaver: boolean | null
  isUnknownPickUpAddress: boolean
  isUnknownWasteProducer: boolean
  additionalFiles: Array<FileLinkResponse>
  analysisReportFiles: Array<FileLinkResponse>
  averageStableTemperature: number | null
  collectionRemarks: string | null
  collectionRequirements: CollectionRequirementOption | null
  composition: Array<WasteCompositionResponse>
  containerLoadingType: ContainerLoadingType | null
  customer: CustomerResponse | null
  dischargeFrequency: WasteDischargeFrequency | null
  ewcLevel1Name: string | null
  ewcLevel2Name: string | null
  ewcLevel3Name: string | null
  expectedPerCollectionQuantity: number | null
  expectedPerCollectionUnit: WasteMeasurementUnit | null
  expectedYearlyVolumeAmount: number | null
  expectedYearlyVolumeUnit: WasteMeasurementUnit | null
  flashpoint: WasteFlashpointOption | null
  hazardInducer1: string | null
  hazardInducer2: string | null
  hazardInducer3: string | null
  legislationRemarks: string | null
  loadingMethod: WasteLoadingMethod | null
  loadingType: WasteLoadingType | null
  maxStableTemperature: number | null
  minStableTemperature: number | null
  noAnalysisReport: boolean
  noSds: boolean
  packaging: Array<WastePackagingResponse>
  packagingType: WastePackagingType | null
  ph: WastePhOption | null
  pickUpAddress: PickUpAddressResponse | null
  propertyRemarks: string | null
  remarks: string | null
  sdsFiles: Array<FileLinkResponse>
  selectedLegislationOptions: Array<WasteLegislationOption>
  selectedPropertyOptions: Array<WastePropertyOption>
  sendCopyToContacts: Array<ContactTypeResponse>
  specificGravity: number | null
  stableTemperatureType: StableTemperatureType | null
  stateOfMatter: StateOfMatter | null
  status: WasteInquiryStatus
  storedIn: WasteStoredInOption | null
  svhcExtra: SvhcExtraOption | null
  transportIn: WasteTransportInOption | null
  transportType: WasteTransportType | null
  transportVolumeAmount: number | null
  transportVolumeUnit: WasteMeasurementUnit | null
  unNumbers: Array<WasteInquiryUnNumberResponse>
  wasteProducer: WasteProducerResponse | null
  wasteStreamDescription: string | null
  wasteStreamName: string | null
}

export interface EwcCodeNotFound {
  code: 'ewc_code_not_found'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface InvalidStableTemperatureError {
  code: 'invalid_stable_temperature'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface FileNotAccessibleError {
  code: 'file_not_accessible'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NoSdsFilesExpected {
  code: 'no_sds_files_expected'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NoAnalysisReportFilesExpected {
  code: 'no_analysis_report_files_expected'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NoOptionExpectedWhenNoneSelected {
  code: 'no_option_expected_when_none_selected'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface NoSvhcExtraExpected {
  code: 'no_svhc_extra_expected'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface WasteComposition {
  name: string | null
  maxWeight: number | null
  minWeight: number | null
}

export interface UnNumber {
  isHazardous: boolean
  packingGroup: PackingGroup | null
  unNumber: string | null
}

export interface WastePackaging {
  hasInnerPackaging: boolean | null
  remarks: string | null
  size: string | null
  type: WastePackagingOption | null
  weightPerPieceUnit: WeightUnit | null
  weightPerPieceValue: number | null
}

export interface UpdateWasteInquiryCommand {
  customerId?: string | null
  pickUpAddressId?: string | null
  wasteProducerId?: string | null
  expectedEndDate?: string | null
  firstCollectionDate?: string | null
  isLoadingByIndaver?: boolean | null
  isRegulatedTransport?: RegulatedTransportOption | null
  isSampleAvailable?: boolean | null
  isTankOwnedByCustomer?: boolean | null
  isTransportByIndaver?: boolean | null
  isUnknownPickUpAddress?: boolean
  isUnknownWasteProducer?: boolean
  additionalFiles?: Array<CreateFileLinkCommand>
  analysisReportFiles?: Array<CreateFileLinkCommand>
  averageStableTemperature?: number | null
  collectionRemarks?: string | null
  collectionRequirements?: CollectionRequirementOption | null
  composition?: Array<WasteComposition>
  containerLoadingType?: ContainerLoadingType | null
  customerName?: string | null
  dischargeFrequency?: WasteDischargeFrequency | null
  ewcLevel1Name?: string | null
  ewcLevel2Name?: string | null
  ewcLevel3Name?: string | null
  expectedPerCollectionQuantity?: number | null
  expectedPerCollectionUnit?: WasteMeasurementUnit | null
  expectedYearlyVolumeAmount?: number | null
  expectedYearlyVolumeUnit?: WasteMeasurementUnit | null
  flashpoint?: WasteFlashpointOption | null
  hazardInducer1?: string | null
  hazardInducer2?: string | null
  hazardInducer3?: string | null
  legislationRemarks?: string | null
  loadingMethod?: WasteLoadingMethod | null
  loadingType?: WasteLoadingType | null
  maxStableTemperature?: number | null
  minStableTemperature?: number | null
  noAnalysisReport?: boolean
  noSds?: boolean
  packaging?: Array<WastePackaging>
  packagingType?: WastePackagingType | null
  ph?: WastePhOption | null
  pickUpAddressName?: string | null
  propertyRemarks?: string | null
  remarks?: string | null
  sdsFiles?: Array<CreateFileLinkCommand>
  selectedLegislationOptions?: Array<WasteLegislationOption>
  selectedPropertyOptions?: Array<WastePropertyOption>
  sendCopyToContacts?: Array<Contact>
  specificGravity?: number | null
  stableTemperatureType?: StableTemperatureType | null
  stateOfMatter?: StateOfMatter | null
  storedIn?: WasteStoredInOption | null
  svhcExtra?: SvhcExtraOption | null
  transportIn?: WasteTransportInOption | null
  transportType?: WasteTransportType | null
  transportVolumeAmount?: number | null
  transportVolumeUnit?: WasteMeasurementUnit | null
  unNumbers?: Array<UnNumber>
  wasteProducerName?: string | null
  wasteStreamDescription?: string | null
  wasteStreamName?: string | null
}

export interface UpdateWasteInquiryResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface WasteInquiryAlreadySubmitted {
  code: 'waste_inquiry_already_submitted'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '409'
}

export interface SubmitWasteInquiryResponse {
  uuid: string
  createdAt: string
  submittedOn: string
  updatedAt: string
  inquiryNumber: string
}

export enum ViewWasteInquiryIndexSortQueryKey {
  CONFORMITY_CHECK = 'conformityCheck',
  CONTRACT_ID = 'contractId',
  CONTRACT_ITEM = 'contractItem',
  CUSTOMER_NAME = 'customerName',
  DATE = 'date',
  INQUIRY_NUMBER = 'inquiryNumber',
  PICK_UP_ADDRESS_ID = 'pickUpAddressId',
  SALES_ORGANISATION_ID = 'salesOrganisationId',
  WASTE_PRODUCER_ID = 'wasteProducerId',
  WASTE_PRODUCER_NAME = 'wasteProducerName',
  WASTE_STREAM_NAME = 'wasteStreamName',
}

export interface ViewWasteInquiryIndexSortQuery {
  key: ViewWasteInquiryIndexSortQueryKey
  order: SortDirection
}

export interface ViewWasteInquiryIndexFilterQuery {
  contractId?: string
  customerId?: string
  pickUpAddressId?: string
  salesOrganisationId?: string
  wasteProducerId?: string
  conformityCheck?: boolean
  contractItem?: string
  date?: DateRange
  ewcCode?: string
  inquiryNumber?: string
  requestorName?: string
  statuses: Array<WasteInquiryStatus>
  wasteStreamName?: string
}

export interface WasteInquiryResponse {
  /**
   * UUID is null when retrieved from SAP
   */
  uuid: string | null
  contractId: string | null
  customerId: string | null
  pickUpAddressId: string | null
  salesOrganisationId: string | null
  wasteProducerId: string | null
  conformityCheck: boolean
  contractItem: string | null
  customerName: string | null
  date: string | null
  ewcLevel1: string | null
  ewcLevel2: string | null
  ewcLevel3: string | null
  inquiryNumber: string | null
  pickUpAddressName: string | null
  requestorName: string | null
  salesOrganisationName: string | null
  status: WasteInquiryStatus
  wasteProducerName: string | null
  wasteStreamName: string | null
}

export interface ViewWasteInquiryIndexResponse {
  /**
   * The items for the current page
   */
  items: Array<WasteInquiryResponse>
  meta: PaginatedOffsetResponseMeta
}

export interface ViewWasteInquirySapResponse {
  expectedEndDate: string | null
  firstCollectionDate: string | null
  submittedOn: string | null
  isLoadingByIndaver: boolean | null
  isRegulatedTransport: RegulatedTransportOption | null
  isSampleAvailable: boolean | null
  isTankOwnedByCustomer: boolean | null
  isTransportByIndaver: boolean | null
  isUnknownPickUpAddress: boolean
  isUnknownWasteProducer: boolean
  additionalFiles: Array<FileLinkResponse>
  analysisReportFiles: Array<FileLinkResponse>
  averageStableTemperature: number | null
  collectionRemarks: string | null
  collectionRequirements: CollectionRequirementOption | null
  composition: Array<WasteCompositionResponse>
  containerLoadingType: ContainerLoadingType | null
  contractItem: string | null
  contractNumber: string | null
  createdBy: string | null
  customer: CustomerResponse | null
  dischargeFrequency: WasteDischargeFrequency | null
  ewcLevel1Name: string | null
  ewcLevel2Name: string | null
  ewcLevel3Name: string | null
  expectedPerCollectionQuantity: number | null
  expectedPerCollectionUnit: WasteMeasurementUnit | null
  expectedYearlyVolumeAmount: number | null
  expectedYearlyVolumeUnit: WasteMeasurementUnit | null
  flashpoint: WasteFlashpointOption | null
  hazardInducer1: string | null
  hazardInducer2: string | null
  hazardInducer3: string | null
  inquiryNumber: string | null
  legislationRemarks: string | null
  loadingMethod: WasteLoadingMethod | null
  loadingType: WasteLoadingType | null
  maxStableTemperature: number | null
  minStableTemperature: number | null
  noAnalysisReport: boolean
  noSds: boolean
  packaging: Array<WastePackagingResponse>
  packagingType: WastePackagingType | null
  ph: WastePhOption | null
  pickUpAddress: PickUpAddressResponse | null
  propertyRemarks: string | null
  remarks: string | null
  sdsFiles: Array<FileLinkResponse>
  selectedLegislationOptions: Array<WasteLegislationOption>
  selectedPropertyOptions: Array<WastePropertyOption>
  sendCopyToContacts: Array<ContactTypeResponse>
  specificGravity: number | null
  stableTemperatureType: StableTemperatureType | null
  stateOfMatter: StateOfMatter | null
  status: WasteInquiryStatus
  storedIn: WasteStoredInOption | null
  svhcExtra: SvhcExtraOption | null
  transportIn: WasteTransportInOption | null
  transportType: WasteTransportType | null
  transportVolumeAmount: number | null
  transportVolumeUnit: WasteMeasurementUnit | null
  unNumbers: Array<WasteInquiryUnNumberResponse>
  wasteProducer: WasteProducerResponse | null
  wasteStreamDescription: string | null
  wasteStreamName: string | null
}

export enum EntityPart {
  ADDITIONAL = 'additional',
  ANALYSIS_REPORT = 'analysis-report',
  NEWS_IMAGE = 'news-image',
  SDS = 'sds',
}

export interface UploadDocumentWasteInquiryCommand {
  fileUuid: string
  entityPart: EntityPart
}

export interface UploadDocumentSubmittedWasteInquiryCommand {
  documents: Array<UploadDocumentWasteInquiryCommand>
}

export interface BulkDeleteWasteInquiryCommand {
  wasteInquiryUuids: Array<string>
}

export interface CopyWasteInquirySapResponse {
  uuid: string
}

export interface CreateWeeklyPlanningRequestCommand {
  [key: string]: unknown
}

export interface CreateWeeklyPlanningRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface CustomerNotProvidedError {
  code: 'customer_not_provided'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface UpdateWeeklyPlanningRequestCommand {
  customerId?: string | null
  wasteProducerId?: string | null
  additionalFiles?: Array<CreateFileLinkCommand>
  pickUpAddressIds?: Array<string>
  remarks?: string | null
  sendCopyToContacts?: Array<Contact>
}

export interface UpdateWeeklyPlanningRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
}

export interface AddWprPickUpRequestCommand {
  pickUpRequestUuid: string
}

export interface AddWprPickUpRequestResponse {
  uuid: string
}

export interface ViewWprPickUpRequestResponse {
  uuid: string
  createdAt: string
  endDate: string | null
  startDate: string | null
  startTime: string | null
  updatedAt: string
  isReturnPackaging: boolean | null
  isTransportByIndaver: boolean | null
  isWicConfirmed: boolean | null
  additionalFiles: Array<FileLinkResponse>
  customer: CustomerResponse | null
  materials: Array<MaterialResponse>
  needsWicConfirmation: boolean
  packagingRemark: string | null
  packagingRequestMaterials: Array<PackagingRequestMaterialResponse>
  pickUpAddresses: Array<PickUpAddressResponse>
  remarks: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  status: PickUpRequestStatus
  totalQuantityPallets: number | null
  transportMode: PickUpTransportMode | null
  wasteProducer: WasteProducerResponse | null
}

export interface ViewWeeklyPlanningRequestResponse {
  uuid: string
  createdAt: string
  updatedAt: string
  additionalFiles: Array<FileLinkResponse>
  customer: CustomerResponse | null
  pickUpAddresses: Array<PickUpAddressResponse>
  pickUpRequests: Array<ViewWprPickUpRequestResponse>
  remarks: string | null
  sendCopyToContacts: Array<ContactTypeResponse>
  wasteProducer: WasteProducerResponse | null
}

export interface WeeklyPlanningRequestAlreadySubmittedError {
  code: 'weekly_planning_request_already_submitted_error'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  source?: ErrorSource
  status: '400'
}

export interface SubmitWeeklyPlanningRequestResponse {
  uuid: string
  createdAt: string
  submittedOn: string
  updatedAt: string
  inquiryNumber: string
}

export interface InternalServerApiError {
  code: 'internal_server_error'
  /**
   * a human-readable explanation specific to this occurrence of the problem
   */
  detail?: string
  status: '500'
}

export interface MigrateCollectionsV1Data {
  body?: never
  query?: {
    collections?: Array<'contact' | 'pick-up-request-template' | 'user'>
    fresh?: boolean
  }
  url: '/api/v1/typesense/migrate'
  path?: never
}

export interface MigrateCollectionsV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type MigrateCollectionsV1Error = MigrateCollectionsV1Errors[keyof MigrateCollectionsV1Errors]

export interface MigrateCollectionsV1Responses {
  /**
   * Successfully migrated collections
   */
  200: unknown
}

export interface ImportCollectionsV1Data {
  body?: never
  query?: {
    collections?: Array<'contact' | 'pick-up-request-template' | 'user'>
  }
  url: '/api/v1/typesense/import'
  path?: never
}

export interface ImportCollectionsV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ImportCollectionsV1Error = ImportCollectionsV1Errors[keyof ImportCollectionsV1Errors]

export interface ImportCollectionsV1Responses {
  /**
   * Successfully imported collections
   */
  200: unknown
}

export interface ViewCollectionsV1Data {
  body?: never
  query?: never
  url: '/api/v1/typesense/collections'
  path?: never
}

export interface ViewCollectionsV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewCollectionsV1Error = ViewCollectionsV1Errors[keyof ViewCollectionsV1Errors]

export interface ViewCollectionsV1Responses {
  /**
   * Successfully returned collections
   */
  200: unknown
}

export interface ViewMeV1Data {
  body?: never
  query?: never
  url: '/api/v1/users/me'
  path?: never
}

export interface ViewMeV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewMeV1Error = ViewMeV1Errors[keyof ViewMeV1Errors]

export interface ViewMeV1Responses {
  /**
   * User details retrieved
   */
  200: ViewMeResponse
}

export type ViewMeV1Response = ViewMeV1Responses[keyof ViewMeV1Responses]

export interface ViewUserDetailV1Data {
  body?: never
  query?: never
  url: '/api/v1/users/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewUserDetailV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUserDetailV1Error = ViewUserDetailV1Errors[keyof ViewUserDetailV1Errors]

export interface ViewUserDetailV1Responses {
  /**
   * User details retrieved
   */
  200: ViewUserDetailResponse
}

export type ViewUserDetailV1Response = ViewUserDetailV1Responses[keyof ViewUserDetailV1Responses]

export interface ViewUserIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/users'
  path?: never
}

export interface ViewUserIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUserIndexV1Error = ViewUserIndexV1Errors[keyof ViewUserIndexV1Errors]

export interface ViewUserIndexV1Responses {
  /**
   * Users retrieved
   */
  200: ViewUserIndexResponse
}

export type ViewUserIndexV1Response = ViewUserIndexV1Responses[keyof ViewUserIndexV1Responses]

export interface SyncEntraUsersV1Data {
  body?: never
  query?: never
  url: '/api/v1/users/sync-external'
  path?: never
}

export interface SyncEntraUsersV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SyncEntraUsersV1Error = SyncEntraUsersV1Errors[keyof SyncEntraUsersV1Errors]

export interface SyncEntraUsersV1Responses {
  /**
   * Azure Entra users synced successfully
   */
  201: unknown
}

export interface StartUserImpersonationV1Data {
  body?: never
  query?: never
  url: '/api/v1/users/{uuid}/impersonate'
  path: {
    uuid: string
  }
}

export interface StartUserImpersonationV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type StartUserImpersonationV1Error = StartUserImpersonationV1Errors[keyof StartUserImpersonationV1Errors]

export interface StartUserImpersonationV1Responses {
  /**
   * User impersonation started
   */
  200: StartUserImpersonationResponse
}

export type StartUserImpersonationV1Response = StartUserImpersonationV1Responses[keyof StartUserImpersonationV1Responses]

export interface ViewPermissionIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/permissions'
  path?: never
}

export interface ViewPermissionIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPermissionIndexV1Error = ViewPermissionIndexV1Errors[keyof ViewPermissionIndexV1Errors]

export interface ViewPermissionIndexV1Responses {
  200: ViewPermissionIndexResponse
}

export type ViewPermissionIndexV1Response = ViewPermissionIndexV1Responses[keyof ViewPermissionIndexV1Responses]

export interface ViewCustomerIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/customers'
  path?: never
}

export interface ViewCustomerIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewCustomerIndexV1Error = ViewCustomerIndexV1Errors[keyof ViewCustomerIndexV1Errors]

export interface ViewCustomerIndexV1Responses {
  200: ViewCustomerIndexResponse
}

export type ViewCustomerIndexV1Response = ViewCustomerIndexV1Responses[keyof ViewCustomerIndexV1Responses]

export interface CreateFileV1Data {
  body: CreateFileCommand
  query?: never
  url: '/api/v1/files'
  path?: never
}

export interface CreateFileV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateFileV1Error = CreateFileV1Errors[keyof CreateFileV1Errors]

export interface CreateFileV1Responses {
  201: CreateFileResponse
}

export type CreateFileV1Response = CreateFileV1Responses[keyof CreateFileV1Responses]

export interface ConfirmFileUploadV1Data {
  body?: never
  query?: never
  url: '/api/v1/files/{file}/confirm-upload'
  path: {
    file: string
  }
}

export interface ConfirmFileUploadV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ConfirmFileUploadV1Error = ConfirmFileUploadV1Errors[keyof ConfirmFileUploadV1Errors]

export interface ConfirmFileUploadV1Responses {
  200: unknown
}

export interface DownloadFileV1Data {
  body?: never
  query?: never
  url: '/api/v1/files/{file}/download'
  path: {
    file: string
  }
}

export interface DownloadFileV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadFileV1Error = DownloadFileV1Errors[keyof DownloadFileV1Errors]

export interface ProxyExternalFileV1Data {
  body?: never
  query: {
    url: string
  }
  url: '/api/v1/files/proxy-external'
  path?: never
}

export interface ProxyExternalFileV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ProxyExternalFileV1Error = ProxyExternalFileV1Errors[keyof ProxyExternalFileV1Errors]

export interface ProxyExternalFileV1Responses {
  200: unknown
}

export interface ViewSuggestedCustomersV1Data {
  body?: never
  query: {
    filter: ViewSuggestedCustomersFilterQuery
  }
  url: '/api/v1/suggested-customers'
  path?: never
}

export interface ViewSuggestedCustomersV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewSuggestedCustomersV1Error = ViewSuggestedCustomersV1Errors[keyof ViewSuggestedCustomersV1Errors]

export interface ViewSuggestedCustomersV1Responses {
  200: ViewSuggestedCustomersResponse
}

export type ViewSuggestedCustomersV1Response = ViewSuggestedCustomersV1Responses[keyof ViewSuggestedCustomersV1Responses]

export interface ViewCustomerCountryV1Data {
  body?: never
  query?: never
  url: '/api/v1/customers/{id}/country'
  path: {
    id: string
  }
}

export interface ViewCustomerCountryV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<CustomerNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewCustomerCountryV1Error = ViewCustomerCountryV1Errors[keyof ViewCustomerCountryV1Errors]

export interface ViewCustomerCountryV1Responses {
  200: ViewCustomerCountryResponse
}

export type ViewCustomerCountryV1Response = ViewCustomerCountryV1Responses[keyof ViewCustomerCountryV1Responses]

export interface ViewDomainEventLogIndexV1Data {
  body?: never
  query?: {
    filter?: ViewDomainEventLogIndexFilterQuery
    pagination?: ViewDomainEventLogIndexPaginationQuery
  }
  url: '/api/v1/event-logs'
  path?: never
}

export interface ViewDomainEventLogIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDomainEventLogIndexV1Error = ViewDomainEventLogIndexV1Errors[keyof ViewDomainEventLogIndexV1Errors]

export interface ViewDomainEventLogIndexV1Responses {
  200: ViewDomainEventLogIndexResponse
}

export type ViewDomainEventLogIndexV1Response = ViewDomainEventLogIndexV1Responses[keyof ViewDomainEventLogIndexV1Responses]

export interface SearchCollectionsV1Data {
  body?: never
  query: {
    filter: SearchCollectionsFilterQuery
    search: string
  }
  url: '/api/v1/search'
  path?: never
}

export interface SearchCollectionsV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SearchCollectionsV1Error = SearchCollectionsV1Errors[keyof SearchCollectionsV1Errors]

export interface SearchCollectionsV1Responses {
  200: SearchCollectionsResponse
}

export type SearchCollectionsV1Response = SearchCollectionsV1Responses[keyof SearchCollectionsV1Responses]

export interface ViewJobsIndexV1Data {
  body?: never
  query?: {
    filter?: ViewJobsIndexFilterQuery
    pagination?: ViewJobsIndexPaginationQuery
    sort?: Array<ViewJobsIndexSortQuery>
  }
  url: '/api/v1/jobs'
  path?: never
}

export interface ViewJobsIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewJobsIndexV1Error = ViewJobsIndexV1Errors[keyof ViewJobsIndexV1Errors]

export interface ViewJobsIndexV1Responses {
  200: ViewJobsIndexResponse
}

export type ViewJobsIndexV1Response = ViewJobsIndexV1Responses[keyof ViewJobsIndexV1Responses]

export interface ViewJobDetailV1Data {
  body?: never
  query: {
    isArchived: boolean
  }
  url: '/api/v1/jobs/{jobId}'
  path: {
    jobId: string
  }
}

export interface ViewJobDetailV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewJobDetailV1Error = ViewJobDetailV1Errors[keyof ViewJobDetailV1Errors]

export interface ViewJobDetailV1Responses {
  200: ViewJobDetailResponse
}

export type ViewJobDetailV1Response = ViewJobDetailV1Responses[keyof ViewJobDetailV1Responses]

export interface GetMyNotificationPreferencesV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notification-preferences'
  path?: never
}

export interface GetMyNotificationPreferencesV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetMyNotificationPreferencesV1Error = GetMyNotificationPreferencesV1Errors[keyof GetMyNotificationPreferencesV1Errors]

export interface GetMyNotificationPreferencesV1Responses {
  200: GetMyNotificationPreferencesResponse
}

export type GetMyNotificationPreferencesV1Response = GetMyNotificationPreferencesV1Responses[keyof GetMyNotificationPreferencesV1Responses]

export interface GetNotificationTypesConfigV1Data {
  body?: never
  query?: never
  url: '/api/v1/notification-preferences/config'
  path?: never
}

export interface GetNotificationTypesConfigV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetNotificationTypesConfigV1Error = GetNotificationTypesConfigV1Errors[keyof GetNotificationTypesConfigV1Errors]

export interface GetNotificationTypesConfigV1Responses {
  200: GetNotificationTypesConfigResponse
}

export type GetNotificationTypesConfigV1Response = GetNotificationTypesConfigV1Responses[keyof GetNotificationTypesConfigV1Responses]

export interface UpdateMyChannelNotificationPreferenceV1Data {
  body: UpdateMyChannelNotificationPreferenceCommand
  query?: never
  url: '/api/v1/me/notification-preferences/channels'
  path?: never
}

export interface UpdateMyChannelNotificationPreferenceV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateMyChannelNotificationPreferenceV1Error = UpdateMyChannelNotificationPreferenceV1Errors[keyof UpdateMyChannelNotificationPreferenceV1Errors]

export interface UpdateMyChannelNotificationPreferenceV1Responses {
  204: void
}

export type UpdateMyChannelNotificationPreferenceV1Response = UpdateMyChannelNotificationPreferenceV1Responses[keyof UpdateMyChannelNotificationPreferenceV1Responses]

export interface SendTestNotificationV1Data {
  body: SendTestNotificationCommand
  query?: never
  url: '/api/v1/notifications/test-notification'
  path?: never
}

export interface SendTestNotificationV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SendTestNotificationV1Error = SendTestNotificationV1Errors[keyof SendTestNotificationV1Errors]

export interface SendTestNotificationV1Responses {
  204: void
}

export type SendTestNotificationV1Response = SendTestNotificationV1Responses[keyof SendTestNotificationV1Responses]

export interface GetMyNotificationsV1Data {
  body?: never
  query?: {
    filter?: GetMyNotificationsFilterQuery
    pagination?: GetMyNotificationsPaginationQuery
  }
  url: '/api/v1/me/notifications'
  path?: never
}

export interface GetMyNotificationsV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetMyNotificationsV1Error = GetMyNotificationsV1Errors[keyof GetMyNotificationsV1Errors]

export interface GetMyNotificationsV1Responses {
  200: GetMyNotificationsResponse
}

export type GetMyNotificationsV1Response = GetMyNotificationsV1Responses[keyof GetMyNotificationsV1Responses]

export interface ViewUnreadNotificationsCountV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notifications/unread-count'
  path?: never
}

export interface ViewUnreadNotificationsCountV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUnreadNotificationsCountV1Error = ViewUnreadNotificationsCountV1Errors[keyof ViewUnreadNotificationsCountV1Errors]

export interface ViewUnreadNotificationsCountV1Responses {
  200: ViewUnreadNotificationsCountResponse
}

export type ViewUnreadNotificationsCountV1Response = ViewUnreadNotificationsCountV1Responses[keyof ViewUnreadNotificationsCountV1Responses]

export interface ViewUserNotificationDetailV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notifications/{notificationUuid}'
  path: {
    notificationUuid: string
  }
}

export interface ViewUserNotificationDetailV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUserNotificationDetailV1Error = ViewUserNotificationDetailV1Errors[keyof ViewUserNotificationDetailV1Errors]

export interface ViewUserNotificationDetailV1Responses {
  200: TestNotificationNotification
}

export type ViewUserNotificationDetailV1Response = ViewUserNotificationDetailV1Responses[keyof ViewUserNotificationDetailV1Responses]

export interface MarkAllNotificationAsReadV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notifications/mark-as-read'
  path?: never
}

export interface MarkAllNotificationAsReadV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type MarkAllNotificationAsReadV1Error = MarkAllNotificationAsReadV1Errors[keyof MarkAllNotificationAsReadV1Errors]

export interface MarkAllNotificationAsReadV1Responses {
  204: void
}

export type MarkAllNotificationAsReadV1Response = MarkAllNotificationAsReadV1Responses[keyof MarkAllNotificationAsReadV1Responses]

export interface UpdateMyNotificationTypePreferenceV1Data {
  body: UpdateMyNotificationTypePreferenceCommand
  query?: never
  url: '/api/v1/me/notification-preferences/types'
  path?: never
}

export interface UpdateMyNotificationTypePreferenceV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateMyNotificationTypePreferenceV1Error = UpdateMyNotificationTypePreferenceV1Errors[keyof UpdateMyNotificationTypePreferenceV1Errors]

export interface UpdateMyNotificationTypePreferenceV1Responses {
  204: void
}

export type UpdateMyNotificationTypePreferenceV1Response = UpdateMyNotificationTypePreferenceV1Responses[keyof UpdateMyNotificationTypePreferenceV1Responses]

export interface MarkNotificationAsReadV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notifications/{notificationUuid}/mark-as-read'
  path: {
    notificationUuid: string
  }
}

export interface MarkNotificationAsReadV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<UserNotificationNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type MarkNotificationAsReadV1Error = MarkNotificationAsReadV1Errors[keyof MarkNotificationAsReadV1Errors]

export interface MarkNotificationAsReadV1Responses {
  204: void
}

export type MarkNotificationAsReadV1Response = MarkNotificationAsReadV1Responses[keyof MarkNotificationAsReadV1Responses]

export interface MarkNotificationAsUnreadV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/notifications/{notificationUuid}/mark-as-unread'
  path: {
    notificationUuid: string
  }
}

export interface MarkNotificationAsUnreadV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<UserNotificationNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type MarkNotificationAsUnreadV1Error = MarkNotificationAsUnreadV1Errors[keyof MarkNotificationAsUnreadV1Errors]

export interface MarkNotificationAsUnreadV1Responses {
  204: void
}

export type MarkNotificationAsUnreadV1Response = MarkNotificationAsUnreadV1Responses[keyof MarkNotificationAsUnreadV1Responses]

export interface UpdateMyNotificationPreferencePresetV1Data {
  body: UpdateMyNotificationPreferencePresetCommand
  query?: never
  url: '/api/v1/me/notification-preferences/preset'
  path?: never
}

export interface UpdateMyNotificationPreferencePresetV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateMyNotificationPreferencePresetV1Error = UpdateMyNotificationPreferencePresetV1Errors[keyof UpdateMyNotificationPreferencePresetV1Errors]

export interface UpdateMyNotificationPreferencePresetV1Responses {
  204: void
}

export type UpdateMyNotificationPreferencePresetV1Response = UpdateMyNotificationPreferencePresetV1Responses[keyof UpdateMyNotificationPreferencePresetV1Responses]

export interface MigrateNotificationTypesV1Data {
  body: MigrateNotificationTypesCommand
  query?: never
  url: '/api/v1/notifications/migrate'
  path?: never
}

export interface MigrateNotificationTypesV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<MigrationAlreadyPerformedError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type MigrateNotificationTypesV1Error = MigrateNotificationTypesV1Errors[keyof MigrateNotificationTypesV1Errors]

export interface MigrateNotificationTypesV1Responses {
  204: void
}

export type MigrateNotificationTypesV1Response = MigrateNotificationTypesV1Responses[keyof MigrateNotificationTypesV1Responses]

export interface ViewRoleIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/roles'
  path?: never
}

export interface ViewRoleIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewRoleIndexV1Error = ViewRoleIndexV1Errors[keyof ViewRoleIndexV1Errors]

export interface ViewRoleIndexV1Responses {
  /**
   * The roles has been successfully received.
   */
  200: ViewRoleIndexResponse
}

export type ViewRoleIndexV1Response = ViewRoleIndexV1Responses[keyof ViewRoleIndexV1Responses]

export interface UpdateRolesPermissionsV1Data {
  body: UpdateRolesPermissionsCommand
  query?: never
  url: '/api/v1/roles'
  path?: never
}

export interface UpdateRolesPermissionsV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<RoleNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateRolesPermissionsV1Error = UpdateRolesPermissionsV1Errors[keyof UpdateRolesPermissionsV1Errors]

export interface UpdateRolesPermissionsV1Responses {
  204: void
}

export type UpdateRolesPermissionsV1Response = UpdateRolesPermissionsV1Responses[keyof UpdateRolesPermissionsV1Responses]

export interface CreateRoleV1Data {
  body: CreateRoleCommand
  query?: never
  url: '/api/v1/roles'
  path?: never
}

export interface CreateRoleV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateRoleV1Error = CreateRoleV1Errors[keyof CreateRoleV1Errors]

export interface CreateRoleV1Responses {
  201: CreateRoleResponse
}

export type CreateRoleV1Response = CreateRoleV1Responses[keyof CreateRoleV1Responses]

export interface ClearRolePermissionsCacheV1Data {
  body: ClearRolePermissionsCacheCommand
  query?: never
  url: '/api/v1/roles/clear-cache'
  path?: never
}

export interface ClearRolePermissionsCacheV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ClearRolePermissionsCacheV1Error = ClearRolePermissionsCacheV1Errors[keyof ClearRolePermissionsCacheV1Errors]

export interface ClearRolePermissionsCacheV1Responses {
  204: void
}

export type ClearRolePermissionsCacheV1Response = ClearRolePermissionsCacheV1Responses[keyof ClearRolePermissionsCacheV1Responses]

export interface DeleteRoleV1Data {
  body?: never
  query?: never
  url: '/api/v1/roles/{role}'
  path: {
    role: string
  }
}

export interface DeleteRoleV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DeleteRoleV1Error = DeleteRoleV1Errors[keyof DeleteRoleV1Errors]

export interface DeleteRoleV1Responses {
  204: void
}

export type DeleteRoleV1Response = DeleteRoleV1Responses[keyof DeleteRoleV1Responses]

export interface ViewRoleDetailV1Data {
  body?: never
  query?: never
  url: '/api/v1/roles/{role}'
  path: {
    role: string
  }
}

export interface ViewRoleDetailV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewRoleDetailV1Error = ViewRoleDetailV1Errors[keyof ViewRoleDetailV1Errors]

export interface ViewRoleDetailV1Responses {
  /**
   * The role has been successfully received.
   */
  200: ViewRoleDetailResponse
}

export type ViewRoleDetailV1Response = ViewRoleDetailV1Responses[keyof ViewRoleDetailV1Responses]

export interface UpdateRoleV1Data {
  body: UpdateRoleCommand
  query?: never
  url: '/api/v1/roles/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateRoleV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateRoleV1Error = UpdateRoleV1Errors[keyof UpdateRoleV1Errors]

export interface UpdateRoleV1Responses {
  201: unknown
}

export interface GetApiInfoData {
  body?: never
  query?: never
  url: '/api'
  path?: never
}

export interface GetApiInfoErrors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetApiInfoError = GetApiInfoErrors[keyof GetApiInfoErrors]

export interface GetApiInfoResponses {
  /**
   * API info retrieved
   */
  200: GetApiInfoResponse
}

export type GetApiInfoResponse2 = GetApiInfoResponses[keyof GetApiInfoResponses]

export interface SwaggerData {
  body?: never
  query?: never
  url: '/api/oauth2-redirect'
  path?: never
}

export interface SwaggerErrors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SwaggerError = SwaggerErrors[keyof SwaggerErrors]

export interface SwaggerResponses {
  200: unknown
}

export interface ViewUiPreferencesV1Data {
  body?: never
  query?: never
  url: '/api/v1/me/ui-preferences'
  path?: never
}

export interface ViewUiPreferencesV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUiPreferencesV1Error = ViewUiPreferencesV1Errors[keyof ViewUiPreferencesV1Errors]

export interface ViewUiPreferencesV1Responses {
  200: ViewUiPreferencesResponse
}

export type ViewUiPreferencesV1Response = ViewUiPreferencesV1Responses[keyof ViewUiPreferencesV1Responses]

export interface UpdateUiPreferencesV1Data {
  body: UpdateUiPreferencesCommand
  query?: never
  url: '/api/v1/me/ui-preferences'
  path?: never
}

export interface UpdateUiPreferencesV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateUiPreferencesV1Error = UpdateUiPreferencesV1Errors[keyof UpdateUiPreferencesV1Errors]

export interface UpdateUiPreferencesV1Responses {
  200: unknown
}

export interface ViewAnnouncementIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/announcements'
  path?: never
}

export interface ViewAnnouncementIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewAnnouncementIndexV1Error = ViewAnnouncementIndexV1Errors[keyof ViewAnnouncementIndexV1Errors]

export interface ViewAnnouncementIndexV1Responses {
  200: ViewAnnouncementIndexResponse
}

export type ViewAnnouncementIndexV1Response = ViewAnnouncementIndexV1Responses[keyof ViewAnnouncementIndexV1Responses]

export interface CreateAnnouncementV1Data {
  body: CreateAnnouncementCommand
  query?: never
  url: '/api/v1/announcements'
  path?: never
}

export interface CreateAnnouncementV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateAnnouncementV1Error = CreateAnnouncementV1Errors[keyof CreateAnnouncementV1Errors]

export interface CreateAnnouncementV1Responses {
  201: CreateAnnouncementResponse
}

export type CreateAnnouncementV1Response = CreateAnnouncementV1Responses[keyof CreateAnnouncementV1Responses]

export interface DeleteAnnouncementV1Data {
  body?: never
  query?: never
  url: '/api/v1/announcements/{uuid}'
  path: {
    uuid: string
  }
}

export interface DeleteAnnouncementV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DeleteAnnouncementV1Error = DeleteAnnouncementV1Errors[keyof DeleteAnnouncementV1Errors]

export interface DeleteAnnouncementV1Responses {
  200: unknown
}

export interface ViewAnnouncementV1Data {
  body?: never
  query?: never
  url: '/api/v1/announcements/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewAnnouncementV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewAnnouncementV1Error = ViewAnnouncementV1Errors[keyof ViewAnnouncementV1Errors]

export interface ViewAnnouncementV1Responses {
  200: ViewAnnouncementResponse
}

export type ViewAnnouncementV1Response = ViewAnnouncementV1Responses[keyof ViewAnnouncementV1Responses]

export interface UpdateAnnouncementV1Data {
  body: UpdateAnnouncementCommand
  query?: never
  url: '/api/v1/announcements/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateAnnouncementV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<DateMustBeAfterError | FieldMustBeNullError | MissingRequiredFieldError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateAnnouncementV1Error = UpdateAnnouncementV1Errors[keyof UpdateAnnouncementV1Errors]

export interface UpdateAnnouncementV1Responses {
  200: UpdateAnnouncementResponse
}

export type UpdateAnnouncementV1Response = UpdateAnnouncementV1Responses[keyof UpdateAnnouncementV1Responses]

export interface ViewDashboardAnnouncementIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/dashboard-announcements'
  path?: never
}

export interface ViewDashboardAnnouncementIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDashboardAnnouncementIndexV1Error = ViewDashboardAnnouncementIndexV1Errors[keyof ViewDashboardAnnouncementIndexV1Errors]

export interface ViewDashboardAnnouncementIndexV1Responses {
  200: ViewDashboardAnnouncementIndexResponse
}

export type ViewDashboardAnnouncementIndexV1Response = ViewDashboardAnnouncementIndexV1Responses[keyof ViewDashboardAnnouncementIndexV1Responses]

export interface ViewDashboardAnnouncementV1Data {
  body?: never
  query?: never
  url: '/api/v1/dashboard-announcements/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewDashboardAnnouncementV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDashboardAnnouncementV1Error = ViewDashboardAnnouncementV1Errors[keyof ViewDashboardAnnouncementV1Errors]

export interface ViewDashboardAnnouncementV1Responses {
  200: ViewDashboardAnnouncementResponse
}

export type ViewDashboardAnnouncementV1Response = ViewDashboardAnnouncementV1Responses[keyof ViewDashboardAnnouncementV1Responses]

export interface DownloadCertificateV1Data {
  body: DownloadCertificateCommand
  query?: never
  url: '/api/v1/certificates/download'
  path?: never
}

export interface DownloadCertificateV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<CertificateNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadCertificateV1Error = DownloadCertificateV1Errors[keyof DownloadCertificateV1Errors]

export interface DownloadCertificateV1Responses {
  200: unknown
}

export interface ViewCertificateIndexV1Data {
  body?: never
  query: {
    filter?: ViewCertificateIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    sort: Array<ViewCertificateIndexSortQuery>
  }
  url: '/api/v1/certificates'
  path?: never
}

export interface ViewCertificateIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewCertificateIndexV1Error = ViewCertificateIndexV1Errors[keyof ViewCertificateIndexV1Errors]

export interface ViewCertificateIndexV1Responses {
  200: ViewCertificateIndexResponse
}

export type ViewCertificateIndexV1Response = ViewCertificateIndexV1Responses[keyof ViewCertificateIndexV1Responses]

export interface ViewContactIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/contacts'
  path?: never
}

export interface ViewContactIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewContactIndexV1Error = ViewContactIndexV1Errors[keyof ViewContactIndexV1Errors]

export interface ViewContactIndexV1Responses {
  200: ViewContactIndexResponse
}

export type ViewContactIndexV1Response = ViewContactIndexV1Responses[keyof ViewContactIndexV1Responses]

export interface CreateContactV1Data {
  body: CreateContactCommand
  query?: never
  url: '/api/v1/contacts'
  path?: never
}

export interface CreateContactV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateContactV1Error = CreateContactV1Errors[keyof CreateContactV1Errors]

export interface CreateContactV1Responses {
  201: CreateContactResponse
}

export type CreateContactV1Response = CreateContactV1Responses[keyof CreateContactV1Responses]

export interface DeleteContactV1Data {
  body?: never
  query?: never
  url: '/api/v1/contacts/{uuid}'
  path: {
    uuid: string
  }
}

export interface DeleteContactV1Errors {
  401: unknown
  404: {
    traceId?: string | null
    errors?: Array<unknown>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DeleteContactV1Error = DeleteContactV1Errors[keyof DeleteContactV1Errors]

export interface DeleteContactV1Responses {
  204: void
}

export type DeleteContactV1Response = DeleteContactV1Responses[keyof DeleteContactV1Responses]

export interface UpdateContactV1Data {
  body: UpdateContactCommand
  query?: never
  url: '/api/v1/contacts/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateContactV1Errors {
  401: unknown
  404: {
    traceId?: string | null
    errors?: Array<unknown>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateContactV1Error = UpdateContactV1Errors[keyof UpdateContactV1Errors]

export interface UpdateContactV1Responses {
  204: void
}

export type UpdateContactV1Response = UpdateContactV1Responses[keyof UpdateContactV1Responses]

export interface ViewContainerTypeIndexV1Data {
  body?: never
  query: {
    filter: ViewContainerTypeIndexFilterQuery
    search?: string
  }
  url: '/api/v1/container-types'
  path?: never
}

export interface ViewContainerTypeIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewContainerTypeIndexV1Error = ViewContainerTypeIndexV1Errors[keyof ViewContainerTypeIndexV1Errors]

export interface ViewContainerTypeIndexV1Responses {
  200: ViewContainerTypeIndexResponse
}

export type ViewContainerTypeIndexV1Response = ViewContainerTypeIndexV1Responses[keyof ViewContainerTypeIndexV1Responses]

export interface ViewContractLineIndexV1Data {
  body?: never
  query: {
    filter: ViewContractLineIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    search?: string
    sort?: Array<ViewContractLineIndexSortQuery>
  }
  url: '/api/v1/contract-lines'
  path?: never
}

export interface ViewContractLineIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewContractLineIndexV1Error = ViewContractLineIndexV1Errors[keyof ViewContractLineIndexV1Errors]

export interface ViewContractLineIndexV1Responses {
  200: ViewContractLineIndexResponse
}

export type ViewContractLineIndexV1Response = ViewContractLineIndexV1Responses[keyof ViewContractLineIndexV1Responses]

export interface ViewWprContractLineIndexV1Data {
  body?: never
  query: {
    filter: ViewWprContractLineIndexFilterQuery
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/contract-lines/weekly-planning-requests'
  path?: never
}

export interface ViewWprContractLineIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWprContractLineIndexV1Error = ViewWprContractLineIndexV1Errors[keyof ViewWprContractLineIndexV1Errors]

export interface ViewWprContractLineIndexV1Responses {
  200: ViewWprContractLineIndexResponse
}

export type ViewWprContractLineIndexV1Response = ViewWprContractLineIndexV1Responses[keyof ViewWprContractLineIndexV1Responses]

export interface ViewPackagingRequestContractLineIndexV1Data {
  body?: never
  query: {
    filter: ViewPackagingRequestContractLineIndexFilterQuery
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/contract-lines/packaging-requests'
  path?: never
}

export interface ViewPackagingRequestContractLineIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPackagingRequestContractLineIndexV1Error = ViewPackagingRequestContractLineIndexV1Errors[keyof ViewPackagingRequestContractLineIndexV1Errors]

export interface ViewPackagingRequestContractLineIndexV1Responses {
  200: ViewPackagingRequestContractLineIndexResponse
}

export type ViewPackagingRequestContractLineIndexV1Response = ViewPackagingRequestContractLineIndexV1Responses[keyof ViewPackagingRequestContractLineIndexV1Responses]

export interface GenerateContractLinesPdfV1Data {
  body: GenerateContractLinesPdfCommand
  query?: never
  url: '/api/v1/contract-lines/generate-pdf'
  path?: never
}

export interface GenerateContractLinesPdfV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ContractLineNotAccessibleError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GenerateContractLinesPdfV1Error = GenerateContractLinesPdfV1Errors[keyof GenerateContractLinesPdfV1Errors]

export interface GenerateContractLinesPdfV1Responses {
  200: GenerateContractLinesPdfResponse
}

export type GenerateContractLinesPdfV1Response = GenerateContractLinesPdfV1Responses[keyof GenerateContractLinesPdfV1Responses]

export interface DownloadDocumentV1Data {
  body: DownloadDocumentCommand
  query?: never
  url: '/api/v1/documents/download'
  path?: never
}

export interface DownloadDocumentV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<DocumentNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadDocumentV1Error = DownloadDocumentV1Errors[keyof DownloadDocumentV1Errors]

export interface GetDocumentFiltersV1Data {
  body?: never
  query: {
    filter: GetDocumentFiltersFilterQuery
  }
  url: '/api/v1/documents/filters'
  path?: never
}

export interface GetDocumentFiltersV1Errors {
  403: {
    traceId?: string | null
    errors?: Array<ForbiddenError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetDocumentFiltersV1Error = GetDocumentFiltersV1Errors[keyof GetDocumentFiltersV1Errors]

export interface GetDocumentFiltersV1Responses {
  200: GetDocumentFiltersResponse
}

export type GetDocumentFiltersV1Response = GetDocumentFiltersV1Responses[keyof GetDocumentFiltersV1Responses]

export interface ViewDocumentIndexV1Data {
  body?: never
  query: {
    filter: ViewDocumentIndexFilterQuery
    pagination?: ViewDocumentIndexPaginationQuery
    /**
     * Searches on document name
     */
    search?: string
  }
  url: '/api/v1/documents'
  path?: never
}

export interface ViewDocumentIndexV1Errors {
  403: {
    traceId?: string | null
    errors?: Array<ForbiddenError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDocumentIndexV1Error = ViewDocumentIndexV1Errors[keyof ViewDocumentIndexV1Errors]

export interface ViewDocumentIndexV1Responses {
  200: ViewDocumentIndexResponse
}

export type ViewDocumentIndexV1Response = ViewDocumentIndexV1Responses[keyof ViewDocumentIndexV1Responses]

export interface ViewUserSiteIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/documents/sites'
  path?: never
}

export interface ViewUserSiteIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUserSiteIndexV1Error = ViewUserSiteIndexV1Errors[keyof ViewUserSiteIndexV1Errors]

export interface ViewUserSiteIndexV1Responses {
  200: Array<ViewUserSiteIndexResponse>
}

export type ViewUserSiteIndexV1Response = ViewUserSiteIndexV1Responses[keyof ViewUserSiteIndexV1Responses]

export interface ViewDynamicTableColumnIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/dynamic-tables/{name}/columns'
  path: {
    name: string
  }
}

export interface ViewDynamicTableColumnIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDynamicTableColumnIndexV1Error = ViewDynamicTableColumnIndexV1Errors[keyof ViewDynamicTableColumnIndexV1Errors]

export interface ViewDynamicTableColumnIndexV1Responses {
  200: DynamicTableIndexColumnResponse
}

export type ViewDynamicTableColumnIndexV1Response = ViewDynamicTableColumnIndexV1Responses[keyof ViewDynamicTableColumnIndexV1Responses]

export interface ViewDynamicTableViewIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/dynamic-tables/{name}/views'
  path: {
    name: string
  }
}

export interface ViewDynamicTableViewIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDynamicTableViewIndexV1Error = ViewDynamicTableViewIndexV1Errors[keyof ViewDynamicTableViewIndexV1Errors]

export interface ViewDynamicTableViewIndexV1Responses {
  200: DynamicTableViewIndexResponse
}

export type ViewDynamicTableViewIndexV1Response = ViewDynamicTableViewIndexV1Responses[keyof ViewDynamicTableViewIndexV1Responses]

export interface CreateDynamicTableViewV1Data {
  body: CreateDynamicTableViewCommand
  query?: never
  url: '/api/v1/dynamic-tables/{name}/views'
  path: {
    name: string
  }
}

export interface CreateDynamicTableViewV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ColumnNotFilterableError | ColumnNotFoundError | ColumnNotSortableError | DuplicateColumnError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateDynamicTableViewV1Error = CreateDynamicTableViewV1Errors[keyof CreateDynamicTableViewV1Errors]

export interface CreateDynamicTableViewV1Responses {
  201: CreateDynamicTableViewResponse
}

export type CreateDynamicTableViewV1Response = CreateDynamicTableViewV1Responses[keyof CreateDynamicTableViewV1Responses]

export interface ViewDefaultDynamicTableViewV1Data {
  body?: never
  query?: never
  url: '/api/v1/dynamic-tables/{name}/default-view'
  path: {
    name: string
  }
}

export interface ViewDefaultDynamicTableViewV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDefaultDynamicTableViewV1Error = ViewDefaultDynamicTableViewV1Errors[keyof ViewDefaultDynamicTableViewV1Errors]

export interface ViewDefaultDynamicTableViewV1Responses {
  200: ViewDefaultDynamicTableViewResponse
}

export type ViewDefaultDynamicTableViewV1Response = ViewDefaultDynamicTableViewV1Responses[keyof ViewDefaultDynamicTableViewV1Responses]

export interface DeleteDynamicTableViewV1Data {
  body?: never
  query?: never
  url: '/api/v1/dynamic-tables/{name}/views/{uuid}'
  path: {
    uuid: string
    name: string
  }
}

export interface DeleteDynamicTableViewV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<GlobalDefaultViewNotDeletable | LastGlobalViewNotDeletable>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DeleteDynamicTableViewV1Error = DeleteDynamicTableViewV1Errors[keyof DeleteDynamicTableViewV1Errors]

export interface DeleteDynamicTableViewV1Responses {
  200: unknown
}

export interface UpdateDynamicTableViewV1Data {
  body: UpdateDynamicTableViewCommand
  query?: never
  url: '/api/v1/dynamic-tables/{name}/views/{uuid}'
  path: {
    uuid: string
    name: string
  }
}

export interface UpdateDynamicTableViewV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ColumnNotFilterableError | ColumnNotFoundError | ColumnNotSortableError | DuplicateColumnError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateDynamicTableViewV1Error = UpdateDynamicTableViewV1Errors[keyof UpdateDynamicTableViewV1Errors]

export interface UpdateDynamicTableViewV1Responses {
  200: UpdateDynamicTableViewResponse
}

export type UpdateDynamicTableViewV1Response = UpdateDynamicTableViewV1Responses[keyof UpdateDynamicTableViewV1Responses]

export interface ViewEwcCodeIndexV1Data {
  body?: never
  query?: {
    search?: string
  }
  url: '/api/v1/ewc-codes'
  path?: never
}

export interface ViewEwcCodeIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewEwcCodeIndexV1Error = ViewEwcCodeIndexV1Errors[keyof ViewEwcCodeIndexV1Errors]

export interface ViewEwcCodeIndexV1Responses {
  200: ViewEwcCodeIndexResponse
}

export type ViewEwcCodeIndexV1Response = ViewEwcCodeIndexV1Responses[keyof ViewEwcCodeIndexV1Responses]

export interface DownloadGuidanceLetterSapV1Data {
  body?: never
  query: {
    type: DownloadGuidanceLetterType
  }
  url: '/api/v1/guidance-letters/{shipmentId}/download'
  path: {
    shipmentId: string
  }
}

export interface DownloadGuidanceLetterSapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<GuidanceLetterDownloadTypeNotFoundError | GuidanceLetterNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadGuidanceLetterSapV1Error = DownloadGuidanceLetterSapV1Errors[keyof DownloadGuidanceLetterSapV1Errors]

export interface DownloadGuidanceLetterSapV1Responses {
  200: unknown
}

export interface ViewGuidanceLetterIndexV1Data {
  body?: never
  query?: {
    filter?: ViewGuidanceLetterIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    sort?: Array<ViewGuidanceLetterIndexSortQuery>
  }
  url: '/api/v1/guidance-letters'
  path?: never
}

export interface ViewGuidanceLetterIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewGuidanceLetterIndexV1Error = ViewGuidanceLetterIndexV1Errors[keyof ViewGuidanceLetterIndexV1Errors]

export interface ViewGuidanceLetterIndexV1Responses {
  200: ViewGuidanceLetterIndexResponse
}

export type ViewGuidanceLetterIndexV1Response = ViewGuidanceLetterIndexV1Responses[keyof ViewGuidanceLetterIndexV1Responses]

export interface DownloadInvoiceCertificateV1Data {
  body: DownloadInvoiceCertificateCommand
  query?: never
  url: '/api/v1/invoices/certificates/download'
  path?: never
}

export interface DownloadInvoiceCertificateV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadInvoiceCertificateV1Error = DownloadInvoiceCertificateV1Errors[keyof DownloadInvoiceCertificateV1Errors]

export interface DownloadInvoiceCertificateV1Responses {
  200: unknown
}

export interface DownloadInvoiceV1Data {
  body?: never
  query?: never
  url: '/api/v1/invoices/{invoiceNumber}/download'
  path: {
    invoiceNumber: string
  }
}

export interface DownloadInvoiceV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<InvoiceDocumentNotFoundError | InvoiceNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadInvoiceV1Error = DownloadInvoiceV1Errors[keyof DownloadInvoiceV1Errors]

export interface DownloadInvoiceV1Responses {
  200: unknown
}

export interface ExportInvoicesExcelV1Data {
  body?: never
  query: {
    filter: ExportInvoicesExcelFilterQuery
    pagination?: PaginatedOffsetQuery
    search?: string
    sort?: Array<ViewInvoiceIndexSortQuery>
  }
  url: '/api/v1/invoices/export-excel'
  path?: never
}

export interface ExportInvoicesExcelV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ExportInvoicesExcelV1Error = ExportInvoicesExcelV1Errors[keyof ExportInvoicesExcelV1Errors]

export interface ExportInvoicesExcelV1Responses {
  /**
   * Invoices exported as Excel
   */
  200: unknown
}

export interface ViewInvoiceIndexV1Data {
  body?: never
  query: {
    filter: ViewInvoiceIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    search?: string
    sort?: Array<ViewInvoiceIndexSortQuery>
  }
  url: '/api/v1/invoices'
  path?: never
}

export interface ViewInvoiceIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewInvoiceIndexV1Error = ViewInvoiceIndexV1Errors[keyof ViewInvoiceIndexV1Errors]

export interface ViewInvoiceIndexV1Responses {
  200: ViewInvoiceIndexResponse
}

export type ViewInvoiceIndexV1Response = ViewInvoiceIndexV1Responses[keyof ViewInvoiceIndexV1Responses]

export interface ViewInvoiceDetailV1Data {
  body?: never
  query?: never
  url: '/api/v1/invoices/{requestNumber}'
  path: {
    requestNumber: string
  }
}

export interface ViewInvoiceDetailV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewInvoiceDetailV1Error = ViewInvoiceDetailV1Errors[keyof ViewInvoiceDetailV1Errors]

export interface ViewInvoiceDetailV1Responses {
  200: ViewInvoiceResponse
}

export type ViewInvoiceDetailV1Response = ViewInvoiceDetailV1Responses[keyof ViewInvoiceDetailV1Responses]

export interface ViewDraftInvoiceDetailV1Data {
  body?: never
  query?: never
  url: '/api/v1/draft-invoices/{invoiceNumber}'
  path: {
    invoiceNumber: string
  }
}

export interface ViewDraftInvoiceDetailV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<InvoiceNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDraftInvoiceDetailV1Error = ViewDraftInvoiceDetailV1Errors[keyof ViewDraftInvoiceDetailV1Errors]

export interface ViewDraftInvoiceDetailV1Responses {
  200: ViewDraftInvoiceDetailResponse
}

export type ViewDraftInvoiceDetailV1Response = ViewDraftInvoiceDetailV1Responses[keyof ViewDraftInvoiceDetailV1Responses]

export interface ViewDraftInvoiceIndexV1Data {
  body?: never
  query: {
    filter: ViewDraftInvoiceIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    sort?: Array<ViewDraftInvoiceIndexSortQuery>
  }
  url: '/api/v1/draft-invoices'
  path?: never
}

export interface ViewDraftInvoiceIndexV1Errors {
  403: {
    traceId?: string | null
    errors?: Array<ForbiddenError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDraftInvoiceIndexV1Error = ViewDraftInvoiceIndexV1Errors[keyof ViewDraftInvoiceIndexV1Errors]

export interface ViewDraftInvoiceIndexV1Responses {
  200: ViewDraftInvoiceIndexResponse
}

export type ViewDraftInvoiceIndexV1Response = ViewDraftInvoiceIndexV1Responses[keyof ViewDraftInvoiceIndexV1Responses]

export interface ExportDraftInvoicesExcelV1Data {
  body?: never
  query: {
    filter: ExportDraftInvoicesExcelFilterQuery
    pagination?: PaginatedOffsetQuery
    sort?: Array<ViewDraftInvoiceIndexSortQuery>
  }
  url: '/api/v1/draft-invoices/export-excel'
  path?: never
}

export interface ExportDraftInvoicesExcelV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ExportDraftInvoicesExcelV1Error = ExportDraftInvoicesExcelV1Errors[keyof ExportDraftInvoicesExcelV1Errors]

export interface ExportDraftInvoicesExcelV1Responses {
  /**
   * draft invoices exported as Excel
   */
  200: unknown
}

export interface ApproveDraftInvoiceV1Data {
  body: ApproveDraftInvoiceCommand
  query?: never
  url: '/api/v1/draft-invoices/{invoiceNumber}/approve'
  path: {
    invoiceNumber: string
  }
}

export interface ApproveDraftInvoiceV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<InvoiceNotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<NonApproveOrRejectableDraftInvoiceError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ApproveDraftInvoiceV1Error = ApproveDraftInvoiceV1Errors[keyof ApproveDraftInvoiceV1Errors]

export interface ApproveDraftInvoiceV1Responses {
  200: unknown
}

export interface RejectDraftInvoiceV1Data {
  body: RejectDraftInvoiceCommand
  query?: never
  url: '/api/v1/draft-invoices/{invoiceNumber}/reject'
  path: {
    invoiceNumber: string
  }
}

export interface RejectDraftInvoiceV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<InvoiceNotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<NonApproveOrRejectableDraftInvoiceError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type RejectDraftInvoiceV1Error = RejectDraftInvoiceV1Errors[keyof RejectDraftInvoiceV1Errors]

export interface RejectDraftInvoiceV1Responses {
  200: unknown
}

export interface SubscribeToNewsletterV1Data {
  body: SubscribeToNewsletterCommand
  query?: never
  url: '/api/v1/newsletters/subscribe'
  path?: never
}

export interface SubscribeToNewsletterV1Errors {
  409: {
    traceId?: string | null
    errors?: Array<AlreadySubscribedError | OptInAlreadyRequestedError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubscribeToNewsletterV1Error = SubscribeToNewsletterV1Errors[keyof SubscribeToNewsletterV1Errors]

export interface SubscribeToNewsletterV1Responses {
  204: void
}

export type SubscribeToNewsletterV1Response = SubscribeToNewsletterV1Responses[keyof SubscribeToNewsletterV1Responses]

export interface ViewNewsItemIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/news-items'
  path?: never
}

export interface ViewNewsItemIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewNewsItemIndexV1Error = ViewNewsItemIndexV1Errors[keyof ViewNewsItemIndexV1Errors]

export interface ViewNewsItemIndexV1Responses {
  200: ViewNewsIndexResponse
}

export type ViewNewsItemIndexV1Response = ViewNewsItemIndexV1Responses[keyof ViewNewsItemIndexV1Responses]

export interface CreateNewsItemV1Data {
  body: CreateNewsItemCommand
  query?: never
  url: '/api/v1/news-items'
  path?: never
}

export interface CreateNewsItemV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateNewsItemV1Error = CreateNewsItemV1Errors[keyof CreateNewsItemV1Errors]

export interface CreateNewsItemV1Responses {
  201: CreateNewsItemResponse
}

export type CreateNewsItemV1Response = CreateNewsItemV1Responses[keyof CreateNewsItemV1Responses]

export interface DeleteNewsItemV1Data {
  body?: never
  query?: never
  url: '/api/v1/news-items/{uuid}'
  path: {
    uuid: string
  }
}

export interface DeleteNewsItemV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DeleteNewsItemV1Error = DeleteNewsItemV1Errors[keyof DeleteNewsItemV1Errors]

export interface DeleteNewsItemV1Responses {
  200: unknown
}

export interface ViewNewsItemV1Data {
  body?: never
  query?: never
  url: '/api/v1/news-items/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewNewsItemV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NewsItemNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewNewsItemV1Error = ViewNewsItemV1Errors[keyof ViewNewsItemV1Errors]

export interface ViewNewsItemV1Responses {
  200: ViewNewsItemResponse
}

export type ViewNewsItemV1Response = ViewNewsItemV1Responses[keyof ViewNewsItemV1Responses]

export interface UpdateNewsItemV1Data {
  body: UpdateNewsItemCommand
  query?: never
  url: '/api/v1/news-items/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateNewsItemV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<NewsItemTranslationExistsError | NoStartDateOrEndDateExpectedError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateNewsItemV1Error = UpdateNewsItemV1Errors[keyof UpdateNewsItemV1Errors]

export interface UpdateNewsItemV1Responses {
  200: UpdateNewsItemResponse
}

export type UpdateNewsItemV1Response = UpdateNewsItemV1Responses[keyof UpdateNewsItemV1Responses]

export interface ViewDashboardNewsItemIndexV1Data {
  body?: never
  query?: {
    filter?: ViewDashboardNewItemFilterQuery
    pagination?: PaginatedOffsetQuery
  }
  url: '/api/v1/dashboard-news-items'
  path?: never
}

export interface ViewDashboardNewsItemIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDashboardNewsItemIndexV1Error = ViewDashboardNewsItemIndexV1Errors[keyof ViewDashboardNewsItemIndexV1Errors]

export interface ViewDashboardNewsItemIndexV1Responses {
  200: ViewDashboardNewsIndexResponse
}

export type ViewDashboardNewsItemIndexV1Response = ViewDashboardNewsItemIndexV1Responses[keyof ViewDashboardNewsItemIndexV1Responses]

export interface ViewDashboardNewsItemV1Data {
  body?: never
  query?: never
  url: '/api/v1/dashboard-news-items/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewDashboardNewsItemV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewDashboardNewsItemV1Error = ViewDashboardNewsItemV1Errors[keyof ViewDashboardNewsItemV1Errors]

export interface ViewDashboardNewsItemV1Responses {
  200: ViewDashboardNewsItemResponse
}

export type ViewDashboardNewsItemV1Response = ViewDashboardNewsItemV1Responses[keyof ViewDashboardNewsItemV1Responses]

export interface CreatePackagingRequestV1Data {
  body: CreatePackagingRequestCommand
  query?: never
  url: '/api/v1/packaging-requests'
  path?: never
}

export interface CreatePackagingRequestV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreatePackagingRequestV1Error = CreatePackagingRequestV1Errors[keyof CreatePackagingRequestV1Errors]

export interface CreatePackagingRequestV1Responses {
  201: CreatePackagingRequestResponse
}

export type CreatePackagingRequestV1Response = CreatePackagingRequestV1Responses[keyof CreatePackagingRequestV1Responses]

export interface ViewPackagingRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/packaging-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewPackagingRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<CustomerNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPackagingRequestV1Error = ViewPackagingRequestV1Errors[keyof ViewPackagingRequestV1Errors]

export interface ViewPackagingRequestV1Responses {
  200: ViewPackagingRequestResponse
}

export type ViewPackagingRequestV1Response = ViewPackagingRequestV1Responses[keyof ViewPackagingRequestV1Responses]

export interface UpdatePackagingRequestV1Data {
  body: UpdatePackagingRequestCommand
  query?: never
  url: '/api/v1/packaging-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdatePackagingRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<CustomerNotAccessibleError | FieldMustBeNullError | MissingRequiredFieldError | PickUpAddressNotAccessibleError | WasteProducerNotAccessibleError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdatePackagingRequestV1Error = UpdatePackagingRequestV1Errors[keyof UpdatePackagingRequestV1Errors]

export interface UpdatePackagingRequestV1Responses {
  200: UpdatePackagingRequestResponse
}

export type UpdatePackagingRequestV1Response = UpdatePackagingRequestV1Responses[keyof UpdatePackagingRequestV1Responses]

export interface ViewWasteProducerIndexV1Data {
  body?: never
  query?: {
    filter?: ViewWasteProducerIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/waste-producers'
  path?: never
}

export interface ViewWasteProducerIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWasteProducerIndexV1Error = ViewWasteProducerIndexV1Errors[keyof ViewWasteProducerIndexV1Errors]

export interface ViewWasteProducerIndexV1Responses {
  200: ViewWasteProducerIndexResponse
}

export type ViewWasteProducerIndexV1Response = ViewWasteProducerIndexV1Responses[keyof ViewWasteProducerIndexV1Responses]

export interface ViewSuggestedWasteProducersV1Data {
  body?: never
  query?: {
    filter?: ViewSuggestedWasteProducersFilterQuery
  }
  url: '/api/v1/suggested-waste-producers'
  path?: never
}

export interface ViewSuggestedWasteProducersV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewSuggestedWasteProducersV1Error = ViewSuggestedWasteProducersV1Errors[keyof ViewSuggestedWasteProducersV1Errors]

export interface ViewSuggestedWasteProducersV1Responses {
  200: ViewSuggestedWasteProducersResponse
}

export type ViewSuggestedWasteProducersV1Response = ViewSuggestedWasteProducersV1Responses[keyof ViewSuggestedWasteProducersV1Responses]

export interface ViewPickUpAddressIndexV1Data {
  body?: never
  query?: {
    filter?: ViewPickUpAddressIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/pick-up-addresses'
  path?: never
}

export interface ViewPickUpAddressIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpAddressIndexV1Error = ViewPickUpAddressIndexV1Errors[keyof ViewPickUpAddressIndexV1Errors]

export interface ViewPickUpAddressIndexV1Responses {
  200: ViewPickUpAddressIndexResponse
}

export type ViewPickUpAddressIndexV1Response = ViewPickUpAddressIndexV1Responses[keyof ViewPickUpAddressIndexV1Responses]

export interface ViewSuggestedPickUpAddressesV1Data {
  body?: never
  query?: {
    filter?: ViewSuggestedPickUpAddressesFilterQuery
  }
  url: '/api/v1/suggested-pick-up-addresses'
  path?: never
}

export interface ViewSuggestedPickUpAddressesV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewSuggestedPickUpAddressesV1Error = ViewSuggestedPickUpAddressesV1Errors[keyof ViewSuggestedPickUpAddressesV1Errors]

export interface ViewSuggestedPickUpAddressesV1Responses {
  200: ViewSuggestedPickUpAddressesResponse
}

export type ViewSuggestedPickUpAddressesV1Response = ViewSuggestedPickUpAddressesV1Responses[keyof ViewSuggestedPickUpAddressesV1Responses]

export interface SubmitPackagingRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/packaging-requests/{uuid}/submit'
  path: {
    uuid: string
  }
}

export interface SubmitPackagingRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError | UpdateOnlyPackagingRequestError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<PackagingRequestAlreadySubmitted>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubmitPackagingRequestV1Error = SubmitPackagingRequestV1Errors[keyof SubmitPackagingRequestV1Errors]

export interface SubmitPackagingRequestV1Responses {
  /**
   * Packaging request submitted
   */
  200: SubmitPackagingRequestResponse
}

export type SubmitPackagingRequestV1Response = SubmitPackagingRequestV1Responses[keyof SubmitPackagingRequestV1Responses]

export interface BulkDeletePackagingRequestV1Data {
  body: BulkDeletePackagingRequestCommand
  query?: never
  url: '/api/v1/packaging-requests/bulk'
  path?: never
}

export interface BulkDeletePackagingRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type BulkDeletePackagingRequestV1Error = BulkDeletePackagingRequestV1Errors[keyof BulkDeletePackagingRequestV1Errors]

export interface BulkDeletePackagingRequestV1Responses {
  200: unknown
}

export interface CopyPackagingRequestSapV1Data {
  body?: never
  query?: never
  url: '/api/v1/packaging-requests/sap/{requestNumber}/copy'
  path: {
    requestNumber: string
  }
}

export interface CopyPackagingRequestSapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<CopyNonSubmittedPickUpRequestError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CopyPackagingRequestSapV1Error = CopyPackagingRequestSapV1Errors[keyof CopyPackagingRequestSapV1Errors]

export interface CopyPackagingRequestSapV1Responses {
  201: CopyPackagingRequestSapResponse
}

export type CopyPackagingRequestSapV1Response = CopyPackagingRequestSapV1Responses[keyof CopyPackagingRequestSapV1Responses]

export interface ViewPackagingTypeIndexV1Data {
  body?: never
  query: {
    filter: ViewPackagingTypeIndexFilterQuery
    search?: string
  }
  url: '/api/v1/packaging-types'
  path?: never
}

export interface ViewPackagingTypeIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPackagingTypeIndexV1Error = ViewPackagingTypeIndexV1Errors[keyof ViewPackagingTypeIndexV1Errors]

export interface ViewPackagingTypeIndexV1Responses {
  200: ViewPackagingTypeIndexResponse
}

export type ViewPackagingTypeIndexV1Response = ViewPackagingTypeIndexV1Responses[keyof ViewPackagingTypeIndexV1Responses]

export interface ViewPayerIndexV1Data {
  body?: never
  query: {
    filter: ViewPayerIndexFilterQuery
  }
  url: '/api/v1/payers'
  path?: never
}

export interface ViewPayerIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPayerIndexV1Error = ViewPayerIndexV1Errors[keyof ViewPayerIndexV1Errors]

export interface ViewPayerIndexV1Responses {
  200: ViewPayerIndexResponse
}

export type ViewPayerIndexV1Response = ViewPayerIndexV1Responses[keyof ViewPayerIndexV1Responses]

export interface GetIsPoNumberAndCostCenterRequiredV1Data {
  body?: never
  query: {
    filter: GetIsPoNumberAndCostCenterRequiredFilterQuery
  }
  url: '/api/v1/pick-up-requests/is-po-number-and-cost-center-required'
  path?: never
}

export interface GetIsPoNumberAndCostCenterRequiredV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type GetIsPoNumberAndCostCenterRequiredV1Error = GetIsPoNumberAndCostCenterRequiredV1Errors[keyof GetIsPoNumberAndCostCenterRequiredV1Errors]

export interface GetIsPoNumberAndCostCenterRequiredV1Responses {
  /**
   * Returns whether PO number and cost center are required
   */
  200: GetIsPoNumberAndCostCenterRequiredResponse
}

export type GetIsPoNumberAndCostCenterRequiredV1Response = GetIsPoNumberAndCostCenterRequiredV1Responses[keyof GetIsPoNumberAndCostCenterRequiredV1Responses]

export interface ViewPickUpRequestIndexV1Data {
  body?: never
  query: {
    filter: ViewPickUpRequestIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    sort?: Array<ViewPickUpRequestIndexSortQuery>
  }
  url: '/api/v1/pick-up-requests'
  path?: never
}

export interface ViewPickUpRequestIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpRequestIndexV1Error = ViewPickUpRequestIndexV1Errors[keyof ViewPickUpRequestIndexV1Errors]

export interface ViewPickUpRequestIndexV1Responses {
  200: ViewPickUpRequestIndexResponse
}

export type ViewPickUpRequestIndexV1Response = ViewPickUpRequestIndexV1Responses[keyof ViewPickUpRequestIndexV1Responses]

export interface CreatePickUpRequestV1Data {
  body: CreatePickUpRequestCommand
  query?: never
  url: '/api/v1/pick-up-requests'
  path?: never
}

export interface CreatePickUpRequestV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreatePickUpRequestV1Error = CreatePickUpRequestV1Errors[keyof CreatePickUpRequestV1Errors]

export interface CreatePickUpRequestV1Responses {
  201: CreatePickUpRequestResponse
}

export type CreatePickUpRequestV1Response = CreatePickUpRequestV1Responses[keyof CreatePickUpRequestV1Responses]

export interface ViewPickUpRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewPickUpRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpRequestV1Error = ViewPickUpRequestV1Errors[keyof ViewPickUpRequestV1Errors]

export interface ViewPickUpRequestV1Responses {
  200: ViewPickUpRequestResponse
}

export type ViewPickUpRequestV1Response = ViewPickUpRequestV1Responses[keyof ViewPickUpRequestV1Responses]

export interface UpdatePickUpRequestV1Data {
  body: UpdatePickUpRequestCommand
  query?: never
  url: '/api/v1/pick-up-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdatePickUpRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<CustomerNotAccessibleError | MissingEwcLevelsError>
  }
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdatePickUpRequestV1Error = UpdatePickUpRequestV1Errors[keyof UpdatePickUpRequestV1Errors]

export interface UpdatePickUpRequestV1Responses {
  200: UpdatePickUpRequestResponse
}

export type UpdatePickUpRequestV1Response = UpdatePickUpRequestV1Responses[keyof UpdatePickUpRequestV1Responses]

export interface SubmitPickUpRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-requests/{uuid}/submit'
  path: {
    uuid: string
  }
}

export interface SubmitPickUpRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<PickUpRequestAlreadySubmitted>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubmitPickUpRequestV1Error = SubmitPickUpRequestV1Errors[keyof SubmitPickUpRequestV1Errors]

export interface SubmitPickUpRequestV1Responses {
  /**
   * Pick up request submitted
   */
  200: SubmitPickUpRequestResponse
}

export type SubmitPickUpRequestV1Response = SubmitPickUpRequestV1Responses[keyof SubmitPickUpRequestV1Responses]

export interface BulkDeletePickUpRequestV1Data {
  body: BulkDeletePickUpRequestCommand
  query?: never
  url: '/api/v1/pick-up-requests/bulk'
  path?: never
}

export interface BulkDeletePickUpRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type BulkDeletePickUpRequestV1Error = BulkDeletePickUpRequestV1Errors[keyof BulkDeletePickUpRequestV1Errors]

export interface BulkDeletePickUpRequestV1Responses {
  200: unknown
}

export interface ViewPickUpRequestSapV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}'
  path: {
    requestNumber: string
  }
}

export interface ViewPickUpRequestSapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<unknown>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpRequestSapV1Error = ViewPickUpRequestSapV1Errors[keyof ViewPickUpRequestSapV1Errors]

export interface ViewPickUpRequestSapV1Responses {
  200: ViewPickUpRequestSapResponse
}

export type ViewPickUpRequestSapV1Response = ViewPickUpRequestSapV1Responses[keyof ViewPickUpRequestSapV1Responses]

export interface UpdatePickUpRequestSapV1Data {
  body: UpdatePickUpRequestSapCommand
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}'
  path: {
    requestNumber: string
  }
}

export interface UpdatePickUpRequestSapV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<InvalidUpdateSapPickUpRequestError>
  }
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestContractLineNotFoundError | PickUpRequestNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdatePickUpRequestSapV1Error = UpdatePickUpRequestSapV1Errors[keyof UpdatePickUpRequestSapV1Errors]

export interface UpdatePickUpRequestSapV1Responses {
  200: unknown
}

export interface ViewPickUpRequestTemplateIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
    search?: string
    sort?: Array<ViewPickUpRequestTemplateIndexSortQuery>
  }
  url: '/api/v1/pick-up-request-templates'
  path?: never
}

export interface ViewPickUpRequestTemplateIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpRequestTemplateIndexV1Error = ViewPickUpRequestTemplateIndexV1Errors[keyof ViewPickUpRequestTemplateIndexV1Errors]

export interface ViewPickUpRequestTemplateIndexV1Responses {
  200: ViewPickUpRequestTemplateIndexResponse
}

export type ViewPickUpRequestTemplateIndexV1Response = ViewPickUpRequestTemplateIndexV1Responses[keyof ViewPickUpRequestTemplateIndexV1Responses]

export interface CreatePickUpRequestTemplateV1Data {
  body: CreatePickUpRequestTemplateCommand
  query?: never
  url: '/api/v1/pick-up-request-templates'
  path?: never
}

export interface CreatePickUpRequestTemplateV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreatePickUpRequestTemplateV1Error = CreatePickUpRequestTemplateV1Errors[keyof CreatePickUpRequestTemplateV1Errors]

export interface CreatePickUpRequestTemplateV1Responses {
  201: CreatePickUpRequestTemplateResponse
}

export type CreatePickUpRequestTemplateV1Response = CreatePickUpRequestTemplateV1Responses[keyof CreatePickUpRequestTemplateV1Responses]

export interface ViewPickUpRequestTemplateV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-request-templates/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewPickUpRequestTemplateV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewPickUpRequestTemplateV1Error = ViewPickUpRequestTemplateV1Errors[keyof ViewPickUpRequestTemplateV1Errors]

export interface ViewPickUpRequestTemplateV1Responses {
  200: ViewPickUpRequestTemplateResponse
}

export type ViewPickUpRequestTemplateV1Response = ViewPickUpRequestTemplateV1Responses[keyof ViewPickUpRequestTemplateV1Responses]

export interface UpdatePickUpRequestTemplateV1Data {
  body: UpdatePickUpRequestTemplateCommand
  query?: never
  url: '/api/v1/pick-up-request-templates/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdatePickUpRequestTemplateV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<CustomerNotAccessibleError | MissingEwcLevelsError>
  }
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdatePickUpRequestTemplateV1Error = UpdatePickUpRequestTemplateV1Errors[keyof UpdatePickUpRequestTemplateV1Errors]

export interface UpdatePickUpRequestTemplateV1Responses {
  200: UpdatePickUpRequestTemplateResponse
}

export type UpdatePickUpRequestTemplateV1Response = UpdatePickUpRequestTemplateV1Responses[keyof UpdatePickUpRequestTemplateV1Responses]

export interface BulkDeletePickUpRequestTemplatesV1Data {
  body: BulkDeletePickUpRequestTemplatesCommand
  query?: never
  url: '/api/v1/pick-up-request-templates/bulk'
  path?: never
}

export interface BulkDeletePickUpRequestTemplatesV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type BulkDeletePickUpRequestTemplatesV1Error = BulkDeletePickUpRequestTemplatesV1Errors[keyof BulkDeletePickUpRequestTemplatesV1Errors]

export interface BulkDeletePickUpRequestTemplatesV1Responses {
  200: unknown
}

export interface CreatePickUpRequestFromTemplateV1Data {
  body: CreatePickUpRequestFromTemplateCommand
  query?: never
  url: '/api/v1/pick-up-request-templates/{uuid}/create-pick-up-request'
  path: {
    uuid: string
  }
}

export interface CreatePickUpRequestFromTemplateV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreatePickUpRequestFromTemplateV1Error = CreatePickUpRequestFromTemplateV1Errors[keyof CreatePickUpRequestFromTemplateV1Errors]

export interface CreatePickUpRequestFromTemplateV1Responses {
  201: CreatePickUpRequestFromTemplateResponse
}

export type CreatePickUpRequestFromTemplateV1Response = CreatePickUpRequestFromTemplateV1Responses[keyof CreatePickUpRequestFromTemplateV1Responses]

export interface UploadDocumentSubmittedPickUpRequestV1Data {
  body: UploadDocumentSubmittedPickUpRequestCommand
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}/documents/upload'
  path: {
    requestNumber: string
  }
}

export interface UploadDocumentSubmittedPickUpRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<FileNotFoundError | NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UploadDocumentSubmittedPickUpRequestV1Error = UploadDocumentSubmittedPickUpRequestV1Errors[keyof UploadDocumentSubmittedPickUpRequestV1Errors]

export interface UploadDocumentSubmittedPickUpRequestV1Responses {
  /**
   * Documents uploaded to the submitted pick-up request
   */
  200: unknown
}

export interface DownloadDocumentSubmittedPickUpRequestV1Data {
  body: DownloadDocumentSubmittedPickUpRequestCommand
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}/documents/download'
  path: {
    requestNumber: string
  }
}

export interface DownloadDocumentSubmittedPickUpRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<FileNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadDocumentSubmittedPickUpRequestV1Error = DownloadDocumentSubmittedPickUpRequestV1Errors[keyof DownloadDocumentSubmittedPickUpRequestV1Errors]

export interface DownloadDocumentSubmittedPickUpRequestV1Responses {
  200: unknown
}

export interface CopyPickUpRequestSapV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}/copy'
  path: {
    requestNumber: string
  }
}

export interface CopyPickUpRequestSapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<CopyNonSubmittedPickUpRequestError | InvalidPickUpRequestCopyError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CopyPickUpRequestSapV1Error = CopyPickUpRequestSapV1Errors[keyof CopyPickUpRequestSapV1Errors]

export interface CopyPickUpRequestSapV1Responses {
  201: CopyPickUpRequestSapResponse
}

export type CopyPickUpRequestSapV1Response = CopyPickUpRequestSapV1Responses[keyof CopyPickUpRequestSapV1Responses]

export interface SubmitPickUpRequestSapV1Data {
  body?: never
  query?: never
  url: '/api/v1/pick-up-requests/sap/{requestNumber}/submit'
  path: {
    requestNumber: string
  }
}

export interface SubmitPickUpRequestSapV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<DateMustBeAfterError | MissingRequiredFieldError>
  }
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<InvalidIndascanSubmitStatusError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubmitPickUpRequestSapV1Error = SubmitPickUpRequestSapV1Errors[keyof SubmitPickUpRequestSapV1Errors]

export interface SubmitPickUpRequestSapV1Responses {
  200: unknown
}

export interface ViewSalesOrganisationIndexV1Data {
  body?: never
  query?: {
    pagination?: PaginatedOffsetQuery
    search?: string
  }
  url: '/api/v1/sales-organisations'
  path?: never
}

export interface ViewSalesOrganisationIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewSalesOrganisationIndexV1Error = ViewSalesOrganisationIndexV1Errors[keyof ViewSalesOrganisationIndexV1Errors]

export interface ViewSalesOrganisationIndexV1Responses {
  200: ViewSalesOrganisationIndexResponse
}

export type ViewSalesOrganisationIndexV1Response = ViewSalesOrganisationIndexV1Responses[keyof ViewSalesOrganisationIndexV1Responses]

export interface ViewTankerTypeIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/tanker-types'
  path?: never
}

export interface ViewTankerTypeIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewTankerTypeIndexV1Error = ViewTankerTypeIndexV1Errors[keyof ViewTankerTypeIndexV1Errors]

export interface ViewTankerTypeIndexV1Responses {
  200: ViewTankerTypeIndexResponse
}

export type ViewTankerTypeIndexV1Response = ViewTankerTypeIndexV1Responses[keyof ViewTankerTypeIndexV1Responses]

export interface ViewTransportTypeIndexV1Data {
  body?: never
  query?: never
  url: '/api/v1/transport-types'
  path?: never
}

export interface ViewTransportTypeIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewTransportTypeIndexV1Error = ViewTransportTypeIndexV1Errors[keyof ViewTransportTypeIndexV1Errors]

export interface ViewTransportTypeIndexV1Responses {
  200: ViewTransportTypeIndexResponse
}

export type ViewTransportTypeIndexV1Response = ViewTransportTypeIndexV1Responses[keyof ViewTransportTypeIndexV1Responses]

export interface ViewUnNumberIndexV1Data {
  body?: never
  query?: {
    filter?: ViewUnNumberFilterQuery
    pagination?: ViewUnNumberIndexPaginationQuery
    search?: string
  }
  url: '/api/v1/un-numbers'
  path?: never
}

export interface ViewUnNumberIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUnNumberIndexV1Error = ViewUnNumberIndexV1Errors[keyof ViewUnNumberIndexV1Errors]

export interface ViewUnNumberIndexV1Responses {
  200: ViewUnNumberIndexResponse
}

export type ViewUnNumberIndexV1Response = ViewUnNumberIndexV1Responses[keyof ViewUnNumberIndexV1Responses]

export interface ViewUnNumberIndexForPickUpRequestV1Data {
  body?: never
  query: {
    filter: ViewUnNumberIndexForPickUpRequestFilterQuery
    search?: string
  }
  url: '/api/v1/un-numbers-pick-up-request'
  path?: never
}

export interface ViewUnNumberIndexForPickUpRequestV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewUnNumberIndexForPickUpRequestV1Error = ViewUnNumberIndexForPickUpRequestV1Errors[keyof ViewUnNumberIndexForPickUpRequestV1Errors]

export interface ViewUnNumberIndexForPickUpRequestV1Responses {
  200: ViewUnNumberIndexForPickUpRequestResponse
}

export type ViewUnNumberIndexForPickUpRequestV1Response = ViewUnNumberIndexForPickUpRequestV1Responses[keyof ViewUnNumberIndexForPickUpRequestV1Responses]

export interface ViewWasteInquiryIndexV1Data {
  body?: never
  query: {
    filter: ViewWasteInquiryIndexFilterQuery
    pagination?: PaginatedOffsetQuery
    sort?: Array<ViewWasteInquiryIndexSortQuery>
  }
  url: '/api/v1/waste-inquiries'
  path?: never
}

export interface ViewWasteInquiryIndexV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWasteInquiryIndexV1Error = ViewWasteInquiryIndexV1Errors[keyof ViewWasteInquiryIndexV1Errors]

export interface ViewWasteInquiryIndexV1Responses {
  200: ViewWasteInquiryIndexResponse
}

export type ViewWasteInquiryIndexV1Response = ViewWasteInquiryIndexV1Responses[keyof ViewWasteInquiryIndexV1Responses]

export interface CreateWasteInquiryV1Data {
  body: CreateWasteInquiryCommand
  query?: never
  url: '/api/v1/waste-inquiries'
  path?: never
}

export interface CreateWasteInquiryV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateWasteInquiryV1Error = CreateWasteInquiryV1Errors[keyof CreateWasteInquiryV1Errors]

export interface CreateWasteInquiryV1Responses {
  201: CreateWasteInquiryResponse
}

export type CreateWasteInquiryV1Response = CreateWasteInquiryV1Responses[keyof CreateWasteInquiryV1Responses]

export interface DownloadWasteInquirySummarySapV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/summary'
  path: {
    inquiryNumber: string
  }
}

export interface DownloadWasteInquirySummarySapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<WasteInquiryNotFoundError | WasteInquirySummaryDocumentNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadWasteInquirySummarySapV1Error = DownloadWasteInquirySummarySapV1Errors[keyof DownloadWasteInquirySummarySapV1Errors]

export interface DownloadWasteInquirySummarySapV1Responses {
  200: unknown
}

export interface DownloadWasteInquiryDocumentSapV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/documents/{sapFileUuid}'
  path: {
    sapFileUuid: string
    inquiryNumber: string
  }
}

export interface DownloadWasteInquiryDocumentSapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<SapFileNotFoundError | WasteInquiryDocumentNotFoundError | WasteInquiryNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type DownloadWasteInquiryDocumentSapV1Error = DownloadWasteInquiryDocumentSapV1Errors[keyof DownloadWasteInquiryDocumentSapV1Errors]

export interface DownloadWasteInquiryDocumentSapV1Responses {
  200: unknown
}

export interface ViewWasteInquiryV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewWasteInquiryV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWasteInquiryV1Error = ViewWasteInquiryV1Errors[keyof ViewWasteInquiryV1Errors]

export interface ViewWasteInquiryV1Responses {
  200: ViewWasteInquiryResponse
}

export type ViewWasteInquiryV1Response = ViewWasteInquiryV1Responses[keyof ViewWasteInquiryV1Responses]

export interface UpdateWasteInquiryV1Data {
  body: UpdateWasteInquiryCommand
  query?: never
  url: '/api/v1/waste-inquiries/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateWasteInquiryV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<CustomerNotAccessibleError | EwcCodeNotFound | FileNotAccessibleError | InvalidStableTemperatureError | MissingEwcLevelsError | NoAnalysisReportFilesExpected | NoOptionExpectedWhenNoneSelected | NoSdsFilesExpected | NoSvhcExtraExpected>
  }
  404: {
    traceId?: string | null
    errors?: Array<PickUpRequestNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateWasteInquiryV1Error = UpdateWasteInquiryV1Errors[keyof UpdateWasteInquiryV1Errors]

export interface UpdateWasteInquiryV1Responses {
  200: UpdateWasteInquiryResponse
}

export type UpdateWasteInquiryV1Response = UpdateWasteInquiryV1Responses[keyof UpdateWasteInquiryV1Responses]

export interface SubmitWasteInquiryV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/{uuid}/submit'
  path: {
    uuid: string
  }
}

export interface SubmitWasteInquiryV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<WasteInquiryAlreadySubmitted>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubmitWasteInquiryV1Error = SubmitWasteInquiryV1Errors[keyof SubmitWasteInquiryV1Errors]

export interface SubmitWasteInquiryV1Responses {
  /**
   * Waste inquiry submitted
   */
  200: SubmitWasteInquiryResponse
}

export type SubmitWasteInquiryV1Response = SubmitWasteInquiryV1Responses[keyof SubmitWasteInquiryV1Responses]

export interface ViewWasteInquirySapV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/sap/{inquiryNumber}'
  path: {
    inquiryNumber: string
  }
}

export interface ViewWasteInquirySapV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<unknown>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWasteInquirySapV1Error = ViewWasteInquirySapV1Errors[keyof ViewWasteInquirySapV1Errors]

export interface ViewWasteInquirySapV1Responses {
  200: ViewWasteInquirySapResponse
}

export type ViewWasteInquirySapV1Response = ViewWasteInquirySapV1Responses[keyof ViewWasteInquirySapV1Responses]

export interface UploadDocumentSubmittedWasteInquiryV1Data {
  body: UploadDocumentSubmittedWasteInquiryCommand
  query?: never
  url: '/api/v1/waste-inquiries/{inquiryNumber}/upload-documents'
  path: {
    inquiryNumber: string
  }
}

export interface UploadDocumentSubmittedWasteInquiryV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<WasteInquiryNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UploadDocumentSubmittedWasteInquiryV1Error = UploadDocumentSubmittedWasteInquiryV1Errors[keyof UploadDocumentSubmittedWasteInquiryV1Errors]

export interface UploadDocumentSubmittedWasteInquiryV1Responses {
  /**
   * Documents uploaded to the submitted waste inquiry
   */
  200: unknown
}

export interface BulkDeleteWasteInquiryV1Data {
  body: BulkDeleteWasteInquiryCommand
  query?: never
  url: '/api/v1/waste-inquiries/bulk'
  path?: never
}

export interface BulkDeleteWasteInquiryV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  409: {
    traceId?: string | null
    errors?: Array<WasteInquiryAlreadySubmitted>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type BulkDeleteWasteInquiryV1Error = BulkDeleteWasteInquiryV1Errors[keyof BulkDeleteWasteInquiryV1Errors]

export interface BulkDeleteWasteInquiryV1Responses {
  200: unknown
}

export interface CopyWasteInquirySapV1Data {
  body?: never
  query?: never
  url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/copy'
  path: {
    inquiryNumber: string
  }
}

export interface CopyWasteInquirySapV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CopyWasteInquirySapV1Error = CopyWasteInquirySapV1Errors[keyof CopyWasteInquirySapV1Errors]

export interface CopyWasteInquirySapV1Responses {
  201: CopyWasteInquirySapResponse
}

export type CopyWasteInquirySapV1Response = CopyWasteInquirySapV1Responses[keyof CopyWasteInquirySapV1Responses]

export interface CreateWeeklyPlanningRequestV1Data {
  body: CreateWeeklyPlanningRequestCommand
  query?: never
  url: '/api/v1/weekly-planning-requests'
  path?: never
}

export interface CreateWeeklyPlanningRequestV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type CreateWeeklyPlanningRequestV1Error = CreateWeeklyPlanningRequestV1Errors[keyof CreateWeeklyPlanningRequestV1Errors]

export interface CreateWeeklyPlanningRequestV1Responses {
  201: CreateWeeklyPlanningRequestResponse
}

export type CreateWeeklyPlanningRequestV1Response = CreateWeeklyPlanningRequestV1Responses[keyof CreateWeeklyPlanningRequestV1Responses]

export interface ViewWeeklyPlanningRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/weekly-planning-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface ViewWeeklyPlanningRequestV1Errors {
  404: {
    traceId?: string | null
    errors?: Array<CustomerNotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type ViewWeeklyPlanningRequestV1Error = ViewWeeklyPlanningRequestV1Errors[keyof ViewWeeklyPlanningRequestV1Errors]

export interface ViewWeeklyPlanningRequestV1Responses {
  200: ViewWeeklyPlanningRequestResponse
}

export type ViewWeeklyPlanningRequestV1Response = ViewWeeklyPlanningRequestV1Responses[keyof ViewWeeklyPlanningRequestV1Responses]

export interface UpdateWeeklyPlanningRequestV1Data {
  body: UpdateWeeklyPlanningRequestCommand
  query?: never
  url: '/api/v1/weekly-planning-requests/{uuid}'
  path: {
    uuid: string
  }
}

export interface UpdateWeeklyPlanningRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<CustomerNotAccessibleError | CustomerNotProvidedError | FieldMustBeNullError | FileNotAccessibleError | PickUpAddressNotAccessibleError | WasteProducerNotAccessibleError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type UpdateWeeklyPlanningRequestV1Error = UpdateWeeklyPlanningRequestV1Errors[keyof UpdateWeeklyPlanningRequestV1Errors]

export interface UpdateWeeklyPlanningRequestV1Responses {
  200: UpdateWeeklyPlanningRequestResponse
}

export type UpdateWeeklyPlanningRequestV1Response = UpdateWeeklyPlanningRequestV1Responses[keyof UpdateWeeklyPlanningRequestV1Responses]

export interface AddWprPickUpRequestV1Data {
  body: AddWprPickUpRequestCommand
  query?: never
  url: '/api/v1/weekly-planning-requests/{uuid}/add-pick-up-request'
  path: {
    uuid: string
  }
}

export interface AddWprPickUpRequestV1Errors {
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type AddWprPickUpRequestV1Error = AddWprPickUpRequestV1Errors[keyof AddWprPickUpRequestV1Errors]

export interface AddWprPickUpRequestV1Responses {
  201: AddWprPickUpRequestResponse
}

export type AddWprPickUpRequestV1Response = AddWprPickUpRequestV1Responses[keyof AddWprPickUpRequestV1Responses]

export interface SubmitWeeklyPlanningRequestV1Data {
  body?: never
  query?: never
  url: '/api/v1/weekly-planning-requests/{uuid}/submit'
  path: {
    uuid: string
  }
}

export interface SubmitWeeklyPlanningRequestV1Errors {
  400: {
    traceId?: string | null
    errors?: Array<ContractLineNotAccessibleError | ContractLineNotOfCustomerError | ContractLineNotOfPickUpAddressesError | WeeklyPlanningRequestAlreadySubmittedError>
  }
  404: {
    traceId?: string | null
    errors?: Array<NotFoundError>
  }
  500: {
    traceId?: string | null
    errors?: Array<InternalServerApiError>
  }
}

export type SubmitWeeklyPlanningRequestV1Error = SubmitWeeklyPlanningRequestV1Errors[keyof SubmitWeeklyPlanningRequestV1Errors]

export interface SubmitWeeklyPlanningRequestV1Responses {
  /**
   * Weekly planning request submitted
   */
  200: SubmitWeeklyPlanningRequestResponse
}

export type SubmitWeeklyPlanningRequestV1Response = SubmitWeeklyPlanningRequestV1Responses[keyof SubmitWeeklyPlanningRequestV1Responses]

export interface ClientOptions {
  baseUrl: (string & {}) | 'http://localhost:3000'
}
