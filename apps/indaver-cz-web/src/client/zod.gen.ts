// This file is auto-generated by @hey-api/openapi-ts

import { z } from 'zod'

export const zPermission = z.enum([
  'all_permissions',
  'announcement.manage',
  'certificate.manage',
  'certificate.read',
  'contact.manage',
  'contact.read',
  'contract-line.read',
  'contract-line.manage',
  'document.master-table',
  'document.tfs',
  'document.quotation',
  'document.minutes-and-presentations',
  'document.manual',
  'document.bsc',
  'document.contract',
  'document.transport',
  'dynamic-table-view.manage',
  'event-log.read',
  'invoice.read',
  'invoice.manage',
  'jobs.read.index',
  'jobs.read.detail',
  'news-item.manage',
  'newsletter.subscribe',
  'pick-up-request.read',
  'pick-up-request.manage',
  'packaging-request.read',
  'packaging-request.manage',
  'power-bi.read',
  'role.read',
  'role.manage',
  'send_push_notification',
  'typesense',
  'useful-link.ecmr',
  'useful-link.indascan',
  'useful-link.permits',
  'useful-link.reporting',
  'user.read',
  'user.manage',
  'user.impersonate',
  'waste-inquiry.read',
  'waste-inquiry.manage',
  'weekly-planning-request.read',
  'weekly-planning-request.manage',
  'guidance-letter.read',
])

export const zViewRoleDetailResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isDefault: z.boolean(),
  isSystemAdmin: z.boolean(),
  name: z.string(),
  permissions: z.array(zPermission),
})

export const zViewMeResponse = z.object({
  uuid: z.string().uuid(),
  isInternalUser: z.boolean(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
  roles: z.array(zViewRoleDetailResponse),
})

export const zViewUserDetailResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
  roles: z.array(zViewRoleDetailResponse),
})

export const zPaginatedOffsetQuery = z.object({
  limit: z.number().gte(1).lte(100),
  offset: z.number().gte(0),
})

export const zUserIndexRoleView = z.object({
  uuid: z.string().uuid(),
  isSystemAdmin: z.boolean(),
  name: z.string(),
})

export const zUserIndexView = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
  roles: z.array(zUserIndexRoleView),
  upn: z.string(),
})

export const zPaginatedOffsetResponseMeta = z.object({
  limit: z.number(),
  offset: z.number(),
  total: z.number(),
})

export const zViewUserIndexResponse = z.object({
  items: z.array(zUserIndexView),
  meta: zPaginatedOffsetResponseMeta,
})

export const zStartUserImpersonationResponse = z.object({ impersonationToken: z.string() })

export const zViewPermissionIndexPermissionResponse = z.object({
  name: z.string(),
  description: z.string(),
  key: zPermission,
})

export const zViewPermissionIndexGroupResponse = z.object({
  name: z.string(),
  permissions: z.array(zViewPermissionIndexPermissionResponse),
})

export const zViewPermissionIndexResponse = z.object({ groups: z.array(zViewPermissionIndexGroupResponse) })

export const zCoordinatesResponse = z.object({
  latitude: z.number(),
  longitude: z.number(),
})

export const zAddressResponse = z.object({
  addressLine1: z.string(),
  addressLine2: z.union([
    z.string(),
    z.null(),
  ]),
  coordinates: z.union([
    zCoordinatesResponse,
    z.null(),
  ]),
  countryCode: z.union([
    z.string(),
    z.null(),
  ]),
  locality: z.string(),
  postalCode: z.string(),
})

export const zCustomerResponse = z.object({
  id: z.string(),
  name: z.string(),
  address: z.union([
    z.string(),
    z.unknown(),
    z.null(),
  ]),
})

export const zViewCustomerIndexResponse = z.object({
  items: z.array(zCustomerResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zMimeType = z.enum([
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/html',
  'image/jpeg',
  'image/png',
  'image/tiff',
  'image/bmp',
  'image/heic',
  'image/webp',
  'image/gif',
  'text/csv',
  'application/vnd.ms-outlook',
  'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  'application/rtf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/octet-stream',
])

export const zCreateFileCommand = z.object({
  name: z.string(),
  mimeType: zMimeType,
})

export const zCreateFileResponse = z.object({
  uuid: z.string().uuid(),
  name: z.string(),
  mimeType: z.union([
    zMimeType,
    z.null(),
  ]),
  uploadUrl: z.string(),
})

export const zRequestType = z.enum([
  'waste',
  'pick-up',
])

export const zViewSuggestedCustomersFilterQuery = z.object({ requestType: zRequestType })

export const zViewSuggestedCustomersResponse = z.object({ items: z.array(zCustomerResponse) })

export const zCustomerNotFoundError = z.object({
  code: z.enum([
    'customer_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zViewCustomerCountryResponse = z.object({ countryCode: z.string() })

export const zSubjectType = z.enum([
  'announcement',
  'contact',
  'dynamic-table-view',
  'file',
  'news-item',
  'pick-up-request',
  'packaging-request',
  'role',
  'user',
  'waste-inquiry',
  'weekly-planning-request',
])

export const zViewDomainEventLogIndexFilterQuery = z.object({
  subjectId: z.string().uuid().optional(),
  userUuid: z.string().uuid().optional(),
  subjectType: zSubjectType.optional(),
})

export const zViewDomainEventLogIndexQueryKey = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string(),
})

export const zViewDomainEventLogIndexPaginationQuery = z.object({
  key: z.union([
    zViewDomainEventLogIndexQueryKey,
    z.null(),
  ]).optional(),
  limit: z.number().gte(0).lte(100),
})

export const zUserCreatedEventContent = z.object({ userUuid: z.string().uuid() })

export const zUserCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zUserCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'user.created',
  ]),
  version: z.number().int().gte(0),
})

export const zUserUpdatedEventContent = z.object({ userUuid: z.string().uuid() })

export const zUserUpdatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zUserUpdatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'user.updated',
  ]),
  version: z.number().int().gte(0),
})

export const zUserSyncedEventContent = z.object({ userUuid: z.string().uuid() })

export const zUserSyncedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zUserSyncedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'user.synced',
  ]),
  version: z.number().int().gte(0),
})

export const zRoleCreatedEventContent = z.object({
  roleUuid: z.string().uuid(),
  roleName: z.string(),
})

export const zRoleCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zRoleCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'role.created',
  ]),
  version: z.number().int().gte(0),
})

export const zRoleDeletedEventContent = z.object({
  roleUuid: z.string().uuid(),
  roleName: z.string(),
})

export const zRoleDeletedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zRoleDeletedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'role.deleted',
  ]),
  version: z.number().int().gte(0),
})

export const zRoleRenamedEventContent = z.object({
  roleUuid: z.string().uuid(),
  newName: z.string(),
  previousName: z.string(),
})

export const zRoleRenamedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zRoleRenamedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'role.renamed',
  ]),
  version: z.number().int().gte(0),
})

export const zRolePermissionsUpdatedEventContent = z.object({
  roleUuid: z.string().uuid(),
  newPermissions: z.array(zPermission),
  roleName: z.string(),
})

export const zRolePermissionsUpdatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zRolePermissionsUpdatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'role.permissions.updated',
  ]),
  version: z.number().int().gte(0),
})

export const zRolePermissionsCacheClearedEventContent = z.object({ roleUuids: z.string().uuid() })

export const zRolePermissionsCacheClearedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zRolePermissionsCacheClearedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'role.permissions.cache.cleared',
  ]),
  version: z.number().int().gte(0),
})

export const zFileCreatedEventContent = z.object({
  fileUuid: z.string().uuid(),
  fileName: z.string(),
})

export const zFileCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zFileCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'file.created',
  ]),
  version: z.number().int().gte(0),
})

export const zFileUploadedEventContent = z.object({
  fileUuid: z.string().uuid(),
  fileName: z.string(),
})

export const zFileUploadedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zFileUploadedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'file.uploaded',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationType = z.enum([
  'user.created',
  'test-notification',
])

export const zNotificationCreatedEventContent = z.object({
  uuid: z.string().uuid(),
  type: zNotificationType,
})

export const zNotificationCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zNotificationCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.created',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationChannel = z.enum([
  'email',
  'sms',
  'app',
  'push',
])

export const zUserNotificationCreatedEventContent = z.object({
  notificationUuid: z.string().uuid(),
  userUuid: z.string().uuid(),
  channel: zNotificationChannel,
})

export const zUserNotificationCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zUserNotificationCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'user.notification.created',
  ]),
  version: z.number().int().gte(0),
})

export const zContactCreatedEventContent = z.object({ contactUuid: z.string().uuid() })

export const zContactCreatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zContactCreatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'contact.created',
  ]),
  version: z.number().int().gte(0),
})

export const zContactUpdatedEventContent = z.object({ contactUuid: z.string().uuid() })

export const zContactUpdatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zContactUpdatedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'contact.updated',
  ]),
  version: z.number().int().gte(0),
})

export const zContactDeletedEventContent = z.object({ contactUuid: z.string().uuid() })

export const zContactDeletedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zContactDeletedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'contact.deleted',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationReadEventContent = z.object({
  notificationUuid: z.string().uuid(),
  userUuid: z.string().uuid(),
})

export const zNotificationReadDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zNotificationReadEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.read',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationUnreadEventContent = z.object({
  notificationUuid: z.string().uuid(),
  userUuid: z.string().uuid(),
})

export const zNotificationUnreadDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zNotificationUnreadEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.unread',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationPreset = z.enum([
  'all',
  'default',
  'custom',
  'none',
])

export const zNotificationPreferencePresetEventContent = z.object({
  userUuid: z.string().uuid(),
  preset: zNotificationPreset,
})

export const zNotificationPreferencePresetUpdatedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zNotificationPreferencePresetEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.preference.preset.updated',
  ]),
  version: z.number().int().gte(0),
})

export const zNotificationTypesMigratedEventContent = z.object({ types: z.array(zNotificationType) })

export const zNotificationTypesMigratedDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zNotificationTypesMigratedEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.types.migrated',
  ]),
  version: z.number().int().gte(0),
})

export const zTestNotificationSentEventContent = z.object({ message: z.string() })

export const zTestNotificationSentDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zTestNotificationSentEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'test-notification.sent',
  ]),
  version: z.number().int().gte(0),
})

export const zAllNotificationsMarkedAsReadEventContent = z.object({ userUuid: z.string().uuid() })

export const zNotificationReadAllDomainEventLog = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  content: zAllNotificationsMarkedAsReadEventContent,
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  type: z.enum([
    'notification.read.all',
  ]),
  version: z.number().int().gte(0),
})

export const zDomainEventLogResponse = z.object({
  uuid: z.string().uuid(),
  subjectId: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  userUuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  message: z.string(),
  source: z.string(),
  subjectType: z.union([
    zSubjectType,
    z.null(),
  ]),
  version: z.number().int().gte(0),
})

export const zViewDomainEventLogIndexResponseMeta = z.object({
  next: z.union([
    zViewDomainEventLogIndexQueryKey,
    z.null(),
  ]),
})

export const zViewDomainEventLogIndexResponse = z.object({
  items: z.array(z.union([
    zUserCreatedDomainEventLog,
    zUserUpdatedDomainEventLog,
    zUserSyncedDomainEventLog,
    zRoleCreatedDomainEventLog,
    zRoleDeletedDomainEventLog,
    zRoleRenamedDomainEventLog,
    zRolePermissionsUpdatedDomainEventLog,
    zRolePermissionsCacheClearedDomainEventLog,
    zFileCreatedDomainEventLog,
    zFileUploadedDomainEventLog,
    zNotificationCreatedDomainEventLog,
    zUserNotificationCreatedDomainEventLog,
    zContactCreatedDomainEventLog,
    zContactUpdatedDomainEventLog,
    zContactDeletedDomainEventLog,
    zNotificationReadDomainEventLog,
    zNotificationUnreadDomainEventLog,
    zNotificationPreferencePresetUpdatedDomainEventLog,
    zNotificationTypesMigratedDomainEventLog,
    zTestNotificationSentDomainEventLog,
    zNotificationReadAllDomainEventLog,
  ])),
  meta: zViewDomainEventLogIndexResponseMeta,
})

export const zGlobalSearchCollectionName = z.enum([
  'user',
  'contact',
])

export const zSearchCollectionsFilterQuery = z.object({ collections: z.array(zGlobalSearchCollectionName).optional() })

export const zSearchCollectionUserResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
})

export const zSearchCollectionContactResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
})

export const zSearchCollectionsResponseItem = z.object({
  collection: zGlobalSearchCollectionName,
  entity: z.union([
    zSearchCollectionUserResponse,
    zSearchCollectionContactResponse,
  ]),
  text_match: z.number(),
})

export const zSearchCollectionsResponse = z.object({ items: z.array(zSearchCollectionsResponseItem) })

export const zViewJobsIndexSortQueryKey = z.enum([
  'createdAt',
])

export const zSortDirection = z.enum([
  'asc',
  'desc',
])

export const zViewJobsIndexSortQuery = z.object({
  key: zViewJobsIndexSortQueryKey,
  order: zSortDirection,
})

export const zQueueName = z.enum([
  'system',
])

export const zViewJobsIndexFilterQuery = z.object({
  archived: z.boolean().optional().default(false),
  queueNames: z.array(zQueueName).optional(),
})

export const zViewJobsIndexQueryKey = z.object({
  id: z.string(),
  createdAt: z.string().datetime().optional(),
})

export const zViewJobsIndexPaginationQuery = z.object({
  key: z.union([
    zViewJobsIndexQueryKey,
    z.null(),
  ]).optional(),
  limit: z.number().gte(0).lte(100),
})

export const zJobStatus = z.enum([
  'created',
  'active',
  'completed',
  'retry',
  'failed',
  'cancelled',
])

export const zViewJobsIndexItemResponse = z.object({
  id: z.string().uuid(),
  completedAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  name: z.string(),
  queueName: zQueueName,
  status: zJobStatus,
})

export const zViewJobsIndexResponseMeta = z.object({
  next: z.union([
    zViewJobsIndexQueryKey,
    z.null(),
  ]),
})

export const zViewJobsIndexResponse = z.object({
  items: z.array(zViewJobsIndexItemResponse),
  meta: zViewJobsIndexResponseMeta,
})

export const zViewJobDetailResponse = z.object({
  id: z.string().uuid(),
  completedAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  singletonOn: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startedAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  name: z.string(),
  data: z.record(z.unknown()),
  deadLetter: z.union([
    z.string(),
    z.null(),
  ]),
  expireIn: z.record(z.unknown()),
  keepUntil: z.string().datetime(),
  output: z.union([
    z.record(z.unknown()),
    z.null(),
  ]),
  policy: z.union([
    z.string(),
    z.null(),
  ]),
  priority: z.number(),
  queueName: zQueueName,
  retryBackoff: z.boolean(),
  retryCount: z.number(),
  retryDelay: z.number(),
  retryLimit: z.number(),
  singletonKey: z.union([
    z.string(),
    z.null(),
  ]),
  startAfter: z.string().datetime(),
  status: zJobStatus,
})

export const zPreferenceTypes = z.object({
  app: z.array(zNotificationType),
  email: z.array(zNotificationType),
  push: z.array(zNotificationType),
  sms: z.array(zNotificationType),
})

export const zGetMyNotificationPreferencesResponse = z.object({
  appEnabled: z.boolean(),
  emailEnabled: z.boolean(),
  preferences: zPreferenceTypes,
  preset: zNotificationPreset,
  pushEnabled: z.boolean(),
  smsEnabled: z.boolean(),
})

export const zNotificationTypeChannelConfig = z.object({
  isSupported: z.boolean(),
  channel: zNotificationChannel,
  defaultValue: z.boolean(),
})

export const zNotificationTypeConfig = z.object({
  channelConfigs: z.array(zNotificationTypeChannelConfig),
  type: zNotificationType,
})

export const zGetNotificationTypesConfigResponse = z.object({ items: z.array(zNotificationTypeConfig) })

export const zUpdateMyChannelNotificationPreferenceCommand = z.object({
  isEnabled: z.boolean(),
  channel: zNotificationChannel,
})

export const zSendTestNotificationCommand = z.object({ message: z.string() })

export const zGetMyNotificationsFilterQuery = z.object({ onlyUnread: z.string().optional() })

export const zGetMyNotificationsQueryKey = z.object({
  notificationUuid: z.string().uuid(),
  createdAt: z.string(),
})

export const zGetMyNotificationsPaginationQuery = z.object({
  key: zGetMyNotificationsQueryKey.optional(),
  limit: z.number().gte(0).lte(100),
})

export const zCreatedByUserResponse = z.object({
  uuid: z.string().uuid(),
  name: z.string(),
})

export const zTestNotificationContent = z.object({ message: z.string() })

export const zTestNotificationNotification = z.object({
  notificationUuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  readAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  createdByUser: z.union([
    zCreatedByUserResponse,
    z.null(),
  ]),
  message: z.string(),
  type: z.enum([
    'test-notification',
  ]),
  meta: zTestNotificationContent,
})

export const zGetMyNotificationsResponseMeta = z.object({
  next: z.union([
    zGetMyNotificationsQueryKey,
    z.null(),
  ]),
})

export const zGetMyNotificationsResponse = z.object({
  items: z.array(zTestNotificationNotification),
  meta: zGetMyNotificationsResponseMeta,
})

export const zViewUnreadNotificationsCountResponse = z.object({
  amount: z.number().gte(0),
  exceedsLimit: z.boolean(),
})

export const zUpdateMyNotificationTypePreferenceCommand = z.object({
  isEnabled: z.boolean(),
  channel: zNotificationChannel,
  types: z.array(zNotificationType),
})

export const zUserNotificationNotFoundError = z.object({
  code: z.enum([
    'user_notification_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zUpdateMyNotificationPreferencePresetCommand = z.object({ preset: zNotificationPreset })

export const zErrorSource = z.object({ pointer: z.string() })

export const zMigrationAlreadyPerformedErrorMeta = z.object({ type: z.array(zNotificationType) })

export const zMigrationAlreadyPerformedError = z.object({
  code: z.enum([
    'migration_already_performed',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
  meta: zMigrationAlreadyPerformedErrorMeta,
})

export const zMigrateNotificationTypesCommand = z.object({ types: z.array(zNotificationType) })

export const zCreateRoleCommand = z.object({ name: z.string() })

export const zCreateRoleResponse = z.object({ uuid: z.string().uuid() })

export const zClearRolePermissionsCacheCommand = z.object({
  roleUuids: z.union([
    z.array(z.string().uuid()),
    z.null(),
  ]).optional(),
})

export const zUpdateRoleCommand = z.object({ name: z.string() })

export const zRoleResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isDefault: z.boolean(),
  isSystemAdmin: z.boolean(),
  name: z.string(),
  permissions: z.array(zPermission),
})

export const zViewRoleIndexResponse = z.object({ items: z.array(zRoleResponse) })

export const zRoleNotFoundError = z.object({
  code: z.enum([
    'role_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zUpdateRolesPermissionsCommandItem = z.object({
  roleUuid: z.string().uuid(),
  permissions: z.array(zPermission),
})

export const zUpdateRolesPermissionsCommand = z.object({ roles: z.array(zUpdateRolesPermissionsCommandItem) })

export const zGetApiInfoResponse = z.object({
  commit: z.string(),
  environment: z.string(),
  timestamp: z.string().datetime(),
  version: z.string(),
})

export const zUiTheme = z.enum([
  'light',
  'dark',
  'system',
])

export const zLocale = z.enum([
  'en-GB',
  'nl-BE',
  'fr-FR',
  'es-ES',
  'de-DE',
])

export const zFontSize = z.enum([
  'smaller',
  'small',
  'default',
  'large',
  'larger',
])

export const zUpdateUiPreferencesCommand = z.object({
  fontSize: zFontSize.optional(),
  highContrast: z.boolean().optional(),
  language: zLocale.optional(),
  reduceMotion: z.boolean().optional(),
  showShortcuts: z.boolean().optional(),
  theme: zUiTheme.optional(),
})

export const zViewUiPreferencesResponse = z.object({
  fontSize: zFontSize,
  highContrast: z.boolean(),
  language: zLocale,
  reduceMotion: z.boolean(),
  showShortcuts: z.boolean(),
  theme: zUiTheme,
})

export const zAnnouncementType = z.enum([
  'informational',
  'urgent',
])

export const zCreateAnnouncementTranslationCommand = z.object({
  title: z.string(),
  content: z.record(z.unknown()),
  language: zLocale,
})

export const zAnnouncementSalesOrganisation = z.object({
  id: z.string(),
  name: z.string(),
})

export const zCreateAnnouncementCommand = z.object({
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.string().date(),
  salesOrganisations: z.array(zAnnouncementSalesOrganisation).optional(),
  translations: z.array(zCreateAnnouncementTranslationCommand),
  type: zAnnouncementType,
})

export const zCreateAnnouncementResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zMissingRequiredFieldError = z.object({
  code: z.enum([
    'missing_required_field',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zDateMustBeAfterError = z.object({
  code: z.enum([
    'date_must_be_after',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zFieldMustBeNullError = z.object({
  code: z.enum([
    'field_must_be_null',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNotFoundError = z.object({
  code: z.enum([
    'not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zUpdateAnnouncementTranslationCommand = z.object({
  title: z.string().optional(),
  content: z.record(z.unknown()).optional(),
  language: zLocale,
})

export const zUpdateAnnouncementCommand = z.object({
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.string().date().optional(),
  salesOrganisations: z.array(zAnnouncementSalesOrganisation).optional(),
  translations: z.array(zUpdateAnnouncementTranslationCommand).optional(),
  type: zAnnouncementType.optional(),
})

export const zUpdateAnnouncementResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zViewAnnouncementTranslationResponse = z.object({
  uuid: z.string().uuid(),
  title: z.string(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  content: z.record(z.unknown()),
  language: zLocale,
})

export const zDashboardAnnouncementIndexView = z.object({
  uuid: z.string().uuid(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  translation: zViewAnnouncementTranslationResponse,
  type: zAnnouncementType,
})

export const zViewDashboardAnnouncementIndexResponse = z.object({
  items: z.array(zDashboardAnnouncementIndexView),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewDashboardAnnouncementResponse = z.object({
  uuid: z.string().uuid(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  translation: zViewAnnouncementTranslationResponse,
  type: zAnnouncementType,
})

export const zPublishStatus = z.enum([
  'scheduled',
  'published',
  'archived',
])

export const zViewNewsItemTranslationResponse = z.object({
  uuid: z.string().uuid(),
  title: z.union([
    z.string(),
    z.null(),
  ]),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  content: z.union([
    z.record(z.unknown()),
    z.null(),
  ]),
  language: zLocale,
})

export const zViewNewsItemAuthorResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewAnnouncementResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  updatedAt: z.string().datetime(),
  author: zViewNewsItemAuthorResponse,
  publishStatus: zPublishStatus,
  salesOrganisations: z.array(zAnnouncementSalesOrganisation),
  translations: z.array(zViewNewsItemTranslationResponse),
  type: zAnnouncementType,
})

export const zViewAnnouncementTranslationIndexResponse = z.object({
  uuid: z.string().uuid(),
  title: z.string(),
  language: zLocale,
})

export const zViewAnnouncementAuthorResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zAnnouncementIndexView = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  updatedAt: z.string().datetime(),
  author: zViewAnnouncementAuthorResponse,
  publishStatus: zPublishStatus,
  translations: z.array(zViewAnnouncementTranslationIndexResponse),
  type: zAnnouncementType,
})

export const zViewAnnouncementIndexResponse = z.object({
  items: z.array(zAnnouncementIndexView),
  meta: zPaginatedOffsetResponseMeta,
})

export const zCertificateFileNotFoundError = z.object({
  code: z.enum([
    'certificate_file_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zCertificateNotFoundError = z.object({
  code: z.enum([
    'certificate_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zCertificateDocType = z.enum([
  'receipt-confirmation',
  'blending-confirmation',
  'treatment-certificates',
  'certificate-of-treatment',
  'tf-certificates',
])

export const zDownloadCertificateCommand = z.object({
  docType: zCertificateDocType,
  invoiceNumber: z.string(),
})

export const zViewCertificateIndexSortKey = z.enum([
  'salesOrder',
  'salesOrderLine',
  'collectionDate',
  'deliveryDate',
  'dispositionPickUpDate',
  'dispositionDeliveryDate',
  'treatmentCentre',
  'endTreatmentCentre',
  'ewc',
  'wtfForm',
  'tfs',
  'disposalDate',
  'printDate',
  'pickUpAddress',
  'pickUpAddressName',
  'customerId',
  'customerName',
  'wasteProducerId',
  'wasteProducerName',
  'contractItem',
  'contract',
  'invoice',
])

export const zViewCertificateIndexSortQuery = z.object({
  key: zViewCertificateIndexSortKey,
  order: zSortDirection,
})

export const zDateRange = z.object({
  from: z.string().date(),
  to: z.string().date(),
})

export const zViewCertificateIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  pickUpAddressId: z.string().optional(),
  wasteProducerId: z.string().optional(),
  collectionDate: zDateRange.optional(),
  deliveryDate: zDateRange.optional(),
  disposalDate: zDateRange.optional(),
  dispositionDeliveryDate: zDateRange.optional(),
  dispositionPickUpDate: zDateRange.optional(),
  printDate: zDateRange.optional(),
  contract: z.string().optional(),
  contractItem: z.string().optional(),
  description: z.string().optional(),
  docTypes: z.array(zCertificateDocType).optional(),
  endTreatmentCentre: z.string().optional(),
  ewc: z.string().optional(),
  invoice: z.string().optional(),
  salesOrder: z.string().optional(),
  salesOrderLine: z.string().optional(),
  tfs: z.string().optional(),
  treatmentCentre: z.string().optional(),
  wtfForm: z.string().optional(),
})

export const zCertificateIndexResponse = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  collectionDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  deliveryDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  disposalDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  dispositionDeliveryDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  dispositionPickUpDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  printDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  contract: z.union([
    z.string(),
    z.null(),
  ]),
  contractItem: z.union([
    z.string(),
    z.null(),
  ]),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  description: z.union([
    z.string(),
    z.null(),
  ]),
  docType: z.union([
    zCertificateDocType,
    z.null(),
  ]),
  endTreatmentCentre: z.union([
    z.string(),
    z.null(),
  ]),
  ewcCode: z.union([
    z.string(),
    z.null(),
  ]),
  fileName: z.union([
    z.string(),
    z.null(),
  ]),
  invoice: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  salesOrder: z.union([
    z.string(),
    z.null(),
  ]),
  salesOrderLine: z.union([
    z.string(),
    z.null(),
  ]),
  tfs: z.union([
    z.string(),
    z.null(),
  ]),
  treatmentCentre: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
  wtfForm: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewCertificateIndexResponse = z.object({
  items: z.array(zCertificateIndexResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zContactResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
})

export const zViewContactIndexResponse = z.object({
  items: z.array(zContactResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zCreateContactCommand = z.object({
  email: z.string().email().max(241),
  firstName: z.string().max(40),
  lastName: z.string().max(40),
})

export const zCreateContactResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zUpdateContactCommand = z.object({
  email: z.string().email().max(241).optional(),
  firstName: z.string().max(40).optional(),
  lastName: z.string().max(40).optional(),
})

export const zViewContainerTypeIndexFilterQuery = z.object({ customerId: z.string() })

export const zContainerTypeResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewContainerTypeIndexResponse = z.object({ items: z.array(zContainerTypeResponse) })

export const zViewContractLineIndexSortQueryKey = z.enum([
  'contractNumber',
  'contractItem',
  'wasteMaterial',
  'treatmentCenterName',
  'installationName',
  'wasteProducerId',
  'wasteProducerName',
  'pickUpAddressId',
  'pickUpAddressName',
  'asn',
  'tcNumber',
  'ewcCode',
  'endTreatmentCenterId',
  'endTreatmentCenterName',
  'processCode',
  'esnNumber',
])

export const zViewContractLineIndexSortQuery = z.object({
  key: zViewContractLineIndexSortQueryKey,
  order: zSortDirection,
})

export const zContractLinePackagingType = z.enum([
  'packaged',
  'bulk',
])

export const zViewContractLineIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  endTreatmentCenterId: z.string().optional(),
  wasteProducerId: z.string().optional(),
  isHazardous: z.boolean().optional(),
  asn: z.string().optional(),
  contractItem: z.string().optional(),
  contractNumber: z.string().optional(),
  customerReference: z.string().optional(),
  deliveryInfo: z.string().optional(),
  endTreatmentCenterName: z.string().optional(),
  esnNumber: z.string().optional(),
  ewcCode: z.string().optional(),
  installationName: z.string().optional(),
  materialAnalysis: z.string().optional(),
  materialNumber: z.string().optional(),
  packaged: zContractLinePackagingType.optional(),
  pickUpAddressIds: z.array(z.string()).optional(),
  processCode: z.string().optional(),
  tcNumber: z.string().optional(),
  tfs: z.boolean().optional(),
  treatmentCenterName: z.string().optional(),
  wasteMaterial: z.string().optional(),
})

export const zContractLineResponse = z.object({
  contractLineId: z.string(),
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  isHazardous: z.union([
    z.boolean(),
    z.null(),
  ]),
  asn: z.union([
    z.string(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractNumber: z.string(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  deliveryInfo: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  esnNumber: z.union([
    z.string(),
    z.null(),
  ]),
  ewcCode: z.union([
    z.string(),
    z.null(),
  ]),
  installationName: z.union([
    z.string(),
    z.null(),
  ]),
  materialAnalysis: z.union([
    z.string(),
    z.null(),
  ]),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  packaged: z.union([
    zContractLinePackagingType,
    z.null(),
  ]),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  processCode: z.union([
    z.string(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
  tfs: z.union([
    z.boolean(),
    z.null(),
  ]),
  treatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewContractLineIndexResponse = z.object({
  items: z.array(zContractLineResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewWprContractLineIndexFilterQuery = z.object({
  customerId: z.string(),
  wasteProducerId: z.string().optional(),
  pickUpAddressIds: z.array(z.string()).optional(),
})

export const zWprContractLineResponse = z.object({
  contractLineId: z.string(),
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpRequestUuid: z.string(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  isHazardous: z.union([
    z.boolean(),
    z.null(),
  ]),
  asn: z.union([
    z.string(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractNumber: z.string(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  deliveryInfo: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  esnNumber: z.union([
    z.string(),
    z.null(),
  ]),
  ewcCode: z.union([
    z.string(),
    z.null(),
  ]),
  installationName: z.union([
    z.string(),
    z.null(),
  ]),
  materialAnalysis: z.union([
    z.string(),
    z.null(),
  ]),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  packaged: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  processCode: z.union([
    z.string(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
  tfs: z.union([
    z.boolean(),
    z.null(),
  ]),
  treatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewWprContractLineIndexResponse = z.object({
  items: z.array(zWprContractLineResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewPackagingRequestContractLineIndexFilterQuery = z.object({
  customerId: z.string(),
  wasteProducerId: z.string(),
  isSales: z.boolean().optional(),
  deliveryAddressIds: z.array(z.string()).optional(),
  wasteMaterial: z.string().optional(),
})

export const zPackagingRequestContractLineResponse = z.object({
  contractLineId: z.string(),
  isSales: z.union([
    z.boolean(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractNumber: z.string(),
  imageUrl: z.union([
    z.string(),
    z.null(),
  ]),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewPackagingRequestContractLineIndexResponse = z.object({
  items: z.array(zPackagingRequestContractLineResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zContractLineNotAccessibleErrorMeta = z.object({
  contractLineNumber: z.string(),
  contractNumber: z.string(),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zContractLineNotAccessibleError = z.object({
  code: z.enum([
    'contract_line_not_accessible',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
  meta: zContractLineNotAccessibleErrorMeta,
})

export const zContractLinesSelection = z.object({
  contractItem: z.string(),
  contractNumber: z.string(),
})

export const zContractLineQuerySelection = z.object({
  excludeSelection: z.array(zContractLinesSelection).min(1).optional(),
  filter: zViewContractLineIndexFilterQuery,
})

export const zGenerateContractLinesPdfCommand = z.object({
  querySelection: zContractLineQuerySelection.optional(),
  selection: z.array(zContractLinesSelection).min(1).optional(),
})

export const zGenerateContractLinesPdfResponse = z.object({
  name: z.string(),
  content: z.string(),
  mimeType: z.string(),
  size: z.number(),
})

export const zDocumentNotFoundErrorMeta = z.object({
  customerUuid: z.string().uuid(),
  documentId: z.string(),
})

export const zDocumentNotFoundError = z.object({
  code: z.enum([
    'document_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zDocumentNotFoundErrorMeta,
})

export const zDownloadDocumentCommand = z.object({
  customerUuid: z.string().uuid(),
  documentId: z.string(),
})

export const zForbiddenError = z.object({
  code: z.enum([
    'forbidden',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '403',
  ]),
})

export const zSharepointDocumentViewName = z.enum([
  'mastertable',
  'tfs',
  'quotation',
  'meetings',
  'manual',
  'bsc',
  'contract',
  'transport',
])

export const zGetDocumentFiltersFilterQuery = z.object({
  customerUuid: z.string().uuid(),
  viewName: zSharepointDocumentViewName,
  wasteProducerIds: z.array(z.string()),
})

export const zDocumentFilterValue = z.object({
  key: z.string(),
  value: z.string(),
})

export const zDocumentFilterResponse = z.object({
  filterName: z.string(),
  filterValues: z.array(zDocumentFilterValue),
})

export const zGetDocumentFiltersResponse = z.object({ filters: z.array(zDocumentFilterResponse) })

export const zViewDocumentIndexPaginationQuery = z.object({
  key: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  limit: z.number().gte(0).lte(100),
})

export const zSharepointDocumentStatus = z.enum([
  'Active',
  'Archived',
  'Approved',
])

export const zViewDocumentIndexFilterQuery = z.object({
  customerUuid: z.string().uuid(),
  refExt: z.string().optional(),
  status: zSharepointDocumentStatus.optional(),
  transportType: z.string().optional(),
  viewName: zSharepointDocumentViewName,
  wasteProducerIds: z.array(z.string()),
  year: z.string().optional(),
})

export const zSharepointDocumentType = z.unknown()

export const zViewDocumentIndexItemResponse = z.object({
  id: z.string(),
  actionAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  name: z.string(),
  applicableFrom: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  applicableTill: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  status: z.union([
    zSharepointDocumentStatus,
    z.null(),
  ]),
  tfsType: z.union([
    zSharepointDocumentType,
    z.null(),
  ]),
  wasteProducer: z.string(),
})

export const zViewDocumentIndexResponseMeta = z.object({
  next: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewDocumentIndexResponse = z.object({
  items: z.array(zViewDocumentIndexItemResponse),
  meta: zViewDocumentIndexResponseMeta,
})

export const zViewUserSiteIndexWasteProducerResponse = z.object({
  id: z.number(),
  name: z.string(),
})

export const zViewUserSiteIndexResponse = z.object({
  uuid: z.string().uuid(),
  name: z.string(),
  wasteProducers: z.array(zViewUserSiteIndexWasteProducerResponse),
})

export const zDynamicColumnNames = z.enum([
  'docType',
  'salesOrder',
  'salesOrderLine',
  'description',
  'collectionDate',
  'deliveryDate',
  'dispositionPickUpDate',
  'dispositionDeliveryDate',
  'treatmentCentre',
  'endTreatmentCentre',
  'ewcCode',
  'wtfForm',
  'tfs',
  'disposalDate',
  'printDate',
  'wasteProducerName',
  'wasteProducerId',
  'contract',
  'contractItem',
  'invoice',
  'pickUpAddressName',
  'pickUpAddressId',
  'customerId',
  'customerName',
  'contractNumber',
  'customerReference',
  'wasteMaterial',
  'materialNumber',
  'treatmentCenterName',
  'installationName',
  'asn',
  'isHazardous',
  'packaged',
  'tcNumber',
  'materialAnalysis',
  'endTreatmentCenterId',
  'endTreatmentCenterName',
  'remarks',
  'processCode',
  'esnNumber',
  'deliveryInfo',
  'materialType',
  'packagingIndicator',
  'invoiceNumber',
  'status',
  'payerId',
  'payerName',
  'issuedOn',
  'firstReminderMailStatus',
  'firstReminderOn',
  'secondReminderMailStatus',
  'secondReminderOn',
  'thirdReminderMailStatus',
  'autoApprovedOn',
  'netAmount',
  'vatAmount',
  'currency',
  'customerApprovalBy',
  'customerApprovalDate',
  'poNumber',
  'accountDocumentNumber',
  'estimatedWeightOrVolumeValue',
  'estimatedWeightOrVolumeUnit',
  'packagingType',
  'quantityPackages',
  'quantityLabels',
  'quantityPallets',
  'unNumber',
  'packingGroup',
  'dangerLabel1',
  'dangerLabel2',
  'dangerLabel3',
  'costCenter',
  'containerType',
  'containerVolumeSize',
  'containerNumber',
  'containerTransportType',
  'isContainerCovered',
  'tankerType',
  'totalQuantityPallets',
  'isReturnPackaging',
  'packagingRemark',
  'reconciliationNumber',
  'hazardInducers',
  'quantityContainers',
  'tfsNumber',
  'serialNumber',
  'dueOn',
  'type',
  'accountManagerName',
  'companyName',
  'requestNumber',
  'transportMode',
  'dateOfRequest',
  'accountManager',
  'isTransportByIndaver',
  'requestedStartDate',
  'requestedEndDate',
  'confirmedTransportDate',
  'nameOfApplicant',
  'orderNumber',
  'nameInstallation',
  'disposalCertificateNumber',
  'ewc',
  'inquiryNumber',
  'wasteStreamName',
  'date',
  'contractId',
  'salesOrganisationId',
  'salesOrganisationName',
  'requestorName',
  'conformityCheck',
  'shipmentId',
  'salesDoc',
  'weightOrVolume',
  'unit',
  'transportDate',
])

export const zDynamicTableColumnIndexView = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isHidable: z.boolean(),
  name: zDynamicColumnNames,
  applicableFields: z.array(z.string()),
  filterableField: z.union([
    z.string(),
    z.null(),
  ]),
  searchableFields: z.array(z.string()),
  sortableFields: z.array(z.string()),
})

export const zDynamicTableIndexColumnResponse = z.object({ items: z.array(zDynamicTableColumnIndexView) })

export const zColumnNotFoundError = z.object({
  code: z.enum([
    'column_not_found',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zColumnNotFilterableError = z.object({
  code: z.enum([
    'column_not_filterable',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zColumnNotSortableError = z.object({
  code: z.enum([
    'column_not_sortable',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zDuplicateColumnError = z.object({
  code: z.enum([
    'duplicate_column',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zDynamicTableViewFilterCommand = z.object({
  columnUuid: z.string().uuid(),
  value: z.union([
    z.string(),
    z.array(z.string()),
    z.boolean(),
    z.object({
      key: z.string().optional(),
      value: z.string().optional(),
    }),
    z.array(z.object({
      key: z.string().optional(),
      value: z.string().optional(),
    })),
  ]),
})

export const zDynamicTableViewSortCommand = z.object({
  columnUuid: z.string().uuid(),
  direction: zSortDirection,
})

export const zDynamicTableViewVisibleColumnsCommand = z.object({ columnUuid: z.string().uuid() })

export const zCreateDynamicTableViewCommand = z.object({
  isDefault: z.boolean().optional(),
  isGlobal: z.boolean().optional(),
  isGlobalDefault: z.boolean().optional(),
  filters: z.array(zDynamicTableViewFilterCommand),
  sorts: z.array(zDynamicTableViewSortCommand),
  viewName: z.string(),
  visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand),
})

export const zCreateDynamicTableViewResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zVisibilityConfigurationResponse = z.object({
  uuid: z.string().uuid(),
  order: z.number(),
})

export const zSortConfigurationResponse = z.object({
  uuid: z.string().uuid(),
  direction: zSortDirection,
  order: z.number(),
})

export const zFilterConfigurationResponse = z.object({
  uuid: z.string().uuid(),
  value: z.union([
    z.string(),
    z.array(z.string()),
    z.boolean(),
    z.object({
      key: z.string().optional(),
      value: z.string().optional(),
    }),
    z.array(z.object({
      key: z.string().optional(),
      value: z.string().optional(),
    })),
  ]),
})

export const zViewDefaultDynamicTableViewResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isDefaultGlobal: z.boolean(),
  isGlobal: z.boolean(),
  isUserDefault: z.boolean(),
  name: z.string(),
  filters: z.array(zFilterConfigurationResponse),
  sorts: z.array(zSortConfigurationResponse),
  visibleColumns: z.array(zVisibilityConfigurationResponse),
})

export const zUpdateDynamicTableViewCommand = z.object({
  isDefault: z.boolean().optional(),
  isGlobal: z.boolean().optional(),
  isGlobalDefault: z.boolean().optional(),
  filters: z.array(zDynamicTableViewFilterCommand).optional(),
  sorts: z.array(zDynamicTableViewSortCommand).optional(),
  viewName: z.string().optional(),
  visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand).optional(),
})

export const zUpdateDynamicTableViewResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zDynamicTableViewResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isDefaultGlobal: z.boolean(),
  isGlobal: z.boolean(),
  isUserDefault: z.boolean(),
  name: z.string(),
  filters: z.array(zFilterConfigurationResponse),
  sorts: z.array(zSortConfigurationResponse),
  visibleColumns: z.array(zVisibilityConfigurationResponse),
})

export const zDynamicTableViewIndexResponse = z.object({
  items: z.array(zDynamicTableViewResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zGlobalDefaultViewNotDeletable = z.object({
  code: z.enum([
    'global_default_view_not_deletable',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zLastGlobalViewNotDeletable = z.object({
  code: z.enum([
    'last_global_view_not_deletable',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zEwcCodeResponse = z.object({
  code: z.string(),
  description: z.string(),
})

export const zViewEwcCodeIndexResponse = z.object({ items: z.array(zEwcCodeResponse) })

export const zDownloadGuidanceLetterType = z.enum([
  'print',
  'preview',
  'attachment',
])

export const zGuidanceLetterDownloadTypeNotFoundErrorMeta = z.object({ type: zDownloadGuidanceLetterType })

export const zGuidanceLetterDownloadTypeNotFoundError = z.object({
  code: z.enum([
    'guidance_letter_download_type_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zGuidanceLetterDownloadTypeNotFoundErrorMeta,
})

export const zGuidanceLetterNotFoundErrorMeta = z.object({ id: z.string() })

export const zGuidanceLetterNotFoundError = z.object({
  code: z.enum([
    'guidance_letter_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zGuidanceLetterNotFoundErrorMeta,
})

export const zViewGuidanceLetterIndexSortQueryKey = z.enum([
  'shipmentId',
  'salesDoc',
  'requestNumber',
  'wasteMaterial',
  'transportDate',
  'customerId',
  'wasteProducerId',
  'pickUpAddressId',
  'customerName',
  'wasteProducerName',
  'pickUpAddressName',
])

export const zViewGuidanceLetterIndexSortQuery = z.object({
  key: zViewGuidanceLetterIndexSortQueryKey,
  order: zSortDirection,
})

export const zViewGuidanceLetterIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  pickUpAddressId: z.string().optional(),
  shipmentId: z.string().optional(),
  wasteProducerId: z.string().optional(),
  transportDate: zDateRange.optional(),
  requestNumber: z.string().optional(),
  salesDoc: z.string().optional(),
  wasteMaterial: z.string().optional(),
})

export const zGuidanceLetterResponse = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  shipmentId: z.string(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  transportDate: z.union([
    z.string(),
    z.null(),
  ]),
  attachment: z.boolean(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  guidanceLetter: z.boolean(),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  requestNumber: z.union([
    z.string(),
    z.null(),
  ]),
  salesDoc: z.string(),
  unit: z.union([
    z.string(),
    z.null(),
  ]),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
  weightOrVolume: z.union([
    z.number(),
    z.null(),
  ]),
})

export const zViewGuidanceLetterIndexResponse = z.object({
  items: z.array(zGuidanceLetterResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zDownloadInvoiceCertificateCommand = z.object({ fileName: z.string() })

export const zInvoiceDocumentNotFoundErrorMeta = z.object({ invoiceNumber: z.string() })

export const zInvoiceDocumentNotFoundError = z.object({
  code: z.enum([
    'invoice_document_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zInvoiceDocumentNotFoundErrorMeta,
})

export const zInvoiceNotFoundErrorMeta = z.object({ invoiceNumber: z.string() })

export const zInvoiceNotFoundError = z.object({
  code: z.enum([
    'invoice_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zInvoiceNotFoundErrorMeta,
})

export const zViewInvoiceIndexSortKey = z.enum([
  'invoiceNumber',
  'customerReference',
  'issueDate',
  'payerId',
  'payerName',
  'customerName',
  'customerId',
  'accountDocumentNumber',
  'dueDate',
  'netAmount',
  'companyName',
])

export const zViewInvoiceIndexSortQuery = z.object({
  key: zViewInvoiceIndexSortKey,
  order: zSortDirection,
})

export const zInvoiceStatus = z.enum([
  'overdue',
  'outstanding',
  'cleared',
])

export const zInvoiceColumnName = z.enum([
  'invoiceNumber',
  'status',
  'issuedOn',
  'customerName',
  'dueOn',
  'customerReference',
  'type',
  'payerId',
  'payerName',
  'netAmount',
  'vatAmount',
  'currency',
  'accountDocumentNumber',
  'accountManagerName',
  'companyName',
])

export const zExportInvoicesExcelFilterQuery = z.object({
  columns: z.array(zInvoiceColumnName),
  statuses: z.array(zInvoiceStatus),
  translatedColumns: z.array(z.string()),
})

export const zInvoiceFilterType = z.enum([
  'invoice',
  'credit_memo',
  'debit_memo',
])

export const zViewInvoiceIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  payerId: z.string().optional(),
  dueDate: zDateRange.optional(),
  issueDate: zDateRange.optional(),
  accountDocumentNumber: z.string().optional(),
  accountManagerName: z.string().optional(),
  companyName: z.string().optional(),
  customerReference: z.string().optional(),
  invoiceNumber: z.string().optional(),
  statuses: z.array(zInvoiceStatus),
  type: zInvoiceFilterType.optional(),
})

export const zInvoiceType = z.enum([
  'invoice',
  'credit_memo',
  'debit_memo',
  'unknown',
])

export const zInvoiceResponse = z.object({
  customerId: z.string(),
  payerId: z.string(),
  dueOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  issuedOn: z.string().date(),
  accountDocumentNumber: z.union([
    z.string(),
    z.null(),
  ]),
  accountManagerName: z.union([
    z.string(),
    z.null(),
  ]),
  companyName: z.string(),
  currency: z.string(),
  customerName: z.string(),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  invoiceNumber: z.string(),
  netAmount: z.string(),
  payerName: z.string(),
  status: zInvoiceStatus,
  type: zInvoiceType,
  vatAmount: z.string(),
})

export const zViewInvoiceIndexResponse = z.object({
  items: z.array(zInvoiceResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewInvoiceResponse = z.object({
  payerId: z.string(),
  dueOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  issuedOn: z.string().date(),
  accountDocumentNumber: z.union([
    z.string(),
    z.null(),
  ]),
  accountManagerName: z.union([
    z.string(),
    z.null(),
  ]),
  companyName: z.string(),
  currency: z.string(),
  customerName: z.string(),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  invoiceNumber: z.string(),
  netAmount: z.string(),
  payerName: z.string(),
  status: zInvoiceStatus,
  type: zInvoiceType,
  vatAmount: z.string(),
})

export const zDraftInvoiceStatus = z.enum([
  'approved_by_customer',
  'auto_approved',
  'internal_approved',
  'rejected_by_customer',
  'rejected_by_indaver',
  'to_be_approved_by_customer',
  'to_be_approved_by_indaver',
])

export const zMailStatus = z.enum([
  'sent',
  'not_sent',
])

export const zViewDraftInvoiceDetailResponse = z.object({
  customerId: z.string(),
  payerId: z.string(),
  autoApprovedOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  customerApprovalDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  firstReminderOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  issuedOn: z.string().date(),
  secondReminderOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  accountDocumentNumber: z.union([
    z.string(),
    z.null(),
  ]),
  currency: z.string(),
  customerApprovalBy: z.union([
    z.string(),
    z.null(),
  ]),
  firstReminderMailStatus: zMailStatus,
  invoiceNumber: z.string(),
  netAmount: z.string(),
  payerName: z.string(),
  poNumber: z.union([
    z.string(),
    z.null(),
  ]),
  secondReminderMailStatus: zMailStatus,
  status: zDraftInvoiceStatus,
  thirdReminderMailStatus: zMailStatus,
  vatAmount: z.string(),
})

export const zViewDraftInvoiceIndexSortQueryKey = z.enum([
  'invoiceNumber',
])

export const zViewDraftInvoiceIndexSortQuery = z.object({
  key: zViewDraftInvoiceIndexSortQueryKey,
  order: zSortDirection,
})

export const zDraftInvoiceFilterStatus = z.enum([
  'to_be_approved',
  'approved',
  'rejected',
])

export const zViewDraftInvoiceIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  payerId: z.string().optional(),
  issuedOn: zDateRange.optional(),
  invoiceNumber: z.string().optional(),
  statuses: z.array(zDraftInvoiceFilterStatus).min(1),
})

export const zDraftInvoiceResponse = z.object({
  customerId: z.string(),
  payerId: z.string(),
  autoApprovedOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  customerApprovalDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  firstReminderOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  issuedOn: z.string().date(),
  secondReminderOn: z.union([
    z.string().date(),
    z.null(),
  ]),
  accountDocumentNumber: z.union([
    z.string(),
    z.null(),
  ]),
  currency: z.string(),
  customerApprovalBy: z.union([
    z.string(),
    z.null(),
  ]),
  firstReminderMailStatus: zMailStatus,
  invoiceNumber: z.string(),
  netAmount: z.string(),
  payerName: z.string(),
  poNumber: z.union([
    z.string(),
    z.null(),
  ]),
  secondReminderMailStatus: zMailStatus,
  status: zDraftInvoiceStatus,
  thirdReminderMailStatus: zMailStatus,
  vatAmount: z.string(),
})

export const zViewDraftInvoiceIndexResponse = z.object({
  items: z.array(zDraftInvoiceResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zExportDraftInvoicesExcelFilterQuery = z.object({
  columns: z.array(zInvoiceColumnName),
  statuses: z.array(zDraftInvoiceFilterStatus),
  translatedColumns: z.array(z.string()),
})

export const zNonApproveOrRejectableDraftInvoiceError = z.object({
  code: z.enum([
    'non_approve_or_rejectable_draft_invoice',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zApproveDraftInvoiceCommand = z.object({
  poNumber: z.union([
    z.string(),
    z.null(),
  ]),
  remark: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zRejectDraftInvoiceCommand = z.object({ remark: z.string() })

export const zAlreadySubscribedError = z.object({
  code: z.enum([
    'already_subscribed',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zOptInAlreadyRequestedError = z.object({
  code: z.enum([
    'opt_in_already_requested',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zSubscribeToNewsletterCommand = z.object({ email: z.string() })

export const zCreateNewsItemTranslationCommand = z.object({
  title: z.string(),
  content: z.record(z.unknown()),
  language: zLocale,
})

export const zNewsItemSalesOrganisation = z.object({
  id: z.string(),
  name: z.string(),
})

export const zCreateNewsItemCommand = z.object({
  imageUuid: z.string().uuid(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.string().date(),
  newsItemTranslations: z.array(zCreateNewsItemTranslationCommand),
  salesOrganisations: z.array(zNewsItemSalesOrganisation),
  videoUrl: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zCreateNewsItemResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zNewsItemTranslationExistsError = z.object({
  code: z.enum([
    'news-item-translation-exists',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNoStartDateOrEndDateExpectedError = z.object({
  code: z.enum([
    'no-start-date-or-end-date-expected',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zUpdateNewsItemTranslationCommand = z.object({
  title: z.string().optional(),
  content: z.record(z.unknown()).optional(),
  language: zLocale,
})

export const zUpdateNewsItemCommand = z.object({
  imageUuid: z.string().uuid().optional(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.string().date().optional(),
  newsItemTranslations: z.array(zUpdateNewsItemTranslationCommand).optional(),
  salesOrganisations: z.array(zNewsItemSalesOrganisation).optional(),
  videoUrl: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zUpdateNewsItemResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zNewsItemNotFoundErrorMeta = z.object({ uuid: z.string().uuid() })

export const zNewsItemNotFoundError = z.object({
  code: z.enum([
    'news_item_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zNewsItemNotFoundErrorMeta,
})

export const zFileResponse = z.object({
  uuid: z.string().uuid(),
  name: z.string(),
  mimeType: zMimeType,
  url: z.string(),
})

export const zViewNewsItemResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  updatedAt: z.string().datetime(),
  author: zViewNewsItemAuthorResponse,
  image: zFileResponse,
  publishStatus: zPublishStatus,
  salesOrganisations: z.array(zNewsItemSalesOrganisation),
  translations: z.array(zViewNewsItemTranslationResponse),
  videoUrl: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewNewsItemTranslationIndexResponse = z.object({
  uuid: z.string().uuid(),
  title: z.union([
    z.string(),
    z.null(),
  ]),
  language: zLocale,
})

export const zNewsItemIndexView = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.string().datetime(),
  updatedAt: z.string().datetime(),
  author: zViewNewsItemAuthorResponse,
  publishStatus: zPublishStatus,
  translations: z.array(zViewNewsItemTranslationIndexResponse),
})

export const zViewNewsIndexResponse = z.object({
  items: z.array(zNewsItemIndexView),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewDashboardNewItemFilterQuery = z.object({ excludeNewsItemUuids: z.array(z.string()).optional() })

export const zDashboardNewsItemIndexView = z.object({
  uuid: z.string().uuid(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  image: z.union([
    zFileResponse,
    z.null(),
  ]),
  translation: zViewNewsItemTranslationResponse,
  videoUrl: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewDashboardNewsIndexResponse = z.object({
  items: z.array(zDashboardNewsItemIndexView),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewDashboardNewsItemResponse = z.object({
  uuid: z.string().uuid(),
  endDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  image: z.union([
    zFileResponse,
    z.null(),
  ]),
  translation: zViewNewsItemTranslationResponse,
  videoUrl: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zCreatePackagingRequestCommand = z.record(z.unknown())

export const zCreatePackagingRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zWasteProducerResponse = z.object({
  id: z.string(),
  name: z.string(),
  address: z.union([
    z.string(),
    z.unknown(),
    z.null(),
  ]),
})

export const zPickUpAddressResponse = z.object({
  id: z.string(),
  name: z.string(),
  address: z.union([
    z.string(),
    z.unknown(),
    z.null(),
  ]),
})

export const zPackagingRequestMaterialResponse = z.object({
  contractLineId: z.string(),
  isSales: z.union([
    z.boolean(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractLineAccessible: z.boolean().optional(),
  contractNumber: z.string(),
  costCenter: z.union([
    z.string(),
    z.null(),
  ]),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  poNumber: z.union([
    z.string(),
    z.null(),
  ]),
  quantity: z.number(),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zContactTypeResponse = z.object({
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
})

export const zViewPackagingRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  updatedAt: z.string().datetime(),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  deliveryAddress: z.union([
    zPickUpAddressResponse,
    z.null(),
  ]),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zViewWasteProducerIndexFilterQuery = z.object({ customerId: z.string().optional() })

export const zViewWasteProducerIndexResponse = z.object({
  items: z.array(zWasteProducerResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewSuggestedWasteProducersFilterQuery = z.object({
  customerId: z.string().uuid(),
  requestType: zRequestType,
})

export const zViewSuggestedWasteProducersResponse = z.object({ items: z.array(zWasteProducerResponse) })

export const zViewPickUpAddressIndexFilterQuery = z.object({ customerId: z.string().optional() })

export const zViewPickUpAddressIndexResponse = z.object({
  items: z.array(zPickUpAddressResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewSuggestedPickUpAddressesFilterQuery = z.object({
  customerId: z.string().uuid(),
  requestType: zRequestType,
})

export const zViewSuggestedPickUpAddressesResponse = z.object({ items: z.array(zPickUpAddressResponse) })

export const zCustomerNotAccessibleError = z.object({
  code: z.enum([
    'customer_not_accessible',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zWasteProducerNotAccessibleError = z.object({
  code: z.enum([
    'waste_producer_not_accessible',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zPickUpAddressNotAccessibleError = z.object({
  code: z.enum([
    'pick_up_address_not_accessible',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zPackagingRequestMaterialCommand = z.object({
  contractLineId: z.string(),
  isSales: z.union([
    z.boolean(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractNumber: z.string(),
  costCenter: z.union([
    z.string().max(25),
    z.null(),
  ]),
  materialNumber: z.string(),
  poNumber: z.union([
    z.string().max(35),
    z.null(),
  ]),
  quantity: z.number().gte(0),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zContact = z.object({
  email: z.string().email().max(241),
  firstName: z.string().max(40),
  lastName: z.string().max(40),
})

export const zUpdatePackagingRequestCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  deliveryAddressId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
  remarks: z.union([
    z.string().max(200),
    z.null(),
  ]).optional(),
  sendCopyToContacts: z.array(zContact).optional(),
})

export const zUpdatePackagingRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zContractLineNotOfCustomerErrorMeta = z.object({
  customerId: z.string(),
  contractLineNumber: z.string(),
  contractNumber: z.string(),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zContractLineNotOfCustomerError = z.object({
  code: z.enum([
    'contract_line_not_of_customer',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
  meta: zContractLineNotOfCustomerErrorMeta,
})

export const zContractLineNotOfPickUpAddressesErrorMeta = z.object({
  contractLineNumber: z.string(),
  contractNumber: z.string(),
  pickUpAddressIds: z.array(z.string()),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zContractLineNotOfPickUpAddressesError = z.object({
  code: z.enum([
    'contract_line_not_of_pick_up_addresses',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
  meta: zContractLineNotOfPickUpAddressesErrorMeta,
})

export const zUpdateOnlyPackagingRequestError = z.object({
  code: z.enum([
    'update_only_packaging_request',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zPackagingRequestAlreadySubmitted = z.object({
  code: z.enum([
    'packaging_request_already_submitted',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zSubmitPackagingRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  submittedOn: z.string().datetime(),
  updatedAt: z.string().datetime(),
  requestNumber: z.string(),
})

export const zBulkDeletePackagingRequestCommand = z.object({ packagingRequestUuids: z.array(z.string()) })

export const zPickUpRequestNotFoundErrorMeta = z.object({
  uuid: z.string().optional(),
  requestNumber: z.string().optional(),
})

export const zPickUpRequestNotFoundError = z.object({
  code: z.enum([
    'pick_up_request_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zPickUpRequestNotFoundErrorMeta,
})

export const zCopyNonSubmittedPickUpRequestError = z.object({
  code: z.enum([
    'copy_non_submitted_pick_up_request',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zCopyPackagingRequestSapResponse = z.object({ uuid: z.string().uuid() })

export const zViewPackagingTypeIndexFilterQuery = z.object({ customerId: z.string() })

export const zPackagingTypeResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewPackagingTypeIndexResponse = z.object({ items: z.array(zPackagingTypeResponse) })

export const zViewPayerIndexFilterQuery = z.object({ customerId: z.string() })

export const zPayerResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewPayerIndexResponse = z.object({ items: z.array(zPayerResponse) })

export const zGetIsPoNumberAndCostCenterRequiredFilterQuery = z.object({ customerId: z.string() })

export const zGetIsPoNumberAndCostCenterRequiredResponse = z.object({
  isCostCenterRequired: z.boolean(),
  isPoNumberRequired: z.boolean(),
})

export const zViewPickUpRequestIndexSortKey = z.enum([
  'requestNumber',
  'wasteMaterial',
  'customerReference',
  'contractNumber',
  'contractItem',
  'wasteProducerId',
  'pickUpAddressId',
  'treatmentCenterName',
  'requestedStartDate',
  'confirmedTransportDate',
  'salesOrder',
  'orderNumber',
  'containerNumber',
  'installationName',
  'disposalCertificateNumber',
  'dateOfRequest',
])

export const zViewPickUpRequestIndexSortQuery = z.object({
  key: zViewPickUpRequestIndexSortKey,
  order: zSortDirection,
})

export const zPickUpRequestStatus = z.enum([
  'draft',
  'pending',
  'confirmed',
  'cancelled',
  'indascan_draft',
])

export const zTransportMode = z.enum([
  'packaging-request-order',
  'packaged-curtain-sider-truck',
  'bulk-skips-container',
  'bulk-vacuum-tankers-road-tankers',
  'bulk-iso-tank',
])

export const zViewPickUpRequestIndexFilterQuery = z.object({
  customerId: z.string().optional(),
  pickUpAddressId: z.string().optional(),
  wasteProducerId: z.string().optional(),
  confirmedTransportDate: zDateRange.optional(),
  requestDate: zDateRange.optional(),
  isHazardous: z.boolean().optional(),
  isTransportByIndaver: z.boolean().optional(),
  accountManager: z.string().optional(),
  containerNumber: z.string().optional(),
  contractItem: z.string().optional(),
  contractNumber: z.string().optional(),
  costCenter: z.string().optional(),
  customerReference: z.string().optional(),
  dateOfRequest: zDateRange.optional(),
  deliveryInfo: z.string().optional(),
  disposalCertificateNumber: z.string().optional(),
  ewc: z.string().optional(),
  materialAnalysis: z.string().optional(),
  nameInstallation: z.string().optional(),
  nameOfApplicant: z.string().optional(),
  orderNumber: z.string().optional(),
  requestNumber: z.string().optional(),
  salesOrder: z.string().optional(),
  statuses: z.array(zPickUpRequestStatus),
  transportMode: zTransportMode.optional(),
  treatmentCenterName: z.string().optional(),
  wasteMaterial: z.string().optional(),
})

export const zPickUpRequestResponse = z.object({
  uuid: z.union([
    z.string(),
    z.null(),
  ]),
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.array(z.string()),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  confirmedTransportDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  createdAt: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  requestedEndDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  requestedStartDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  isHazardous: z.array(z.boolean()),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  accountManager: z.union([
    z.string(),
    z.null(),
  ]),
  containerNumber: z.array(z.string()),
  contractItem: z.array(z.string()),
  contractNumber: z.array(z.string()),
  costCenter: z.array(z.string()),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  dateOfRequest: z.union([
    z.string(),
    z.null(),
  ]),
  deliveryInfo: z.union([
    z.string(),
    z.null(),
  ]),
  disposalCertificateNumber: z.union([
    z.string(),
    z.null(),
  ]),
  ewc: z.union([
    z.string(),
    z.null(),
  ]),
  materialAnalysis: z.union([
    z.string(),
    z.null(),
  ]),
  nameInstallation: z.union([
    z.string(),
    z.null(),
  ]),
  nameOfApplicant: z.union([
    z.string(),
    z.null(),
  ]),
  orderNumber: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressName: z.array(z.string()),
  requestNumber: z.union([
    z.string(),
    z.null(),
  ]),
  salesOrder: z.union([
    z.string(),
    z.null(),
  ]),
  status: zPickUpRequestStatus,
  tfsNumber: z.union([
    z.string(),
    z.null(),
  ]),
  transportMode: z.union([
    zTransportMode,
    z.null(),
  ]),
  treatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  wasteMaterial: z.array(z.string()),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewPickUpRequestIndexResponse = z.object({
  items: z.array(zPickUpRequestResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zCreatePickUpRequestCommand = z.record(z.unknown())

export const zCreatePickUpRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zPickUpTransportMode = z.enum([
  'packaged-curtain-sider-truck',
  'bulk-skips-container',
  'bulk-vacuum-tankers-road-tankers',
  'bulk-iso-tank',
])

export const zWasteMeasurementUnit = z.enum([
  'kg',
  'm3',
  'pc',
  'to',
  'yd3',
])

export const zMaterialResponse = z.object({
  contractLineId: z.string(),
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  isContainerCovered: z.union([
    z.boolean(),
    z.null(),
  ]),
  isHazardous: z.union([
    z.boolean(),
    z.null(),
  ]),
  adrClass: z.union([
    z.string(),
    z.null(),
  ]),
  asn: z.union([
    z.string(),
    z.null(),
  ]),
  containerNumber: z.union([
    z.string(),
    z.null(),
  ]),
  containerTransportType: z.union([
    z.string(),
    z.null(),
  ]),
  containerType: z.union([
    z.string(),
    z.null(),
  ]),
  containerVolumeSize: z.union([
    z.string(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractLineAccessible: z.union([
    z.boolean(),
    z.null(),
  ]),
  contractNumber: z.string(),
  costCenter: z.union([
    z.string(),
    z.null(),
  ]),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]),
  dangerLabel1: z.union([
    z.string(),
    z.null(),
  ]),
  dangerLabel2: z.union([
    z.string(),
    z.null(),
  ]),
  dangerLabel3: z.union([
    z.string(),
    z.null(),
  ]),
  deliveryInfo: z.union([
    z.string(),
    z.null(),
  ]),
  endTreatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  esnNumber: z.union([
    z.string(),
    z.null(),
  ]),
  estimatedWeightOrVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  estimatedWeightOrVolumeValue: z.union([
    z.number(),
    z.null(),
  ]),
  ewcCode: z.union([
    z.string(),
    z.null(),
  ]),
  hazardInducers: z.union([
    z.string(),
    z.null(),
  ]),
  installationName: z.union([
    z.string(),
    z.null(),
  ]),
  materialAnalysis: z.union([
    z.string(),
    z.null(),
  ]),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  materialType: z.union([
    z.string(),
    z.null(),
  ]),
  packaged: z.union([
    zContractLinePackagingType,
    z.null(),
  ]),
  packagingIndicator: z.union([
    z.string(),
    z.null(),
  ]),
  packagingType: z.union([
    z.string(),
    z.null(),
  ]),
  packingGroup: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  poNumber: z.union([
    z.string(),
    z.null(),
  ]),
  position: z.union([
    z.string(),
    z.null(),
  ]),
  processCode: z.union([
    z.string(),
    z.null(),
  ]),
  quantityContainers: z.union([
    z.number(),
    z.null(),
  ]),
  quantityLabels: z.union([
    z.number(),
    z.null(),
  ]),
  quantityPackages: z.union([
    z.number(),
    z.null(),
  ]),
  quantityPallets: z.union([
    z.number(),
    z.null(),
  ]),
  reconciliationNumber: z.union([
    z.string(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  serialNumber: z.union([
    z.string(),
    z.null(),
  ]),
  tankerType: z.union([
    z.string(),
    z.null(),
  ]),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]),
  tfs: z.union([
    z.boolean(),
    z.null(),
  ]),
  tfsNumber: z.union([
    z.string(),
    z.null(),
  ]),
  treatmentCenterName: z.union([
    z.string(),
    z.null(),
  ]),
  unNumber: z.union([
    z.string(),
    z.null(),
  ]),
  unNumberDescription: z.union([
    z.string(),
    z.null(),
  ]),
  unNumberHazardous: z.union([
    z.boolean(),
    z.null(),
  ]),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zFileLinkResponse = z.object({
  uuid: z.union([
    z.string().uuid(),
    z.null(),
  ]),
  name: z.string(),
  mimeType: z.union([
    zMimeType,
    z.null(),
  ]),
  order: z.union([
    z.number(),
    z.null(),
  ]),
  url: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewPickUpRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]),
  updatedAt: z.string().datetime(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]),
  additionalFiles: z.array(zFileLinkResponse),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  materials: z.array(zMaterialResponse),
  needsWicConfirmation: z.boolean(),
  packagingRemark: z.union([
    z.string(),
    z.null(),
  ]),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
  pickUpAddresses: z.array(zPickUpAddressResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  status: zPickUpRequestStatus,
  totalQuantityPallets: z.union([
    z.number(),
    z.null(),
  ]),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zMissingEwcLevelsError = z.object({
  code: z.enum([
    'missing_ewc_levels',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zPackingGroup = z.enum([
  'not-applicable',
  'one',
  'two',
  'three',
])

export const zPickUpRequestMaterialCommand = z.object({
  contractLineId: z.string(),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  isContainerCovered: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isHazardous: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  adrClass: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  asn: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  containerNumber: z.union([
    z.string().max(40),
    z.null(),
  ]).optional(),
  containerTransportType: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  containerType: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  containerVolumeSize: z.union([
    z.string().max(13),
    z.null(),
  ]).optional(),
  contractItem: z.string(),
  contractNumber: z.string(),
  costCenter: z.union([
    z.string().max(25),
    z.null(),
  ]).optional(),
  customerReference: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  dangerLabel1: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  dangerLabel2: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  dangerLabel3: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  estimatedWeightOrVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]).optional(),
  estimatedWeightOrVolumeValue: z.union([
    z.number().gte(0.01).lte(99_999_999_999.99),
    z.null(),
  ]).optional(),
  ewcCode: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  hazardInducers: z.union([
    z.string().max(100),
    z.null(),
  ]).optional(),
  materialNumber: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  packagingType: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  packingGroup: z.union([
    zPackingGroup,
    z.null(),
  ]).optional(),
  poNumber: z.union([
    z.string().max(25),
    z.null(),
  ]).optional(),
  processCode: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  quantityContainers: z.union([
    z.number().gte(0).lte(9),
    z.null(),
  ]).optional(),
  quantityLabels: z.union([
    z.number().gte(0).lte(999),
    z.null(),
  ]).optional(),
  quantityPackages: z.union([
    z.number().gte(0).lte(999),
    z.null(),
  ]).optional(),
  quantityPallets: z.union([
    z.number().gte(0).lte(999),
    z.null(),
  ]).optional(),
  reconciliationNumber: z.union([
    z.string().max(6),
    z.null(),
  ]).optional(),
  serialNumber: z.union([
    z.string().max(4),
    z.null(),
  ]).optional(),
  tankerType: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  tcNumber: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  tfs: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  tfsNumber: z.union([
    z.string().max(15),
    z.null(),
  ]).optional(),
  unNumber: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  unNumberDescription: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  unNumberHazardous: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zCreateFileLinkCommand = z.object({
  fileUuid: z.string().uuid(),
  order: z.number().int(),
})

export const zUpdatePickUpRequestCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]).optional(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  additionalFiles: z.array(zCreateFileLinkCommand).optional(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  materials: z.array(zPickUpRequestMaterialCommand).optional(),
  packagingRemark: z.union([
    z.string().max(200),
    z.null(),
  ]).optional(),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
  pickUpAddressIds: z.array(z.string()).optional(),
  pickUpAddressNames: z.array(z.string()).optional(),
  remarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  sendCopyToContacts: z.array(zContact).optional(),
  totalQuantityPallets: z.union([
    z.number().gte(0).lte(999),
    z.null(),
  ]).optional(),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]).optional(),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zUpdatePickUpRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]),
  needsWicConfirmation: z.boolean(),
})

export const zPickUpRequestAlreadySubmitted = z.object({
  code: z.enum([
    'pick_up_request_already_submitted',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zSubmitPickUpRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  submittedOn: z.string().datetime(),
  updatedAt: z.string().datetime(),
  requestNumber: z.string(),
})

export const zBulkDeletePickUpRequestCommand = z.object({ pickUpRequestUuids: z.array(z.string()) })

export const zViewPickUpRequestSapResponse = z.object({
  confirmedDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]),
  submittedOn: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]),
  additionalFiles: z.array(zFileLinkResponse),
  createdBy: z.union([
    z.string(),
    z.null(),
  ]),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  materials: z.array(zMaterialResponse),
  needsWicConfirmation: z.boolean(),
  packagingRemark: z.union([
    z.string(),
    z.null(),
  ]),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
  pickUpAddresses: z.array(zPickUpAddressResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  requestNumber: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  status: zPickUpRequestStatus,
  totalQuantityPallets: z.union([
    z.number(),
    z.null(),
  ]),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zCreatePickUpRequestTemplateCommand = z.object({ name: z.string().max(150) })

export const zCreatePickUpRequestTemplateResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zUpdatedByUserResponse = z.object({
  uuid: z.string().uuid(),
  email: z.string().email(),
  firstName: z.union([
    z.string(),
    z.null(),
  ]),
  lastName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewPickUpRequestTemplateResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]),
  updatedAt: z.string().datetime(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]),
  additionalFiles: z.array(zFileLinkResponse),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  materials: z.array(zMaterialResponse),
  needsWicConfirmation: z.boolean(),
  packagingRemark: z.union([
    z.string(),
    z.null(),
  ]),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
  pickUpAddresses: z.array(zPickUpAddressResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  status: zPickUpRequestStatus,
  templateName: z.string(),
  templateUpdatedByUser: zUpdatedByUserResponse.optional(),
  totalQuantityPallets: z.union([
    z.number(),
    z.null(),
  ]),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zViewPickUpRequestTemplateIndexSortQueryKey = z.enum([
  'name',
  'createdAt',
  'updatedAt',
  'createdBy',
  'updatedBy',
])

export const zViewPickUpRequestTemplateIndexSortQuery = z.object({
  key: zViewPickUpRequestTemplateIndexSortQueryKey,
  order: zSortDirection,
})

export const zPickUpRequestTemplateResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  name: z.string(),
  createdBy: z.string(),
  updatedBy: z.string(),
})

export const zViewPickUpRequestTemplateIndexResponse = z.object({
  items: z.array(zPickUpRequestTemplateResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zUpdatePickUpRequestTemplateCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]).optional(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  materials: z.array(zPickUpRequestMaterialCommand).optional(),
  packagingRemark: z.union([
    z.string().max(200),
    z.null(),
  ]).optional(),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
  pickUpAddressIds: z.array(z.string()).optional(),
  pickUpAddressNames: z.array(z.string()).optional(),
  remarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  sendCopyToContacts: z.array(zContact).optional(),
  templateName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  totalQuantityPallets: z.union([
    z.number().gte(0).lte(999),
    z.null(),
  ]).optional(),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]).optional(),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zUpdatePickUpRequestTemplateResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zBulkDeletePickUpRequestTemplatesCommand = z.object({ pickUpRequestUuids: z.array(z.string()) })

export const zCreatePickUpRequestFromTemplateCommand = z.record(z.unknown())

export const zCreatePickUpRequestFromTemplateResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zFileNotFoundError = z.object({
  code: z.enum([
    'file_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zUploadDocumentSubmittedPickUpRequestCommand = z.object({ fileUuids: z.array(z.string().uuid()) })

export const zDownloadDocumentSubmittedPickUpRequestCommand = z.object({ fileName: z.string() })

export const zInvalidPickUpRequestCopyErrorMeta = z.object({ requestNumber: z.string() })

export const zInvalidPickUpRequestCopyError = z.object({
  code: z.enum([
    'invalid_pick_up_request_copy',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
  meta: zInvalidPickUpRequestCopyErrorMeta,
})

export const zCopyPickUpRequestSapResponse = z.object({ uuid: z.string().uuid() })

export const zInvalidUpdateSapPickUpRequestError = z.object({
  code: z.enum([
    'invalid_update_sap_pick_up_request',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zPickUpRequestContractLineNotFoundError = z.object({
  code: z.enum([
    'pick_up_request_contract_line_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
})

export const zUpdatePickUpRequestMaterialSapCommand = z.object({
  costCenter: z.union([
    z.string().max(25),
    z.null(),
  ]).optional(),
  poNumber: z.union([
    z.string().max(25),
    z.null(),
  ]).optional(),
  position: z.string().max(6),
})

export const zUpdatePickUpRequestPackagingMaterialSapCommand = z.object({
  contractLineId: z.string(),
  isSales: z.union([
    z.boolean(),
    z.null(),
  ]),
  contractItem: z.string(),
  contractNumber: z.string(),
  costCenter: z.union([
    z.string().max(25),
    z.null(),
  ]),
  materialNumber: z.string(),
  poNumber: z.union([
    z.string().max(35),
    z.null(),
  ]),
  position: z.union([
    z.string().max(6),
    z.null(),
  ]),
  quantity: z.number().gte(0),
  wasteMaterial: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zUpdatePickUpRequestPackagingSapCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  deliveryAddressId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  packagingRequestMaterials: z.array(zUpdatePickUpRequestPackagingMaterialSapCommand).optional(),
})

export const zUpdatePickUpRequestSapCommand = z.object({
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  additionalFiles: z.array(zCreateFileLinkCommand).optional(),
  contacts: z.array(zContact).optional(),
  materials: z.array(zUpdatePickUpRequestMaterialSapCommand).optional(),
  packagingRemark: z.union([
    z.string().max(200),
    z.null(),
  ]).optional(),
  packagingRequest: zUpdatePickUpRequestPackagingSapCommand.optional(),
  remarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
})

export const zInvalidIndascanSubmitStatusError = z.object({
  code: z.enum([
    'invalid_indascan_submit_status',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zViewSalesOrganisationResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewSalesOrganisationIndexResponse = z.object({
  items: z.array(zViewSalesOrganisationResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zTankerTypeResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewTankerTypeIndexResponse = z.object({ items: z.array(zTankerTypeResponse) })

export const zTransportTypeResponse = z.object({
  id: z.string(),
  name: z.string(),
})

export const zViewTransportTypeIndexResponse = z.object({ items: z.array(zTransportTypeResponse) })

export const zViewUnNumberFilterQuery = z.object({ keys: z.array(z.string()).optional() })

export const zViewUnNumberIndexQueryKey = z.object({ number: z.string() })

export const zViewUnNumberIndexPaginationQuery = z.object({
  key: z.union([
    zViewUnNumberIndexQueryKey,
    z.null(),
  ]).optional(),
  limit: z.number().gte(0).lte(100),
})

export const zUnNumberResponse = z.object({
  isHazardous: z.union([
    z.boolean(),
    z.null(),
  ]),
  dangerLabel1: z.union([
    z.string(),
    z.null(),
  ]),
  dangerLabel2: z.union([
    z.string(),
    z.null(),
  ]),
  dangerLabel3: z.union([
    z.string(),
    z.null(),
  ]),
  description: z.union([
    z.string(),
    z.null(),
  ]),
  number: z.string(),
  packingGroup: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewUnNumberIndexResponseMeta = z.object({
  next: z.union([
    zViewUnNumberIndexQueryKey,
    z.null(),
  ]),
})

export const zViewUnNumberIndexResponse = z.object({
  items: z.array(zUnNumberResponse),
  meta: zViewUnNumberIndexResponseMeta,
})

export const zViewUnNumberIndexForPickUpRequestFilterQuery = z.object({
  contractItem: z.string(),
  contractNumber: z.string(),
  tcNumber: z.string().optional(),
})

export const zViewUnNumberIndexForPickUpRequestResponse = z.object({ items: z.array(zUnNumberResponse) })

export const zCreateWasteInquiryCommand = z.record(z.unknown())

export const zCreateWasteInquiryResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zWasteInquirySummaryDocumentNotFoundErrorMeta = z.object({ wasteInquiryId: z.string() })

export const zWasteInquirySummaryDocumentNotFoundError = z.object({
  code: z.enum([
    'waste_inquiry_summary_document_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zWasteInquirySummaryDocumentNotFoundErrorMeta,
})

export const zWasteInquiryNotFoundErrorMeta = z.object({
  uuid: z.string().uuid().optional(),
  inquiryNumber: z.string().optional(),
})

export const zWasteInquiryNotFoundError = z.object({
  code: z.enum([
    'waste_inquiry_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zWasteInquiryNotFoundErrorMeta,
})

export const zWasteInquiryDocumentNotFoundErrorMeta = z.object({ wasteInquiryId: z.string() })

export const zWasteInquiryDocumentNotFoundError = z.object({
  code: z.enum([
    'waste_inquiry_document_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zWasteInquiryDocumentNotFoundErrorMeta,
})

export const zSapFileNotFoundErrorMeta = z.object({ sapFileUuid: z.string().uuid() })

export const zSapFileNotFoundError = z.object({
  code: z.enum([
    'sap_file_not_found',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '404',
  ]),
  meta: zSapFileNotFoundErrorMeta,
})

export const zWasteInquiryStatus = z.enum([
  'draft',
  'new',
  'in_progress',
  'conformity_confirmed',
  'solution_defined',
  'offer_sent',
  'offer_approved',
  'completed',
  'rejected',
])

export const zStateOfMatter = z.enum([
  'gaseous',
  'powder',
  'sludgy',
  'solid',
  'liquid',
  'viscous',
  'liquid-with-solids',
  'no-data-available',
])

export const zWastePackagingType = z.enum([
  'bulk',
  'packaged',
])

export const zWasteFlashpointOption = z.enum([
  '< 23°',
  '23° - 60°',
  '> 60°',
])

export const zWastePhOption = z.enum([
  '< 2',
  '2 - 4',
  '4 - 10',
  '> 10',
])

export const zStableTemperatureType = z.enum([
  'ambient',
  'other',
])

export const zWasteCompositionResponse = z.object({
  name: z.union([
    z.string(),
    z.null(),
  ]),
  maxWeight: z.union([
    z.number().gte(0).lte(100),
    z.null(),
  ]),
  minWeight: z.union([
    z.number().gte(0).lte(100),
    z.null(),
  ]),
})

export const zWasteLegislationOption = z.enum([
  'none',
  'radioactive',
  'cwc',
  'controlled-drugs',
  'drug-precursor',
  'hg-containing',
  'ozon-depleting-substance',
  'animal-byproduct',
  'infectious-waste',
  'svhc',
])

export const zSvhcExtraOption = z.enum([
  'other',
  '< 1 mg/kg',
  '> 1 mg/kg',
])

export const zWastePropertyOption = z.enum([
  'none',
  'explosive',
  'gaseous',
  'peroxide',
  'polymerisation-sensitive',
  'pyrophoric',
  'strong-oxidizing',
  'reactive-with-t-gas',
  'reactive-with-f-gas',
  'high-acute-toxic',
  'thermal-unstable',
])

export const zWasteDischargeFrequency = z.enum([
  'once-off-stream',
  'regular-stream',
  'once-off-campaign',
  'regular-campaign',
])

export const zRegulatedTransportOption = z.enum([
  'yes',
  'no',
  'unknown',
])

export const zWasteInquiryUnNumberResponse = z.object({
  isHazardous: z.boolean(),
  packingGroup: z.union([
    zPackingGroup,
    z.null(),
  ]),
  unNumber: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zWastePackagingOption = z.enum([
  'asf',
  'asp',
  'big-bag',
  'cardboard-box',
  'ibc',
  'metal-drum',
  'oversized-drum',
  'plastic-drum',
  'other',
])

export const zWeightUnit = z.enum([
  'kg',
])

export const zWastePackagingResponse = z.object({
  hasInnerPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  size: z.union([
    z.string(),
    z.null(),
  ]),
  type: z.union([
    zWastePackagingOption,
    z.null(),
  ]),
  weightPerPieceUnit: z.union([
    zWeightUnit,
    z.null(),
  ]),
  weightPerPieceValue: z.union([
    z.number(),
    z.null(),
  ]),
})

export const zWasteTransportType = z.enum([
  'container',
  'skip',
  'tipper-truck',
  'rel-truck',
  'other',
])

export const zContainerLoadingType = z.enum([
  'hook',
  'chain',
])

export const zWasteLoadingType = z.enum([
  'on-waste-collection',
  'before-waste-collection',
])

export const zWasteTransportInOption = z.enum([
  'tank-trailer',
  'tank-container',
  'vacuum-truck',
  'tank-trailer-with-pump',
  'sludge-vacuum-truck',
  'other',
])

export const zWasteLoadingMethod = z.enum([
  'gravitational',
  'pump-from-customer',
  'pump-from-haulier',
])

export const zWasteStoredInOption = z.enum([
  'storage-tank',
  'tank-container',
  'ibcs',
  'drums',
  'other',
])

export const zCollectionRequirementOption = z.enum([
  'tractor',
  'tractor-trailer',
  'tractor-trailer-tank',
])

export const zViewWasteInquiryResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  expectedEndDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  firstCollectionDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  submittedOn: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  updatedAt: z.string().datetime(),
  isLoadingByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isRegulatedTransport: z.union([
    zRegulatedTransportOption,
    z.null(),
  ]),
  isSampleAvailable: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTankOwnedByCustomer: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isUnknownPickUpAddress: z.boolean(),
  isUnknownWasteProducer: z.boolean(),
  additionalFiles: z.array(zFileLinkResponse),
  analysisReportFiles: z.array(zFileLinkResponse),
  averageStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  collectionRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  collectionRequirements: z.union([
    zCollectionRequirementOption,
    z.null(),
  ]),
  composition: z.array(zWasteCompositionResponse),
  containerLoadingType: z.union([
    zContainerLoadingType,
    z.null(),
  ]),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  dischargeFrequency: z.union([
    zWasteDischargeFrequency,
    z.null(),
  ]),
  ewcLevel1Name: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel2Name: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel3Name: z.union([
    z.string(),
    z.null(),
  ]),
  expectedPerCollectionQuantity: z.union([
    z.number(),
    z.null(),
  ]),
  expectedPerCollectionUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  expectedYearlyVolumeAmount: z.union([
    z.number(),
    z.null(),
  ]),
  expectedYearlyVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  flashpoint: z.union([
    zWasteFlashpointOption,
    z.null(),
  ]),
  hazardInducer1: z.union([
    z.string(),
    z.null(),
  ]),
  hazardInducer2: z.union([
    z.string(),
    z.null(),
  ]),
  hazardInducer3: z.union([
    z.string(),
    z.null(),
  ]),
  legislationRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  loadingMethod: z.union([
    zWasteLoadingMethod,
    z.null(),
  ]),
  loadingType: z.union([
    zWasteLoadingType,
    z.null(),
  ]),
  maxStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  minStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  noAnalysisReport: z.boolean(),
  noSds: z.boolean(),
  packaging: z.array(zWastePackagingResponse),
  packagingType: z.union([
    zWastePackagingType,
    z.null(),
  ]),
  ph: z.union([
    zWastePhOption,
    z.null(),
  ]),
  pickUpAddress: z.union([
    zPickUpAddressResponse,
    z.null(),
  ]),
  propertyRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sdsFiles: z.array(zFileLinkResponse),
  selectedLegislationOptions: z.array(zWasteLegislationOption),
  selectedPropertyOptions: z.array(zWastePropertyOption),
  sendCopyToContacts: z.array(zContactTypeResponse),
  specificGravity: z.union([
    z.number(),
    z.null(),
  ]),
  stableTemperatureType: z.union([
    zStableTemperatureType,
    z.null(),
  ]),
  stateOfMatter: z.union([
    zStateOfMatter,
    z.null(),
  ]),
  status: zWasteInquiryStatus,
  storedIn: z.union([
    zWasteStoredInOption,
    z.null(),
  ]),
  svhcExtra: z.union([
    zSvhcExtraOption,
    z.null(),
  ]),
  transportIn: z.union([
    zWasteTransportInOption,
    z.null(),
  ]),
  transportType: z.union([
    zWasteTransportType,
    z.null(),
  ]),
  transportVolumeAmount: z.union([
    z.number(),
    z.null(),
  ]),
  transportVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  unNumbers: z.array(zWasteInquiryUnNumberResponse),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
  wasteStreamDescription: z.union([
    z.string(),
    z.null(),
  ]),
  wasteStreamName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zEwcCodeNotFound = z.object({
  code: z.enum([
    'ewc_code_not_found',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zInvalidStableTemperatureError = z.object({
  code: z.enum([
    'invalid_stable_temperature',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zFileNotAccessibleError = z.object({
  code: z.enum([
    'file_not_accessible',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNoSdsFilesExpected = z.object({
  code: z.enum([
    'no_sds_files_expected',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNoAnalysisReportFilesExpected = z.object({
  code: z.enum([
    'no_analysis_report_files_expected',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNoOptionExpectedWhenNoneSelected = z.object({
  code: z.enum([
    'no_option_expected_when_none_selected',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zNoSvhcExtraExpected = z.object({
  code: z.enum([
    'no_svhc_extra_expected',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zWasteComposition = z.object({
  name: z.union([
    z.string().max(132),
    z.null(),
  ]),
  maxWeight: z.union([
    z.number().gte(0).lte(100),
    z.null(),
  ]),
  minWeight: z.union([
    z.number().gte(0).lte(100),
    z.null(),
  ]),
})

export const zUnNumber = z.object({
  isHazardous: z.boolean(),
  packingGroup: z.union([
    zPackingGroup,
    z.null(),
  ]),
  unNumber: z.union([
    z.string().max(20),
    z.null(),
  ]),
})

export const zWastePackaging = z.object({
  hasInnerPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  remarks: z.union([
    z.string().max(1333),
    z.null(),
  ]),
  size: z.union([
    z.string().max(10),
    z.null(),
  ]),
  type: z.union([
    zWastePackagingOption,
    z.null(),
  ]),
  weightPerPieceUnit: z.union([
    zWeightUnit,
    z.null(),
  ]),
  weightPerPieceValue: z.union([
    z.number().gte(0).lte(99_999_999_999.99),
    z.null(),
  ]),
})

export const zUpdateWasteInquiryCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  expectedEndDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  firstCollectionDate: z.union([
    z.string().date(),
    z.null(),
  ]).optional(),
  isLoadingByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isRegulatedTransport: z.union([
    zRegulatedTransportOption,
    z.null(),
  ]).optional(),
  isSampleAvailable: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isTankOwnedByCustomer: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]).optional(),
  isUnknownPickUpAddress: z.boolean().optional(),
  isUnknownWasteProducer: z.boolean().optional(),
  additionalFiles: z.array(zCreateFileLinkCommand).optional(),
  analysisReportFiles: z.array(zCreateFileLinkCommand).optional(),
  averageStableTemperature: z.union([
    z.number().gte(-99_999).lte(99_999),
    z.null(),
  ]).optional(),
  collectionRemarks: z.union([
    z.string().max(1333),
    z.null(),
  ]).optional(),
  collectionRequirements: z.union([
    zCollectionRequirementOption,
    z.null(),
  ]).optional(),
  composition: z.array(zWasteComposition).optional(),
  containerLoadingType: z.union([
    zContainerLoadingType,
    z.null(),
  ]).optional(),
  customerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  dischargeFrequency: z.union([
    zWasteDischargeFrequency,
    z.null(),
  ]).optional(),
  ewcLevel1Name: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  ewcLevel2Name: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  ewcLevel3Name: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  expectedPerCollectionQuantity: z.union([
    z.number().gte(0.01).lte(99_999_999_999.99),
    z.null(),
  ]).optional(),
  expectedPerCollectionUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]).optional(),
  expectedYearlyVolumeAmount: z.union([
    z.number().gte(0.01).lte(99_999_999_999.99),
    z.null(),
  ]).optional(),
  expectedYearlyVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]).optional(),
  flashpoint: z.union([
    zWasteFlashpointOption,
    z.null(),
  ]).optional(),
  hazardInducer1: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  hazardInducer2: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  hazardInducer3: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  legislationRemarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  loadingMethod: z.union([
    zWasteLoadingMethod,
    z.null(),
  ]).optional(),
  loadingType: z.union([
    zWasteLoadingType,
    z.null(),
  ]).optional(),
  maxStableTemperature: z.union([
    z.number().gte(-99_999).lte(99_999),
    z.null(),
  ]).optional(),
  minStableTemperature: z.union([
    z.number().gte(-99_999).lte(99_999),
    z.null(),
  ]).optional(),
  noAnalysisReport: z.boolean().optional(),
  noSds: z.boolean().optional(),
  packaging: z.array(zWastePackaging).optional(),
  packagingType: z.union([
    zWastePackagingType,
    z.null(),
  ]).optional(),
  ph: z.union([
    zWastePhOption,
    z.null(),
  ]).optional(),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  propertyRemarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  remarks: z.union([
    z.string().max(1333),
    z.null(),
  ]).optional(),
  sdsFiles: z.array(zCreateFileLinkCommand).optional(),
  selectedLegislationOptions: z.array(zWasteLegislationOption).optional(),
  selectedPropertyOptions: z.array(zWastePropertyOption).optional(),
  sendCopyToContacts: z.array(zContact).optional(),
  specificGravity: z.union([
    z.number().gte(-9_999_999.9).lte(9_999_999.9),
    z.null(),
  ]).optional(),
  stableTemperatureType: z.union([
    zStableTemperatureType,
    z.null(),
  ]).optional(),
  stateOfMatter: z.union([
    zStateOfMatter,
    z.null(),
  ]).optional(),
  storedIn: z.union([
    zWasteStoredInOption,
    z.null(),
  ]).optional(),
  svhcExtra: z.union([
    zSvhcExtraOption,
    z.null(),
  ]).optional(),
  transportIn: z.union([
    zWasteTransportInOption,
    z.null(),
  ]).optional(),
  transportType: z.union([
    zWasteTransportType,
    z.null(),
  ]).optional(),
  transportVolumeAmount: z.union([
    z.number().gte(0.01).lte(99_999_999_999.99),
    z.null(),
  ]).optional(),
  transportVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]).optional(),
  unNumbers: z.array(zUnNumber).optional(),
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteStreamDescription: z.union([
    z.string().max(1333),
    z.null(),
  ]).optional(),
  wasteStreamName: z.union([
    z.string().max(40),
    z.null(),
  ]).optional(),
})

export const zUpdateWasteInquiryResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zWasteInquiryAlreadySubmitted = z.object({
  code: z.enum([
    'waste_inquiry_already_submitted',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '409',
  ]),
})

export const zSubmitWasteInquiryResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  submittedOn: z.string().datetime(),
  updatedAt: z.string().datetime(),
  inquiryNumber: z.string(),
})

export const zViewWasteInquiryIndexSortQueryKey = z.enum([
  'contractId',
  'contractItem',
  'date',
  'customerName',
  'pickUpAddressId',
  'inquiryNumber',
  'salesOrganisationId',
  'wasteStreamName',
  'wasteProducerId',
  'wasteProducerName',
  'conformityCheck',
])

export const zViewWasteInquiryIndexSortQuery = z.object({
  key: zViewWasteInquiryIndexSortQueryKey,
  order: zSortDirection,
})

export const zViewWasteInquiryIndexFilterQuery = z.object({
  contractId: z.string().optional(),
  customerId: z.string().optional(),
  pickUpAddressId: z.string().optional(),
  salesOrganisationId: z.string().optional(),
  wasteProducerId: z.string().optional(),
  conformityCheck: z.boolean().optional(),
  contractItem: z.string().optional(),
  date: zDateRange.optional(),
  ewcCode: z.string().optional(),
  inquiryNumber: z.string().optional(),
  requestorName: z.string().optional(),
  statuses: z.array(zWasteInquiryStatus),
  wasteStreamName: z.string().optional(),
})

export const zWasteInquiryResponse = z.object({
  uuid: z.union([
    z.string(),
    z.null(),
  ]),
  contractId: z.union([
    z.string(),
    z.null(),
  ]),
  customerId: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressId: z.union([
    z.string(),
    z.null(),
  ]),
  salesOrganisationId: z.union([
    z.string(),
    z.null(),
  ]),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]),
  conformityCheck: z.boolean(),
  contractItem: z.union([
    z.string(),
    z.null(),
  ]),
  customerName: z.union([
    z.string(),
    z.null(),
  ]),
  date: z.union([
    z.string().date(),
    z.null(),
  ]),
  ewcLevel1: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel2: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel3: z.union([
    z.string(),
    z.null(),
  ]),
  inquiryNumber: z.union([
    z.string(),
    z.null(),
  ]),
  pickUpAddressName: z.union([
    z.string(),
    z.null(),
  ]),
  requestorName: z.union([
    z.string(),
    z.null(),
  ]),
  salesOrganisationName: z.union([
    z.string(),
    z.null(),
  ]),
  status: zWasteInquiryStatus,
  wasteProducerName: z.union([
    z.string(),
    z.null(),
  ]),
  wasteStreamName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zViewWasteInquiryIndexResponse = z.object({
  items: z.array(zWasteInquiryResponse),
  meta: zPaginatedOffsetResponseMeta,
})

export const zViewWasteInquirySapResponse = z.object({
  expectedEndDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  firstCollectionDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  submittedOn: z.union([
    z.string().datetime(),
    z.null(),
  ]),
  isLoadingByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isRegulatedTransport: z.union([
    zRegulatedTransportOption,
    z.null(),
  ]),
  isSampleAvailable: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTankOwnedByCustomer: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isUnknownPickUpAddress: z.boolean(),
  isUnknownWasteProducer: z.boolean(),
  additionalFiles: z.array(zFileLinkResponse),
  analysisReportFiles: z.array(zFileLinkResponse),
  averageStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  collectionRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  collectionRequirements: z.union([
    zCollectionRequirementOption,
    z.null(),
  ]),
  composition: z.array(zWasteCompositionResponse),
  containerLoadingType: z.union([
    zContainerLoadingType,
    z.null(),
  ]),
  contractItem: z.union([
    z.string(),
    z.null(),
  ]),
  contractNumber: z.union([
    z.string(),
    z.null(),
  ]),
  createdBy: z.union([
    z.string(),
    z.null(),
  ]),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  dischargeFrequency: z.union([
    zWasteDischargeFrequency,
    z.null(),
  ]),
  ewcLevel1Name: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel2Name: z.union([
    z.string(),
    z.null(),
  ]),
  ewcLevel3Name: z.union([
    z.string(),
    z.null(),
  ]),
  expectedPerCollectionQuantity: z.union([
    z.number(),
    z.null(),
  ]),
  expectedPerCollectionUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  expectedYearlyVolumeAmount: z.union([
    z.number(),
    z.null(),
  ]),
  expectedYearlyVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  flashpoint: z.union([
    zWasteFlashpointOption,
    z.null(),
  ]),
  hazardInducer1: z.union([
    z.string(),
    z.null(),
  ]),
  hazardInducer2: z.union([
    z.string(),
    z.null(),
  ]),
  hazardInducer3: z.union([
    z.string(),
    z.null(),
  ]),
  inquiryNumber: z.union([
    z.string(),
    z.null(),
  ]),
  legislationRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  loadingMethod: z.union([
    zWasteLoadingMethod,
    z.null(),
  ]),
  loadingType: z.union([
    zWasteLoadingType,
    z.null(),
  ]),
  maxStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  minStableTemperature: z.union([
    z.number(),
    z.null(),
  ]),
  noAnalysisReport: z.boolean(),
  noSds: z.boolean(),
  packaging: z.array(zWastePackagingResponse),
  packagingType: z.union([
    zWastePackagingType,
    z.null(),
  ]),
  ph: z.union([
    zWastePhOption,
    z.null(),
  ]),
  pickUpAddress: z.union([
    zPickUpAddressResponse,
    z.null(),
  ]),
  propertyRemarks: z.union([
    z.string(),
    z.null(),
  ]),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sdsFiles: z.array(zFileLinkResponse),
  selectedLegislationOptions: z.array(zWasteLegislationOption),
  selectedPropertyOptions: z.array(zWastePropertyOption),
  sendCopyToContacts: z.array(zContactTypeResponse),
  specificGravity: z.union([
    z.number(),
    z.null(),
  ]),
  stableTemperatureType: z.union([
    zStableTemperatureType,
    z.null(),
  ]),
  stateOfMatter: z.union([
    zStateOfMatter,
    z.null(),
  ]),
  status: zWasteInquiryStatus,
  storedIn: z.union([
    zWasteStoredInOption,
    z.null(),
  ]),
  svhcExtra: z.union([
    zSvhcExtraOption,
    z.null(),
  ]),
  transportIn: z.union([
    zWasteTransportInOption,
    z.null(),
  ]),
  transportType: z.union([
    zWasteTransportType,
    z.null(),
  ]),
  transportVolumeAmount: z.union([
    z.number(),
    z.null(),
  ]),
  transportVolumeUnit: z.union([
    zWasteMeasurementUnit,
    z.null(),
  ]),
  unNumbers: z.array(zWasteInquiryUnNumberResponse),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
  wasteStreamDescription: z.union([
    z.string(),
    z.null(),
  ]),
  wasteStreamName: z.union([
    z.string(),
    z.null(),
  ]),
})

export const zEntityPart = z.enum([
  'sds',
  'analysis-report',
  'additional',
  'news-image',
])

export const zUploadDocumentWasteInquiryCommand = z.object({
  fileUuid: z.string().uuid(),
  entityPart: zEntityPart,
})

export const zUploadDocumentSubmittedWasteInquiryCommand = z.object({ documents: z.array(zUploadDocumentWasteInquiryCommand) })

export const zBulkDeleteWasteInquiryCommand = z.object({ wasteInquiryUuids: z.array(z.string()) })

export const zCopyWasteInquirySapResponse = z.object({ uuid: z.string().uuid() })

export const zCreateWeeklyPlanningRequestCommand = z.record(z.unknown())

export const zCreateWeeklyPlanningRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zCustomerNotProvidedError = z.object({
  code: z.enum([
    'customer_not_provided',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zUpdateWeeklyPlanningRequestCommand = z.object({
  customerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  wasteProducerId: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  additionalFiles: z.array(zCreateFileLinkCommand).optional(),
  pickUpAddressIds: z.array(z.string()).optional(),
  remarks: z.union([
    z.string(),
    z.null(),
  ]).optional(),
  sendCopyToContacts: z.array(zContact).optional(),
})

export const zUpdateWeeklyPlanningRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
})

export const zAddWprPickUpRequestCommand = z.object({ pickUpRequestUuid: z.string().uuid() })

export const zAddWprPickUpRequestResponse = z.object({ uuid: z.string().uuid() })

export const zViewWprPickUpRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  endDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startDate: z.union([
    z.string().date(),
    z.null(),
  ]),
  startTime: z.union([
    z.string().time(),
    z.null(),
  ]),
  updatedAt: z.string().datetime(),
  isReturnPackaging: z.union([
    z.boolean(),
    z.null(),
  ]),
  isTransportByIndaver: z.union([
    z.boolean(),
    z.null(),
  ]),
  isWicConfirmed: z.union([
    z.boolean(),
    z.null(),
  ]),
  additionalFiles: z.array(zFileLinkResponse),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  materials: z.array(zMaterialResponse),
  needsWicConfirmation: z.boolean(),
  packagingRemark: z.union([
    z.string(),
    z.null(),
  ]),
  packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
  pickUpAddresses: z.array(zPickUpAddressResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  status: zPickUpRequestStatus,
  totalQuantityPallets: z.union([
    z.number(),
    z.null(),
  ]),
  transportMode: z.union([
    zPickUpTransportMode,
    z.null(),
  ]),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zViewWeeklyPlanningRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  additionalFiles: z.array(zFileLinkResponse),
  customer: z.union([
    zCustomerResponse,
    z.null(),
  ]),
  pickUpAddresses: z.array(zPickUpAddressResponse),
  pickUpRequests: z.array(zViewWprPickUpRequestResponse),
  remarks: z.union([
    z.string(),
    z.null(),
  ]),
  sendCopyToContacts: z.array(zContactTypeResponse),
  wasteProducer: z.union([
    zWasteProducerResponse,
    z.null(),
  ]),
})

export const zWeeklyPlanningRequestAlreadySubmittedError = z.object({
  code: z.enum([
    'weekly_planning_request_already_submitted_error',
  ]),
  detail: z.string().optional(),
  source: zErrorSource.optional(),
  status: z.enum([
    '400',
  ]),
})

export const zSubmitWeeklyPlanningRequestResponse = z.object({
  uuid: z.string().uuid(),
  createdAt: z.string().datetime(),
  submittedOn: z.string().datetime(),
  updatedAt: z.string().datetime(),
  inquiryNumber: z.string(),
})

export const zInternalServerApiError = z.object({
  code: z.enum([
    'internal_server_error',
  ]),
  detail: z.string().optional(),
  status: z.enum([
    '500',
  ]),
})

export const zMigrateCollectionsV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    collections: z.array(z.enum([
      'contact',
      'pick-up-request-template',
      'user',
    ])).optional(),
    fresh: z.boolean().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zImportCollectionsV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    collections: z.array(z.enum([
      'contact',
      'pick-up-request-template',
      'user',
    ])).optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewCollectionsV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewMeV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

/**
 * User details retrieved
 */
export const zViewMeV1Response = zViewMeResponse

export const zViewUserDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * User details retrieved
 */
export const zViewUserDetailV1Response = zViewUserDetailResponse

export const zViewUserIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

/**
 * Users retrieved
 */
export const zViewUserIndexV1Response = zViewUserIndexResponse

export const zSyncEntraUsersV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zStartUserImpersonationV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * User impersonation started
 */
export const zStartUserImpersonationV1Response = zStartUserImpersonationResponse

export const zViewPermissionIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewPermissionIndexV1Response = zViewPermissionIndexResponse

export const zViewCustomerIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewCustomerIndexV1Response = zViewCustomerIndexResponse

export const zCreateFileV1Data = z.object({
  body: zCreateFileCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateFileV1Response = zCreateFileResponse

export const zConfirmFileUploadV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ file: z.string() }),
})

export const zDownloadFileV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ file: z.string() }),
})

export const zProxyExternalFileV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ url: z.string() }),
  path: z.never().optional(),
})

export const zViewSuggestedCustomersV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zViewSuggestedCustomersFilterQuery }),
  path: z.never().optional(),
})

export const zViewSuggestedCustomersV1Response = zViewSuggestedCustomersResponse

export const zViewCustomerCountryV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ id: z.string() }),
})

export const zViewCustomerCountryV1Response = zViewCustomerCountryResponse

export const zViewDomainEventLogIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewDomainEventLogIndexFilterQuery.optional(),
    pagination: zViewDomainEventLogIndexPaginationQuery.optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewDomainEventLogIndexV1Response = zViewDomainEventLogIndexResponse

export const zSearchCollectionsV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zSearchCollectionsFilterQuery,
    search: z.string(),
  }),
  path: z.never().optional(),
})

export const zSearchCollectionsV1Response = zSearchCollectionsResponse

export const zViewJobsIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewJobsIndexFilterQuery.optional(),
    pagination: zViewJobsIndexPaginationQuery.optional(),
    sort: z.array(zViewJobsIndexSortQuery).optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewJobsIndexV1Response = zViewJobsIndexResponse

export const zViewJobDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ isArchived: z.boolean() }),
  path: z.object({ jobId: z.string() }),
})

export const zViewJobDetailV1Response = zViewJobDetailResponse

export const zGetMyNotificationPreferencesV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zGetMyNotificationPreferencesV1Response = zGetMyNotificationPreferencesResponse

export const zGetNotificationTypesConfigV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zGetNotificationTypesConfigV1Response = zGetNotificationTypesConfigResponse

export const zUpdateMyChannelNotificationPreferenceV1Data = z.object({
  body: zUpdateMyChannelNotificationPreferenceCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zUpdateMyChannelNotificationPreferenceV1Response = z.void()

export const zSendTestNotificationV1Data = z.object({
  body: zSendTestNotificationCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zSendTestNotificationV1Response = z.void()

export const zGetMyNotificationsV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zGetMyNotificationsFilterQuery.optional(),
    pagination: zGetMyNotificationsPaginationQuery.optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zGetMyNotificationsV1Response = zGetMyNotificationsResponse

export const zViewUnreadNotificationsCountV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewUnreadNotificationsCountV1Response = zViewUnreadNotificationsCountResponse

export const zViewUserNotificationDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ notificationUuid: z.string() }),
})

export const zViewUserNotificationDetailV1Response = zTestNotificationNotification

export const zMarkAllNotificationAsReadV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zMarkAllNotificationAsReadV1Response = z.void()

export const zUpdateMyNotificationTypePreferenceV1Data = z.object({
  body: zUpdateMyNotificationTypePreferenceCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zUpdateMyNotificationTypePreferenceV1Response = z.void()

export const zMarkNotificationAsReadV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ notificationUuid: z.string() }),
})

export const zMarkNotificationAsReadV1Response = z.void()

export const zMarkNotificationAsUnreadV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ notificationUuid: z.string() }),
})

export const zMarkNotificationAsUnreadV1Response = z.void()

export const zUpdateMyNotificationPreferencePresetV1Data = z.object({
  body: zUpdateMyNotificationPreferencePresetCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zUpdateMyNotificationPreferencePresetV1Response = z.void()

export const zMigrateNotificationTypesV1Data = z.object({
  body: zMigrateNotificationTypesCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zMigrateNotificationTypesV1Response = z.void()

export const zViewRoleIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

/**
 * The roles has been successfully received.
 */
export const zViewRoleIndexV1Response = zViewRoleIndexResponse

export const zUpdateRolesPermissionsV1Data = z.object({
  body: zUpdateRolesPermissionsCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zUpdateRolesPermissionsV1Response = z.void()

export const zCreateRoleV1Data = z.object({
  body: zCreateRoleCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateRoleV1Response = zCreateRoleResponse

export const zClearRolePermissionsCacheV1Data = z.object({
  body: zClearRolePermissionsCacheCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zClearRolePermissionsCacheV1Response = z.void()

export const zDeleteRoleV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ role: z.string() }),
})

export const zDeleteRoleV1Response = z.void()

export const zViewRoleDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ role: z.string() }),
})

/**
 * The role has been successfully received.
 */
export const zViewRoleDetailV1Response = zViewRoleDetailResponse

export const zUpdateRoleV1Data = z.object({
  body: zUpdateRoleCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zGetApiInfoData = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

/**
 * API info retrieved
 */
export const zGetApiInfoResponse2 = zGetApiInfoResponse

export const zSwaggerData = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewUiPreferencesV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewUiPreferencesV1Response = zViewUiPreferencesResponse

export const zUpdateUiPreferencesV1Data = z.object({
  body: zUpdateUiPreferencesCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewAnnouncementIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ pagination: zPaginatedOffsetQuery.optional() }).optional(),
  path: z.never().optional(),
})

export const zViewAnnouncementIndexV1Response = zViewAnnouncementIndexResponse

export const zCreateAnnouncementV1Data = z.object({
  body: zCreateAnnouncementCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateAnnouncementV1Response = zCreateAnnouncementResponse

export const zDeleteAnnouncementV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewAnnouncementV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewAnnouncementV1Response = zViewAnnouncementResponse

export const zUpdateAnnouncementV1Data = z.object({
  body: zUpdateAnnouncementCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdateAnnouncementV1Response = zUpdateAnnouncementResponse

export const zViewDashboardAnnouncementIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ pagination: zPaginatedOffsetQuery.optional() }).optional(),
  path: z.never().optional(),
})

export const zViewDashboardAnnouncementIndexV1Response = zViewDashboardAnnouncementIndexResponse

export const zViewDashboardAnnouncementV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewDashboardAnnouncementV1Response = zViewDashboardAnnouncementResponse

export const zDownloadCertificateV1Data = z.object({
  body: zDownloadCertificateCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewCertificateIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewCertificateIndexFilterQuery.optional(),
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewCertificateIndexSortQuery),
  }),
  path: z.never().optional(),
})

export const zViewCertificateIndexV1Response = zViewCertificateIndexResponse

export const zViewContactIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewContactIndexV1Response = zViewContactIndexResponse

export const zCreateContactV1Data = z.object({
  body: zCreateContactCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateContactV1Response = zCreateContactResponse

export const zDeleteContactV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zDeleteContactV1Response = z.void()

export const zUpdateContactV1Data = z.object({
  body: zUpdateContactCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdateContactV1Response = z.void()

export const zViewContainerTypeIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewContainerTypeIndexFilterQuery,
    search: z.string().optional(),
  }),
  path: z.never().optional(),
})

export const zViewContainerTypeIndexV1Response = zViewContainerTypeIndexResponse

export const zViewContractLineIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewContractLineIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
    sort: z.array(zViewContractLineIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewContractLineIndexV1Response = zViewContractLineIndexResponse

export const zViewWprContractLineIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewWprContractLineIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
  }),
  path: z.never().optional(),
})

export const zViewWprContractLineIndexV1Response = zViewWprContractLineIndexResponse

export const zViewPackagingRequestContractLineIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewPackagingRequestContractLineIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
  }),
  path: z.never().optional(),
})

export const zViewPackagingRequestContractLineIndexV1Response = zViewPackagingRequestContractLineIndexResponse

export const zGenerateContractLinesPdfV1Data = z.object({
  body: zGenerateContractLinesPdfCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zGenerateContractLinesPdfV1Response = zGenerateContractLinesPdfResponse

export const zDownloadDocumentV1Data = z.object({
  body: zDownloadDocumentCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zGetDocumentFiltersV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zGetDocumentFiltersFilterQuery }),
  path: z.never().optional(),
})

export const zGetDocumentFiltersV1Response = zGetDocumentFiltersResponse

export const zViewDocumentIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewDocumentIndexFilterQuery,
    pagination: zViewDocumentIndexPaginationQuery.optional(),
    search: z.string().optional(),
  }),
  path: z.never().optional(),
})

export const zViewDocumentIndexV1Response = zViewDocumentIndexResponse

export const zViewUserSiteIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewUserSiteIndexV1Response = z.array(zViewUserSiteIndexResponse)

export const zViewDynamicTableColumnIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ name: z.string() }),
})

export const zViewDynamicTableColumnIndexV1Response = zDynamicTableIndexColumnResponse

export const zViewDynamicTableViewIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ pagination: zPaginatedOffsetQuery.optional() }).optional(),
  path: z.object({ name: z.string() }),
})

export const zViewDynamicTableViewIndexV1Response = zDynamicTableViewIndexResponse

export const zCreateDynamicTableViewV1Data = z.object({
  body: zCreateDynamicTableViewCommand,
  query: z.never().optional(),
  path: z.object({ name: z.string() }),
})

export const zCreateDynamicTableViewV1Response = zCreateDynamicTableViewResponse

export const zViewDefaultDynamicTableViewV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ name: z.string() }),
})

export const zViewDefaultDynamicTableViewV1Response = zViewDefaultDynamicTableViewResponse

export const zDeleteDynamicTableViewV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({
    uuid: z.string(),
    name: z.string(),
  }),
})

export const zUpdateDynamicTableViewV1Data = z.object({
  body: zUpdateDynamicTableViewCommand,
  query: z.never().optional(),
  path: z.object({
    uuid: z.string(),
    name: z.string(),
  }),
})

export const zUpdateDynamicTableViewV1Response = zUpdateDynamicTableViewResponse

export const zViewEwcCodeIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ search: z.string().optional() }).optional(),
  path: z.never().optional(),
})

export const zViewEwcCodeIndexV1Response = zViewEwcCodeIndexResponse

export const zDownloadGuidanceLetterSapV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ type: zDownloadGuidanceLetterType }),
  path: z.object({ shipmentId: z.string() }),
})

export const zViewGuidanceLetterIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewGuidanceLetterIndexFilterQuery.optional(),
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewGuidanceLetterIndexSortQuery).optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewGuidanceLetterIndexV1Response = zViewGuidanceLetterIndexResponse

export const zDownloadInvoiceCertificateV1Data = z.object({
  body: zDownloadInvoiceCertificateCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zDownloadInvoiceV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ invoiceNumber: z.string() }),
})

export const zExportInvoicesExcelV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zExportInvoicesExcelFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
    sort: z.array(zViewInvoiceIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewInvoiceIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewInvoiceIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
    sort: z.array(zViewInvoiceIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewInvoiceIndexV1Response = zViewInvoiceIndexResponse

export const zViewInvoiceDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zViewInvoiceDetailV1Response = zViewInvoiceResponse

export const zViewDraftInvoiceDetailV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ invoiceNumber: z.string() }),
})

export const zViewDraftInvoiceDetailV1Response = zViewDraftInvoiceDetailResponse

export const zViewDraftInvoiceIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewDraftInvoiceIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewDraftInvoiceIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewDraftInvoiceIndexV1Response = zViewDraftInvoiceIndexResponse

export const zExportDraftInvoicesExcelV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zExportDraftInvoicesExcelFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewDraftInvoiceIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zApproveDraftInvoiceV1Data = z.object({
  body: zApproveDraftInvoiceCommand,
  query: z.never().optional(),
  path: z.object({ invoiceNumber: z.string() }),
})

export const zRejectDraftInvoiceV1Data = z.object({
  body: zRejectDraftInvoiceCommand,
  query: z.never().optional(),
  path: z.object({ invoiceNumber: z.string() }),
})

export const zSubscribeToNewsletterV1Data = z.object({
  body: zSubscribeToNewsletterCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zSubscribeToNewsletterV1Response = z.void()

export const zViewNewsItemIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ pagination: zPaginatedOffsetQuery.optional() }).optional(),
  path: z.never().optional(),
})

export const zViewNewsItemIndexV1Response = zViewNewsIndexResponse

export const zCreateNewsItemV1Data = z.object({
  body: zCreateNewsItemCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateNewsItemV1Response = zCreateNewsItemResponse

export const zDeleteNewsItemV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewNewsItemV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewNewsItemV1Response = zViewNewsItemResponse

export const zUpdateNewsItemV1Data = z.object({
  body: zUpdateNewsItemCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdateNewsItemV1Response = zUpdateNewsItemResponse

export const zViewDashboardNewsItemIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewDashboardNewItemFilterQuery.optional(),
    pagination: zPaginatedOffsetQuery.optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewDashboardNewsItemIndexV1Response = zViewDashboardNewsIndexResponse

export const zViewDashboardNewsItemV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewDashboardNewsItemV1Response = zViewDashboardNewsItemResponse

export const zCreatePackagingRequestV1Data = z.object({
  body: zCreatePackagingRequestCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreatePackagingRequestV1Response = zCreatePackagingRequestResponse

export const zViewPackagingRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewPackagingRequestV1Response = zViewPackagingRequestResponse

export const zUpdatePackagingRequestV1Data = z.object({
  body: zUpdatePackagingRequestCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdatePackagingRequestV1Response = zUpdatePackagingRequestResponse

export const zViewWasteProducerIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewWasteProducerIndexFilterQuery.optional(),
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewWasteProducerIndexV1Response = zViewWasteProducerIndexResponse

export const zViewSuggestedWasteProducersV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zViewSuggestedWasteProducersFilterQuery.optional() }).optional(),
  path: z.never().optional(),
})

export const zViewSuggestedWasteProducersV1Response = zViewSuggestedWasteProducersResponse

export const zViewPickUpAddressIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewPickUpAddressIndexFilterQuery.optional(),
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewPickUpAddressIndexV1Response = zViewPickUpAddressIndexResponse

export const zViewSuggestedPickUpAddressesV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zViewSuggestedPickUpAddressesFilterQuery.optional() }).optional(),
  path: z.never().optional(),
})

export const zViewSuggestedPickUpAddressesV1Response = zViewSuggestedPickUpAddressesResponse

export const zSubmitPackagingRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * Packaging request submitted
 */
export const zSubmitPackagingRequestV1Response = zSubmitPackagingRequestResponse

export const zBulkDeletePackagingRequestV1Data = z.object({
  body: zBulkDeletePackagingRequestCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCopyPackagingRequestSapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zCopyPackagingRequestSapV1Response = zCopyPackagingRequestSapResponse

export const zViewPackagingTypeIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewPackagingTypeIndexFilterQuery,
    search: z.string().optional(),
  }),
  path: z.never().optional(),
})

export const zViewPackagingTypeIndexV1Response = zViewPackagingTypeIndexResponse

export const zViewPayerIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zViewPayerIndexFilterQuery }),
  path: z.never().optional(),
})

export const zViewPayerIndexV1Response = zViewPayerIndexResponse

export const zGetIsPoNumberAndCostCenterRequiredV1Data = z.object({
  body: z.never().optional(),
  query: z.object({ filter: zGetIsPoNumberAndCostCenterRequiredFilterQuery }),
  path: z.never().optional(),
})

/**
 * Returns whether PO number and cost center are required
 */
export const zGetIsPoNumberAndCostCenterRequiredV1Response = zGetIsPoNumberAndCostCenterRequiredResponse

export const zViewPickUpRequestIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewPickUpRequestIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewPickUpRequestIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewPickUpRequestIndexV1Response = zViewPickUpRequestIndexResponse

export const zCreatePickUpRequestV1Data = z.object({
  body: zCreatePickUpRequestCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreatePickUpRequestV1Response = zCreatePickUpRequestResponse

export const zViewPickUpRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewPickUpRequestV1Response = zViewPickUpRequestResponse

export const zUpdatePickUpRequestV1Data = z.object({
  body: zUpdatePickUpRequestCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdatePickUpRequestV1Response = zUpdatePickUpRequestResponse

export const zSubmitPickUpRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * Pick up request submitted
 */
export const zSubmitPickUpRequestV1Response = zSubmitPickUpRequestResponse

export const zBulkDeletePickUpRequestV1Data = z.object({
  body: zBulkDeletePickUpRequestCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewPickUpRequestSapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zViewPickUpRequestSapV1Response = zViewPickUpRequestSapResponse

export const zUpdatePickUpRequestSapV1Data = z.object({
  body: zUpdatePickUpRequestSapCommand,
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zViewPickUpRequestTemplateIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
    sort: z.array(zViewPickUpRequestTemplateIndexSortQuery).optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewPickUpRequestTemplateIndexV1Response = zViewPickUpRequestTemplateIndexResponse

export const zCreatePickUpRequestTemplateV1Data = z.object({
  body: zCreatePickUpRequestTemplateCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreatePickUpRequestTemplateV1Response = zCreatePickUpRequestTemplateResponse

export const zViewPickUpRequestTemplateV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewPickUpRequestTemplateV1Response = zViewPickUpRequestTemplateResponse

export const zUpdatePickUpRequestTemplateV1Data = z.object({
  body: zUpdatePickUpRequestTemplateCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdatePickUpRequestTemplateV1Response = zUpdatePickUpRequestTemplateResponse

export const zBulkDeletePickUpRequestTemplatesV1Data = z.object({
  body: zBulkDeletePickUpRequestTemplatesCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreatePickUpRequestFromTemplateV1Data = z.object({
  body: zCreatePickUpRequestFromTemplateCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zCreatePickUpRequestFromTemplateV1Response = zCreatePickUpRequestFromTemplateResponse

export const zUploadDocumentSubmittedPickUpRequestV1Data = z.object({
  body: zUploadDocumentSubmittedPickUpRequestCommand,
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zDownloadDocumentSubmittedPickUpRequestV1Data = z.object({
  body: zDownloadDocumentSubmittedPickUpRequestCommand,
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zCopyPickUpRequestSapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zCopyPickUpRequestSapV1Response = zCopyPickUpRequestSapResponse

export const zSubmitPickUpRequestSapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ requestNumber: z.string() }),
})

export const zViewSalesOrganisationIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    pagination: zPaginatedOffsetQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewSalesOrganisationIndexV1Response = zViewSalesOrganisationIndexResponse

export const zViewTankerTypeIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewTankerTypeIndexV1Response = zViewTankerTypeIndexResponse

export const zViewTransportTypeIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zViewTransportTypeIndexV1Response = zViewTransportTypeIndexResponse

export const zViewUnNumberIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewUnNumberFilterQuery.optional(),
    pagination: zViewUnNumberIndexPaginationQuery.optional(),
    search: z.string().optional(),
  }).optional(),
  path: z.never().optional(),
})

export const zViewUnNumberIndexV1Response = zViewUnNumberIndexResponse

export const zViewUnNumberIndexForPickUpRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewUnNumberIndexForPickUpRequestFilterQuery,
    search: z.string().optional(),
  }),
  path: z.never().optional(),
})

export const zViewUnNumberIndexForPickUpRequestV1Response = zViewUnNumberIndexForPickUpRequestResponse

export const zViewWasteInquiryIndexV1Data = z.object({
  body: z.never().optional(),
  query: z.object({
    filter: zViewWasteInquiryIndexFilterQuery,
    pagination: zPaginatedOffsetQuery.optional(),
    sort: z.array(zViewWasteInquiryIndexSortQuery).optional(),
  }),
  path: z.never().optional(),
})

export const zViewWasteInquiryIndexV1Response = zViewWasteInquiryIndexResponse

export const zCreateWasteInquiryV1Data = z.object({
  body: zCreateWasteInquiryCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateWasteInquiryV1Response = zCreateWasteInquiryResponse

export const zDownloadWasteInquirySummarySapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ inquiryNumber: z.string() }),
})

export const zDownloadWasteInquiryDocumentSapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({
    sapFileUuid: z.string(),
    inquiryNumber: z.string(),
  }),
})

export const zViewWasteInquiryV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewWasteInquiryV1Response = zViewWasteInquiryResponse

export const zUpdateWasteInquiryV1Data = z.object({
  body: zUpdateWasteInquiryCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdateWasteInquiryV1Response = zUpdateWasteInquiryResponse

export const zSubmitWasteInquiryV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * Waste inquiry submitted
 */
export const zSubmitWasteInquiryV1Response = zSubmitWasteInquiryResponse

export const zViewWasteInquirySapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ inquiryNumber: z.string() }),
})

export const zViewWasteInquirySapV1Response = zViewWasteInquirySapResponse

export const zUploadDocumentSubmittedWasteInquiryV1Data = z.object({
  body: zUploadDocumentSubmittedWasteInquiryCommand,
  query: z.never().optional(),
  path: z.object({ inquiryNumber: z.string() }),
})

export const zBulkDeleteWasteInquiryV1Data = z.object({
  body: zBulkDeleteWasteInquiryCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCopyWasteInquirySapV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ inquiryNumber: z.string() }),
})

export const zCopyWasteInquirySapV1Response = zCopyWasteInquirySapResponse

export const zCreateWeeklyPlanningRequestV1Data = z.object({
  body: zCreateWeeklyPlanningRequestCommand,
  query: z.never().optional(),
  path: z.never().optional(),
})

export const zCreateWeeklyPlanningRequestV1Response = zCreateWeeklyPlanningRequestResponse

export const zViewWeeklyPlanningRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zViewWeeklyPlanningRequestV1Response = zViewWeeklyPlanningRequestResponse

export const zUpdateWeeklyPlanningRequestV1Data = z.object({
  body: zUpdateWeeklyPlanningRequestCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zUpdateWeeklyPlanningRequestV1Response = zUpdateWeeklyPlanningRequestResponse

export const zAddWprPickUpRequestV1Data = z.object({
  body: zAddWprPickUpRequestCommand,
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

export const zAddWprPickUpRequestV1Response = zAddWprPickUpRequestResponse

export const zSubmitWeeklyPlanningRequestV1Data = z.object({
  body: z.never().optional(),
  query: z.never().optional(),
  path: z.object({ uuid: z.string() }),
})

/**
 * Weekly planning request submitted
 */
export const zSubmitWeeklyPlanningRequestV1Response = zSubmitWeeklyPlanningRequestResponse
