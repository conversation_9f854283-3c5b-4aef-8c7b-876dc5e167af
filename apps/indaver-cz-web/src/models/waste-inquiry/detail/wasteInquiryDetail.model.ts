import { z } from 'zod'

import {
  CollectionRequirementOption,
  ContainerLoadingType,
  PackingGroup,
  RegulatedTransportOption,
  SvhcExtraOption,
  WasteDischargeFrequency,
  WasteLegislationOption,
  WasteLoadingMethod,
  WasteLoadingType,
  WasteMeasurementUnit,
  WastePackagingOption,
  WastePropertyOption,
  WasteStoredInOption,
  WasteTransportInOption,
  WasteTransportType,
} from '@/client'
import {
  StateOfMatter,
  WasteFlashpointOption,
  WastePackagingType,
  WastePhOption,
} from '@indaver/types'
import { zWasteInquiryStatus } from '@/client/zod.gen'
import { customerIndexSchema } from '@/models/customer/index/customerIndex.model'
import { calendarDateSchema } from '@/models/date/calendarDate.model'
import { StableTemperatureType } from '@/models/enums/stableTemperatureType.enum'
import { pickUpAddressIndexSchema } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import { s3FileSchema } from '@/models/s3-file/s3File.model'
import { wasteInquiryUuidSchema } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import { wasteProducerIndexSchema } from '@/models/waste-producer/index/wasteProducerIndex.model'

export const wasteInquiryDetailSchema = z.object({
  uuid: wasteInquiryUuidSchema.nullable(),
  createdAt: calendarDateSchema.nullable(),
  expectedEndDate: calendarDateSchema.nullable(),
  firstCollectionDate: calendarDateSchema.nullable(),
  submittedOn: calendarDateSchema.nullable(),
  updatedAt: calendarDateSchema.nullable(),
  hasNoAnalysisReport: z.boolean().nullable(),
  hasNoSds: z.boolean().nullable(),
  isLoadingByIndaver: z.boolean().nullable(),
  isSampleAvailable: z.boolean().nullable(),
  isTankOwnedByCustomer: z.boolean().nullable(),
  isTransportByIndaver: z.boolean().nullable(),
  isUnknownPickUpAddress: z.boolean(),
  isUnknownWasteProducer: z.boolean(),
  additionalFiles: s3FileSchema.array().nullable(),
  analysisReportFiles: s3FileSchema.array().nullable(),
  averageTemperature: z.number().nullable(),
  collectionRemarks: z.string().nullable(),
  collectionRequirements: z.nativeEnum(CollectionRequirementOption).nullable(),
  composition: z.object({
    name: z.string().nullable(),
    maxWeight: z.number().nullable(),
    minWeight: z.number().nullable(),
  }).array().nullable(),
  containerLoadingType: z.nativeEnum(ContainerLoadingType).nullable(),
  contractItem: z.string().nullable(),
  contractNumber: z.string().nullable(),
  createdBy: z.string().nullable(),
  customer: customerIndexSchema.nullable(),
  dischargeFrequency: z.nativeEnum(WasteDischargeFrequency).nullable(),
  ewcLevel1: z.string().nullable(),
  ewcLevel2: z.string().nullable(),
  ewcLevel3: z.string().nullable(),
  expectedPerCollectionQuantity: z.number().min(0).nullable(),
  expectedPerCollectionUnit: z.nativeEnum(WasteMeasurementUnit).nullable(),
  expectedYearlyVolumeAmount: z.number().min(0).nullable(),
  expectedYearlyVolumeUnit: z.nativeEnum(WasteMeasurementUnit).nullable(),
  flashpointType: z.nativeEnum(WasteFlashpointOption).nullable(),
  hazardInducer1: z.string().nullable(),
  hazardInducer2: z.string().nullable(),
  hazardInducer3: z.string().nullable(),
  inquiryNumber: z.string().nullable(),
  legislationRemarks: z.string().nullable(),
  loadingMethod: z.nativeEnum(WasteLoadingMethod).nullable(),
  maximumTemperature: z.number().nullable(),
  minimumTemperature: z.number().nullable(),
  packaging: z.object({
    hasInnerPackaging: z.boolean().nullable(),
    remarks: z.string().nullable(),
    size: z.string().nullable(),
    type: z.nativeEnum(WastePackagingOption).nullable(),
    weightPerPieceValue: z.number().nullable(),
  }).array(),
  packagingType: z.nativeEnum(WastePackagingType).nullable(),
  phType: z.nativeEnum(WastePhOption).nullable(),
  pickupAddress: pickUpAddressIndexSchema.nullable(),
  propertyRemarks: z.string().nullable(),
  regulatedTransport: z.nativeEnum(RegulatedTransportOption).nullable(),
  sdsFiles: s3FileSchema.array().nullable(),
  selectedLegislationTypes: z.nativeEnum(WasteLegislationOption).array().nullable(),
  selectedPropertyTypes: z.nativeEnum(WastePropertyOption).array().nullable(),
  specificGravity: z.number().nullable(),
  stableTemperatureType: z.nativeEnum(StableTemperatureType).nullable(),
  stateOfMatter: z.nativeEnum(StateOfMatter).nullable(),
  status: zWasteInquiryStatus.nullable(),
  svhcExtraType: z.nativeEnum(SvhcExtraOption).nullable(),
  transportIn: z.nativeEnum(WasteTransportInOption).nullable(),
  transportType: z.nativeEnum(WasteTransportType).nullable(),
  transportVolumeAmount: z.number().nullable(),
  transportVolumeUnit: z.nativeEnum(WasteMeasurementUnit).nullable(),
  unNumbers: z.object({
    isHazardous: z.boolean(),
    packingGroup: z.nativeEnum(PackingGroup).nullable(),
    unNumber: z.string().nullable(),
  }).array(),
  wasteLoadingType: z.nativeEnum(WasteLoadingType).nullable(),
  wasteProducer: wasteProducerIndexSchema.nullable(),
  wasteStoredIn: z.nativeEnum(WasteStoredInOption).nullable(),
  wasteStreamDescription: z.string().nullable(),
  wasteStreamName: z.string().nullable(),
})

export type WasteInquiryDetail = z.infer<typeof wasteInquiryDetailSchema>

export type WasteInquiryDetailCharacteristicsStep = Pick<WasteInquiryDetail,
  'averageTemperature'
  | 'flashpointType'
  | 'maximumTemperature'
  | 'minimumTemperature'
  | 'phType'
  | 'specificGravity'
  | 'stableTemperatureType'
>

export type WasteInquiryDetailCollectionStep = Pick<WasteInquiryDetail,
  'collectionRemarks'
  | 'dischargeFrequency'
  | 'expectedEndDate'
  | 'expectedPerCollectionQuantity'
  | 'expectedPerCollectionUnit'
  | 'expectedYearlyVolumeAmount'
  | 'expectedYearlyVolumeUnit'
  | 'firstCollectionDate'
>

export type WasteInquiryDetailCompositionStep = Pick<WasteInquiryDetail,
  'analysisReportFiles'
  | 'composition'
  | 'hasNoAnalysisReport'
  | 'hasNoSds'
  | 'isSampleAvailable'
  | 'sdsFiles'
>

export type WasteInquiryDetailCustomerAndLocationStep = Pick<WasteInquiryDetail,
  'customer'
  | 'isUnknownPickUpAddress'
  | 'isUnknownWasteProducer'
  | 'pickupAddress'
  | 'wasteProducer'
>

export type WasteInquiryDetailLegislationAndPropertiesStep = Pick<WasteInquiryDetail,
  'legislationRemarks'
  | 'propertyRemarks'
  | 'selectedLegislationTypes'
  | 'selectedPropertyTypes'
  | 'svhcExtraType'
>

export type WasteInquiryDetailTransportStep = Pick<WasteInquiryDetail,
  'collectionRequirements'
  | 'containerLoadingType'
  | 'hazardInducer1'
  | 'hazardInducer2'
  | 'hazardInducer3'
  | 'isLoadingByIndaver'
  | 'isTankOwnedByCustomer'
  | 'isTransportByIndaver'
  | 'loadingMethod'
  | 'packaging'
  | 'regulatedTransport'
  | 'transportIn'
  | 'transportType'
  | 'transportVolumeAmount'
  | 'transportVolumeUnit'
  | 'unNumbers'
  | 'wasteLoadingType'
  | 'wasteStoredIn'
>

export type WasteInquiryDetailTypeStep = Pick<WasteInquiryDetail,
  | 'ewcLevel1'
  | 'ewcLevel2'
  | 'ewcLevel3'
  | 'packagingType'
  | 'stateOfMatter'
  | 'wasteStreamDescription'
  | 'wasteStreamName'
>
