import { z } from 'zod'

import { WastePhOption } from '@indaver/types'
import { StableTemperatureType } from '@/models/enums/stableTemperatureType.enum'
import { WasteFlashpointOption } from '@/models/enums/wasteFlashpoint.enum'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

export const wasteInquiryCharacteristicsFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    averageTemperature: z.number().min(-99_999).max(99_999).nullable(),
    flashpointType: z.nativeEnum(WasteFlashpointOption).nullable(),
    maximumTemperature: z.number().min(-99_999).max(99_999).nullable(),
    minimumTemperature: z.number().min(-99_999).max(99_999).nullable(),
    phType: z.nativeEnum(WastePhOption).nullable(),
    specificGravity: z.number().nullable(),
    stableTemperatureType: z.nativeEnum(StableTemperatureType).nullable(),
  }),
).superRefine((data, ctx) => {
  if (data.stableTemperatureType === StableTemperatureType.MIN_MAX) {
    ZodUtil.validateNumber('minimumTemperature', data.minimumTemperature, ctx)

    ZodUtil.validateField(
      'maximumTemperature',
      z.number().min(data.minimumTemperature ?? 0),
      data.maximumTemperature,
      ctx,
    )
  }

  if (!data.isFinalized) {
    return
  }

  if (data.stableTemperatureType === StableTemperatureType.AVERAGE) {
    ZodUtil.validateNumber('averageTemperature', data.averageTemperature, ctx)
  }
})

export type WasteInquiryCharacteristicsForm = z.infer<typeof wasteInquiryCharacteristicsFormSchema>
