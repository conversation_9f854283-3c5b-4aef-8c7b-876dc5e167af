import { z } from 'zod'

import {
  StateOfMatter,
  WastePackagingType,
} from '@indaver/types'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

export const wasteInquiryTypeFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    name: z.string().max(40).nullable(),
    description: z.string().max(1333).nullable(),
    ewcLevel1: z.string().nullable(),
    ewcLevel2: z.string().nullable(),
    ewcLevel3: z.string().nullable(),
    packagingType: z.nativeEnum(WastePackagingType).nullable(),
    stateOfMatter: z.nativeEnum(StateOfMatter).nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  ZodUtil.validateString('name', data.name, ctx)
  ZodUtil.validateString('description', data.description, ctx)
  ZodUtil.validateString('stateOfMatter', data.stateOfMatter, ctx)

  ZodUtil.validateString('ewcLevel1', data.ewcLevel1, ctx)
  ZodUtil.validateString('ewcLevel2', data.ewcLevel2, ctx)
  ZodUtil.validateString('ewcLevel3', data.ewcLevel3, ctx)
})

export type WasteInquiryTypeForm = z.infer<typeof wasteInquiryTypeFormSchema>
