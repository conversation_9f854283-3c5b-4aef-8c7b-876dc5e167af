import type { ViewWasteInquiryIndexV1Data } from '@/client'
import { ViewWasteInquiryIndexSortQueryKey } from '@/client'
import { WasteInquiryStatus } from '@indaver/types'
import { CalendarDateRangeTransformer } from '@/models/date/calendarDate.transformer'
import type { WasteInquiryIndexQueryParams } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { CustomBooleanFilterUtil } from '@/utils/customBooleanFilter.util'
import { SortUtil } from '@/utils/sort.util'

export class WasteInquiryIndexQueryParamsTransformer {
  private static mapStatusFilterToDto(statuses: WasteInquiryStatus[]): WasteInquiryStatus[] {
    if (statuses.length === 0) {
      return [
        WasteInquiryStatus.NEW,
        WasteInquiryStatus.IN_PROGRESS,
        WasteInquiryStatus.CONFORMITY_CONFIRMED,
        WasteInquiryStatus.SOLUTION_DEFINED,
        WasteInquiryStatus.OFFER_SENT,
        WasteInquiryStatus.OFFER_APPROVED,
        WasteInquiryStatus.COMPLETED,
        WasteInquiryStatus.REJECTED,
      ]
    }

    return statuses
  }

  static toDto(options: OffsetPagination<WasteInquiryIndexQueryParams>): ViewWasteInquiryIndexV1Data['query'] {
    return {
      filter: {
        contractId: options.filters.contractId ?? undefined,
        customerId: options.filters.customerId?.id ?? undefined,
        pickUpAddressId: options.filters.pickupAddressId?.id ?? undefined,
        wasteProducerId: options.filters.wasteProducerId?.id ?? undefined,
        conformityCheck: CustomBooleanFilterUtil.toDto(options.filters.conformityCheck),
        contractItem: options.filters.contractItem ?? undefined,
        date: CalendarDateRangeTransformer.toDto(options.filters.date ?? null) || undefined,
        ewcCode: options.filters.ewcCode?.value ?? undefined,
        inquiryNumber: options.filters.inquiryNumber ?? undefined,
        requestorName: options.filters.requestorName ?? undefined,
        statuses: this.mapStatusFilterToDto(options.filters.statuses),
        wasteStreamName: options.filters.wasteStreamName ?? undefined,
      },
      pagination: options.pagination,
      sort: SortUtil.toDto(options.sort, {
        contractId: ViewWasteInquiryIndexSortQueryKey.CONTRACT_ID,
        pickUpAddressId: ViewWasteInquiryIndexSortQueryKey.PICK_UP_ADDRESS_ID,
        salesOrganisationId: ViewWasteInquiryIndexSortQueryKey.SALES_ORGANISATION_ID,
        wasteProducerId: ViewWasteInquiryIndexSortQueryKey.WASTE_PRODUCER_ID,
        conformityCheck: ViewWasteInquiryIndexSortQueryKey.CONFORMITY_CHECK,
        contractItem: ViewWasteInquiryIndexSortQueryKey.CONTRACT_ITEM,
        customerName: ViewWasteInquiryIndexSortQueryKey.CUSTOMER_NAME,
        date: ViewWasteInquiryIndexSortQueryKey.DATE,
        inquiryNumber: ViewWasteInquiryIndexSortQueryKey.INQUIRY_NUMBER,
        wasteProducerName: ViewWasteInquiryIndexSortQueryKey.WASTE_PRODUCER_NAME,
        wasteStreamName: ViewWasteInquiryIndexSortQueryKey.WASTE_STREAM_NAME,
      }),
    }
  }
}
