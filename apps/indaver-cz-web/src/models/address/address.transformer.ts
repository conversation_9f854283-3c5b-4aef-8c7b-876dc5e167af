import type { Address } from '@/models/address/address.model'
import type { AddressDto } from '@/models/address/addressDto.model'
import type { AddressForm } from '@/models/address/addressForm.model'

export class AddressTransformer {
  static fromDto(dto: AddressDto): Address {
    return {
      addressLine1: dto.addressLine1,
      addressLine2: dto.addressLine2,
      coordinates: dto.coordinates,
      countryCode: dto.countryCode,
      locality: dto.locality,
      postalCode: dto.postalCode,
    }
  }

  static fromNullableDto(dto: AddressDto | null): Address | null {
    if (dto === null) {
      return null
    }

    return this.fromDto(dto)
  }

  static toDto(_address: AddressForm): AddressDto {
    throw new Error('Method not implemented.')
  }
}
