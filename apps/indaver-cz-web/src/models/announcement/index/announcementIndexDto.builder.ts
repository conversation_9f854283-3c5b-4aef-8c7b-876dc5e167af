import type { Locale } from '@/client'
import { PublishStatus } from '@/client'
import { AnnouncementType } from '@indaver/types'
import type { NewsAnnouncementIndexDto } from '@/models/announcement/index/announcementIndexDto.model.ts'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer.ts'
import { UuidUtil } from '@/utils/uuid.util.ts'

export class NewsAnnouncementIndexDtoBuilder {
  private value: NewsAnnouncementIndexDto = {
    uuid: UuidUtil.getRandom(),
    createdAt: CalendarDateTimeTransformer.toDto(new Date()),
    endDate: CalendarDateTimeTransformer.toDto(new Date()),
    startDate: CalendarDateTimeTransformer.toDto(new Date()),
    updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
    author: {
      uuid: UuidUtil.getRandom(),
      email: '<EMAIL>',
      firstName: '<PERSON>',
      lastName: 'Doe',
    },
    publishStatus: PublishStatus.PUBLISHED,
    translations: [],
    type: AnnouncementType.URGENT,
  }

  constructor() {
  }

  build(): NewsAnnouncementIndexDto {
    return this.value
  }

  withAuthor(firstName: string, lastName: string): NewsAnnouncementIndexDtoBuilder {
    this.value.author.firstName = firstName
    this.value.author.lastName = lastName

    return this
  }

  withEndDate(date: Date): NewsAnnouncementIndexDtoBuilder {
    this.value.endDate = CalendarDateTimeTransformer.toDto(date)

    return this
  }

  withStartDate(date: Date): NewsAnnouncementIndexDtoBuilder {
    this.value.startDate = CalendarDateTimeTransformer.toDto(date)

    return this
  }

  withTranslation(translation: {
    title: string
    language: Locale
  }): NewsAnnouncementIndexDtoBuilder {
    this.value.translations = [
      {
        uuid: UuidUtil.getRandom(),
        title: translation.title,
        language: translation.language,
      },
    ]

    return this
  }

  withType(type: AnnouncementType): NewsAnnouncementIndexDtoBuilder {
    this.value.type = type

    return this
  }
}
