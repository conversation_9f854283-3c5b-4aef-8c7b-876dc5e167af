import { WastePhOption } from '@indaver/types'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WastePhEnumUtil {
  private static i18nKeys = createI18nKeyMap<WastePhOption>({
    [WastePhOption['2_4']]: 'enum.waste_ph.medium',
    [WastePhOption['4_10']]: 'enum.waste_ph.high',
    [WastePhOption['<_2']]: 'enum.waste_ph.low',
    [WastePhOption['>_10']]: 'enum.waste_ph.very_high',
  })

  static getI18nKey(value: WastePhOption): I18nKey {
    return this.i18nKeys.get(value)!
  }
}
