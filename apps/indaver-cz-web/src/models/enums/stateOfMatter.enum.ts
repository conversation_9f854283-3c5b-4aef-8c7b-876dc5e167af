import { StateOfMatter } from '@indaver/types'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class StateOfMatterEnumUtil {
  private static i18nKeys = createI18nKeyMap<StateOfMatter>({
    [StateOfMatter.GASEOUS]: 'enum.state_of_matter.gaseous',
    [StateOfMatter.LIQUID]: 'enum.state_of_matter.liquid',
    [StateOfMatter.LIQUID_WITH_SOLIDS]: 'enum.state_of_matter.liquid_with_solids',
    [StateOfMatter.NO_DATA_AVAILABLE]: 'enum.state_of_matter.no_data_available',
    [StateOfMatter.POWDER]: 'enum.state_of_matter.powder',
    [StateOfMatter.SLUDGY]: 'enum.state_of_matter.sludgy',
    [StateOfMatter.SOLID]: 'enum.state_of_matter.solid',
    [StateOfMatter.VISCOUS]: 'enum.state_of_matter.viscous',
  })

  static getI18nKey(value: StateOfMatter): I18nKey {
    return this.i18nKeys.get(value)!
  }
}
