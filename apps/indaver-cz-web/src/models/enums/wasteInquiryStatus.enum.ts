import { WasteInquiryStatus } from '@indaver/types'
import type { I18n<PERSON>ey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'
import type { SelectOption } from '@/types/tableFilter.type'

export class WasteInquiryStatusEnumUtil {
  private static i18nKeys = createI18nKeyMap<WasteInquiryStatus>({
    [WasteInquiryStatus.COMPLETED]: 'enum.waste_inquiry_status.completed',
    [WasteInquiryStatus.CONFORMITY_CONFIRMED]: 'enum.waste_inquiry_status.conformity_confirmed',
    [WasteInquiryStatus.DRAFT]: 'enum.waste_inquiry_status.draft',
    [WasteInquiryStatus.IN_PROGRESS]: 'enum.waste_inquiry_status.in_progress',
    [WasteInquiryStatus.NEW]: 'enum.waste_inquiry_status.new',
    [WasteInquiryStatus.OFFER_APPROVED]: 'enum.waste_inquiry_status.offer_approved',
    [WasteInquiryStatus.OFFER_SENT]: 'enum.waste_inquiry_status.offer_sent',
    [WasteInquiryStatus.REJECTED]: 'enum.waste_inquiry_status.rejected',
    [WasteInquiryStatus.SOLUTION_DEFINED]: 'enum.waste_inquiry_status.solution_defined',
  })

  static getI18nKey(value: WasteInquiryStatus): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getSelectOptions(): SelectOption<WasteInquiryStatus>[] {
    return Object.values(WasteInquiryStatus).map((status) => ({
      type: 'option',
      value: status,
    }))
  }
}
