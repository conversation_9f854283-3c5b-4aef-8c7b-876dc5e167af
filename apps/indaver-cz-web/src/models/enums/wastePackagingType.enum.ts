import type { Icon } from '@wisemen/vue-core-components'

import { WastePackagingType } from '@indaver/types'
import type { I18nK<PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WastePackagingTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<WastePackagingType>({
    [WastePackagingType.BULK]: 'enum.packaging.bulk.label',
    [WastePackagingType.PACKAGED]: 'enum.packaging.packaged.label',
  })

  static getDescriptionI18nKey(value: WastePackagingType): I18n<PERSON>ey {
    switch (value) {
      case WastePackagingType.BULK:
        return 'enum.packaging.bulk.description'
      case WastePackagingType.PACKAGED:
        return 'enum.packaging.packaged.description'
    }
  }

  static getI18nKey(value: WastePackagingType): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getIcon(value: WastePackagingType): Icon {
    switch (value) {
      case WastePackagingType.BULK:
        return 'bulk'
      case WastePackagingType.PACKAGED:
        return 'packaged'
    }
  }
}
