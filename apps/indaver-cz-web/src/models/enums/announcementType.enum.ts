import { AnnouncementType } from '@indaver/types'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class AnnouncementTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<AnnouncementType>({
    [AnnouncementType.INFORMATIONAL]: 'enum.announcement_type.informational',
    [AnnouncementType.URGENT]: 'enum.announcement_type.urgent',
  })

  static getLabelI18nKey(value: AnnouncementType): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getOptions(): AnnouncementType[] {
    return Object.values(AnnouncementType)
  }
}
