import { InvoiceStatus } from '@indaver/types'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'
import type { SelectOption } from '@/types/tableFilter.type'

export class InvoiceStatusEnumUtil {
  private static i18nKeys = createI18nKeyMap<InvoiceStatus>({
    [InvoiceStatus.CLEARED]: 'enum.invoice_status.cleared',
    [InvoiceStatus.OUTSTANDING]: 'enum.invoice_status.outstanding',
    [InvoiceStatus.OVERDUE]: 'enum.invoice_status.overdue',
  })

  static getI18nKey(value: InvoiceStatus): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getSelectOptions(): SelectOption<InvoiceStatus>[] {
    return Object.values(InvoiceStatus).map((status) => ({
      type: 'option',
      value: status,
    }))
  }
}
