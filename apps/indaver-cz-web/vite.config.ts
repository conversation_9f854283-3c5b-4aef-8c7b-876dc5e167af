/* eslint-disable node/prefer-global/process */
import ImportMetaEnvPlugin from '@import-meta-env/unplugin'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import {
  defineConfig,
  loadEnv,
} from 'vite'
import viteCompression from 'vite-plugin-compression'
import istanbul from 'vite-plugin-istanbul'
import { VitePWA } from 'vite-plugin-pwa'

export default defineConfig(({ mode }) => {
  const env = Object.assign(process.env, loadEnv(mode, process.cwd(), ''))

  return {
    build: {
      rollupOptions: {
        output: {
          manualChunks: (id): string | undefined => {
            if (id.includes('@wisemen+vue-core-components@')) {
              return 'wisemen-vue-core-components'
            }

            if (id.includes('@wisemen+vue-core@')) {
              return 'wisemen-vue-core'
            }

            if (id.includes('@sentry')) {
              return 'sentry'
            }
          },
        },
      },
      sourcemap: true,
    },
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true',
      BUILD_COMMIT: JSON.stringify(process.env.BUILD_COMMIT ?? 'undefined'),
      BUILD_NUMBER: JSON.stringify(process.env.BUILD_NUMBER ?? '0'),
      BUILD_TIMESTAMP: JSON.stringify(process.env.BUILD_TIMESTAMP ?? 'undefined'),
    },
    plugins: [
      ImportMetaEnvPlugin.vite({
        env: '.env',
        example: '.env.example',
      }),
      tailwindcss(),
      viteCompression(),
      VitePWA({
        includeAssets: [
          'favicon.ico',
          'apple-touch-icon.png',
        ],
        injectRegister: 'auto',
        manifest: {
          name: 'Indaver',
          background_color: '#ffffff',
          display: 'fullscreen',
          icons: [
            {
              sizes: '512x512',
              src: 'favicon_512x512.png',
              type: 'image/png',
            },
            {
              sizes: '192x192',
              src: 'favicon_192x192.png',
              type: 'image/png',
            },
          ],
          short_name: 'Indaver',
          theme_color: 'rgb(23, 23, 23)',
        },
        registerType: 'autoUpdate',
        strategies: 'generateSW',
        workbox: {
          clientsClaim: true,
          navigateFallbackDenylist: [
            /api/,
          ],
          runtimeCaching: [
            {
              handler: 'CacheFirst',
              options: {
                cacheableResponse: {
                  statuses: [
                    0,
                    200,
                  ], // cache responses with these statuses
                },
                cacheName: 's3-images',
                expiration: { maxAgeSeconds: 30 * 24 * 60 * 60 }, // cache for 30 days
                matchOptions: { ignoreSearch: true },
              },
              urlPattern: ({ url }): boolean => url.origin.includes('.s3.'),
            },
          ],
          skipWaiting: true,
        },
      }),
      istanbul({
        exclude: [
          'node_modules',
          'test/',
          'src/configs',
          'src/utils',
          'src/libs',
          'src/plugins',
          'src/**/*.builder.ts',
          'src/constants',
          'src/mocks',
          'src/composables',
          'src/client',
        ],
        extension: [
          '.js',
          '.ts',
          '.vue',
        ],
        include: 'src/*',
        requireEnv: false,
      }),
      vue(),
      sentryVitePlugin({
        org: env.SENTRY_ORG,
        project: env.SENTRY_PROJECT,
        url: env.SENTRY_URL,
      }),
    ],
    resolve: {
      alias: [
        {
          find: '@',
          replacement: '/src',
        },
        {
          find: '@indaver/types',
          replacement: '../../packages/types/src',
        },
      ],
    },
  }
})
