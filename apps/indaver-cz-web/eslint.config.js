import path from 'node:path'
import { fileURLToPath } from 'node:url'

import eslintVueConfig from '@wisemen/eslint-config-vue'
import playwright from 'eslint-plugin-playwright'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

export default [
  ...(await eslintVueConfig),
  {
    ...playwright.configs['flat/recommended'],
    files: [
      'tests/**',
    ],
    languageOptions: { parserOptions: { tsconfigRootDir: path.join(__dirname, 'tests') } },
  },
]
