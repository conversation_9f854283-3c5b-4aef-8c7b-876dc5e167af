{
  "extends": "../../tsconfig.base.json",
  "references": [
    { "path": "../../packages/types" }
  ],
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@tests/*": ["./tests/*"],
      "@@/*": ["./tests/*"],
      "@indaver/types": ["../../packages/types/src"]
    },
    "types": ["vite-plugin-pwa/client"],
    "allowImportingTsExtensions": true,

    /* Linting */
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "isolatedModules": true
  },
  "include": [
    "env.d.ts",
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.vue",
    "src/**/tests/*.spec.ts",
    "tests/**/*",
    "tests/**/*.ts",
    "virtual:pwa-register/vue"
  ],
  "exclude": ["src/**/__tests__/*"]
}
