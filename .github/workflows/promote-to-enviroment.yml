name: Promote to environment Monorepo

on:
  workflow_dispatch:
    inputs:
      application:
        description: 'Application to promote (api or web)'
        type: choice
        required: true
        options:
          - api
          - web
      environment:
        description: Target environment to deploy TO
        type: environment
        required: true
      source-tag:
        description: Source tag (defaults to previous environment)
        type: string
        required: false

permissions:
  contents: read
  id-token: write # Required for Azure login

jobs:
  promote-api:
    name: Promote API to ${{ github.event.inputs.environment }}
    if: github.event.inputs.application == 'api'
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-promote-to-environment-azure.yml@main
    with:
      environment-source: ${{ github.event.inputs.source-tag }}
      environment-target: ${{ github.event.inputs.environment }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}

  promote-web:
    name: Promote Web to ${{ github.event.inputs.environment }}
    if: github.event.inputs.application == 'web'
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-promote-to-environment-azure.yml@main
    with:
      environment-source: ${{ github.event.inputs.source-tag }}
      environment-target: ${{ github.event.inputs.environment }}
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}

  no-application-selected:
    name: No Valid Application Selected
    if: github.event.inputs.application != 'api' && github.event.inputs.application != 'web'
    runs-on: ubuntu-latest
    steps:
      - run: echo "No valid application (api or web) was selected for promotion. Please choose 'api' or 'web'."
