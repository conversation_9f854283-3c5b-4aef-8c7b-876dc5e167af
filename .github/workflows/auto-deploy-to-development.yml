name: Auto-deploy to development Monorepo

on:
  push:
    branches: [main]
  workflow_dispatch:

permissions:
  contents: read
  id-token: write # Required for Azure login

jobs:
  filter:
    runs-on: ubuntu-latest
    outputs:
      api_changed: ${{ steps.changes.outputs.api }}
      web_changed: ${{ steps.changes.outputs.web }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            api:
              - 'apps/indaver-cz-api/**'
            web:
              - 'apps/indaver-cz-web/**'

  deploy-api:
    name: Deploy API to Development
    needs: filter
    if: needs.filter.outputs.api_changed == 'true'
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-build-and-deploy-azure.yml@main
    with:
      environment: development
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}

  deploy-web:
    name: Deploy Web to Development
    needs: filter
    if: needs.filter.outputs.web_changed == 'true'
    uses: wisemen-digital/devops-github-actions/.github/workflows/workflow-build-and-deploy-azure.yml@main
    with:
      environment: development
    secrets:
      AZURE_CREDENTIALS: ${{ secrets.AZURE_CREDENTIALS }}
      AZURE_CR_USER: ${{ secrets.AZURE_CR_USER }}
      AZURE_CR_TOKEN: ${{ secrets.AZURE_CR_TOKEN }}

  no-app-changes:
    name: No App Changes Detected for Deployment
    needs: filter
    if: needs.filter.outputs.api_changed != 'true' && needs.filter.outputs.web_changed != 'true'
    runs-on: ubuntu-latest
    steps:
      - run: echo "No changes detected in API or Web applications. Skipping deployment."
