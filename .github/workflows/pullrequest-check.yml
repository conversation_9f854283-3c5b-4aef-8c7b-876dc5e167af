name: PR Check Monorepo

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches:
      - main

permissions:
  checks: write
  contents: read

jobs:
  filter:
    runs-on: ubuntu-latest
    outputs:
      api_changed: ${{ steps.changes.outputs.api }}
      web_changed: ${{ steps.changes.outputs.web }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            api:
              - 'apps/indaver-cz-api/**'
            web:
              - 'apps/indaver-cz-web/**'

  check-api:
    name: API Checks
    needs: filter
    if: github.event.pull_request.draft == false && needs.filter.outputs.api_changed == 'true'
    uses: wisemen-digital/devops-github-actions/.github/workflows/node-build-and-test.yml@main
    with:
      test-postgres-image: timescale/timescaledb-ha:pg16
      test-typesense-enabled: true
      test-redis-enabled: true
      test-nats-enabled: true
    secrets: inherit

  check-web:
    name: Web Checks
    needs: filter
    if: github.event.pull_request.draft == false && needs.filter.outputs.web_changed == 'true'
    uses: wisemen-digital/devops-github-actions/.github/workflows/web-build-and-test.yml@main
    with:
      test-playwright-image: 'mcr.microsoft.com/playwright:v1.55.0'
      test-timeout: 10
    secrets: inherit

  no-app-changes:
    name: No App Changes Detected
    needs: filter
    if: github.event.pull_request.draft == false && needs.filter.outputs.api_changed != 'true' && needs.filter.outputs.web_changed != 'true'
    runs-on: ubuntu-latest
    steps:
      - run: echo "No changes detected in API or Web applications. Skipping app-specific checks."
