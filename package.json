{"name": "indaver-monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "pnpm --filter @indaver/types build && pnpm -r --if-present build", "test": "pnpm -r --if-present test", "dev": "pnpm -r --parallel --if-present dev", "dev:api": "pnpm --filter nestjs-example dev", "dev:web": "pnpm --filter @indaver/web dev", "dev:types": "pnpm --filter @indaver/types dev", "clean": "pnpm -r --if-present clean", "type-check": "pnpm -r --if-present type-check", "lint": "pnpm -r lint", "lint:fix": "pnpm -r --if-present lint:fix", "format": "prettier --write .", "format:check": "prettier --check .", "install:all": "pnpm install", "reset": "pnpm clean && pnpm install:all && pnpm build", "prepare": "husky install", "postinstall": "husky install"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^24.5.2", "eslint": "9.36.0", "typescript": "^5.9.2", "husky": "9.1.7", "prettier": "^3.0.0", "@wisemen/eslint-config-nestjs": "^0.1.7", "@wisemen/eslint-config-vue": "1.2.0-beta.1", "eslint-plugin-unicorn": "61.0.2", "eslint-plugin-playwright": "2.2.2", "tailwindcss": "4.1.12"}}