{"lastValidatedTimestamp": 1761731298059, "projects": {"/Users/<USER>/Projects/indaver/indaver-monorepo": {"name": "indaver-monorepo", "version": "1.0.0"}, "/Users/<USER>/Projects/indaver/indaver-monorepo/apps/indaver-cz-api": {"name": "nestjs-example", "version": "0.1.0"}, "/Users/<USER>/Projects/indaver/indaver-monorepo/apps/indaver-cz-web": {"name": "@indaver/web"}, "/Users/<USER>/Projects/indaver/indaver-monorepo/packages/types": {"name": "@indaver/types", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": false}