hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@angular-devkit/core@19.2.15(chokidar@4.0.3)':
    '@angular-devkit/core': private
  '@angular-devkit/schematics-cli@19.2.15(@types/node@24.9.1)(chokidar@4.0.3)':
    '@angular-devkit/schematics-cli': private
  '@angular-devkit/schematics@19.2.15(chokidar@4.0.3)':
    '@angular-devkit/schematics': private
  '@antfu/eslint-config@4.2.1(@typescript-eslint/utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(@vue/compiler-sfc@3.5.22)(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.9.1)(jiti@2.6.1)(jsdom@26.1.0)(lightningcss@1.30.1)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))':
    '@antfu/eslint-config': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@anthropic-ai/claude-agent-sdk@0.1.23(zod@3.24.4)':
    '@anthropic-ai/claude-agent-sdk': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@apm-js-collab/code-transformer@0.8.2':
    '@apm-js-collab/code-transformer': private
  '@apm-js-collab/tracing-hooks@0.3.1':
    '@apm-js-collab/tracing-hooks': private
  '@asamuzakjp/css-color@3.2.0':
    '@asamuzakjp/css-color': private
  '@axe-core/playwright@4.10.2(playwright-core@1.56.0-alpha-1758750661000)':
    '@axe-core/playwright': private
  '@azure/abort-controller@2.1.2':
    '@azure/abort-controller': private
  '@azure/core-auth@1.10.1':
    '@azure/core-auth': private
  '@azure/core-client@1.10.1':
    '@azure/core-client': private
  '@azure/core-http-compat@2.3.1':
    '@azure/core-http-compat': private
  '@azure/core-lro@2.7.2':
    '@azure/core-lro': private
  '@azure/core-paging@1.6.2':
    '@azure/core-paging': private
  '@azure/core-rest-pipeline@1.22.1':
    '@azure/core-rest-pipeline': private
  '@azure/core-tracing@1.3.1':
    '@azure/core-tracing': private
  '@azure/core-util@1.13.1':
    '@azure/core-util': private
  '@azure/core-xml@1.5.0':
    '@azure/core-xml': private
  '@azure/identity@4.13.0':
    '@azure/identity': private
  '@azure/logger@1.3.0':
    '@azure/logger': private
  '@azure/msal-browser@4.25.1':
    '@azure/msal-browser': private
  '@azure/msal-common@15.13.0':
    '@azure/msal-common': private
  '@azure/msal-node@3.8.0':
    '@azure/msal-node': private
  '@azure/storage-blob@12.29.1':
    '@azure/storage-blob': private
  '@azure/storage-common@12.1.1':
    '@azure/storage-common': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.4':
    '@babel/compat-data': private
  '@babel/core@7.28.4':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.28.3(@babel/core@7.28.4)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.4)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.4)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.4)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.4)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.4)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.28.3':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.28.4':
    '@babel/helpers': private
  '@babel/parser@7.28.4':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.28.3(@babel/core@7.28.4)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.4)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.4)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.4)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.4)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.4)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.4(@babel/core@7.28.4)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.28.3(@babel/core@7.28.4)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.4(@babel/core@7.28.4)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.4)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.4)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.4(@babel/core@7.28.4)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.4)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.28.4(@babel/core@7.28.4)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.4)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.4)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.3(@babel/core@7.28.4)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.4)':
    '@babel/preset-modules': private
  '@babel/runtime@7.28.4':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.4':
    '@babel/traverse': private
  '@babel/types@7.28.4':
    '@babel/types': private
  '@bcoe/v8-coverage@1.0.2':
    '@bcoe/v8-coverage': private
  '@borewit/text-codec@0.1.1':
    '@borewit/text-codec': private
  '@bufbuild/protobuf@2.10.0':
    '@bufbuild/protobuf': private
  '@bundled-es-modules/cookie@2.0.1':
    '@bundled-es-modules/cookie': private
  '@bundled-es-modules/statuses@1.0.1':
    '@bundled-es-modules/statuses': private
  '@bundled-es-modules/tough-cookie@0.1.6':
    '@bundled-es-modules/tough-cookie': private
  '@clack/core@0.4.2':
    '@clack/core': private
  '@clack/prompts@0.10.1':
    '@clack/prompts': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@connectrpc/connect-node@2.1.0(@bufbuild/protobuf@2.10.0)(@connectrpc/connect@2.1.0(@bufbuild/protobuf@2.10.0))':
    '@connectrpc/connect-node': private
  '@connectrpc/connect-web@2.1.0(@bufbuild/protobuf@2.10.0)(@connectrpc/connect@2.1.0(@bufbuild/protobuf@2.10.0))':
    '@connectrpc/connect-web': private
  '@connectrpc/connect@2.1.0(@bufbuild/protobuf@2.10.0)':
    '@connectrpc/connect': private
  '@csstools/color-helpers@5.1.0':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.1.0(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.4':
    '@csstools/css-tokenizer': private
  '@emmetio/abbreviation@2.3.3':
    '@emmetio/abbreviation': private
  '@emmetio/css-abbreviation@2.1.8':
    '@emmetio/css-abbreviation': private
  '@emmetio/css-parser@https://codeload.github.com/ramya-rao-a/css-parser/tar.gz/370c480ac103bd17c7bcfb34bf5d577dc40d3660':
    '@emmetio/css-parser': private
  '@emmetio/html-matcher@1.3.0':
    '@emmetio/html-matcher': private
  '@emmetio/scanner@1.0.4':
    '@emmetio/scanner': private
  '@emmetio/stream-reader-utils@0.1.0':
    '@emmetio/stream-reader-utils': private
  '@emmetio/stream-reader@2.2.0':
    '@emmetio/stream-reader': private
  '@es-joy/jsdoccomment@0.50.2':
    '@es-joy/jsdoccomment': private
  '@esbuild/darwin-x64@0.25.11':
    '@esbuild/darwin-x64': private
  '@eslint-community/eslint-plugin-eslint-comments@4.5.0(eslint@9.36.0(jiti@2.6.1))':
    '@eslint-community/eslint-plugin-eslint-comments': private
  '@eslint-community/eslint-utils@4.9.0(eslint@9.36.0(jiti@2.6.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.4.0(eslint@9.36.0(jiti@2.6.1))':
    '@eslint/compat': private
  '@eslint/config-array@0.21.1':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.2':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.38.0':
    '@eslint/js': private
  '@eslint/markdown@6.6.0':
    '@eslint/markdown': private
  '@eslint/object-schema@2.1.7':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': private
  '@fast-csv/format@4.3.5':
    '@fast-csv/format': private
  '@fast-csv/parse@4.3.6':
    '@fast-csv/parse': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.4':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@floating-ui/vue@1.1.9(vue@3.5.13(typescript@5.9.3))':
    '@floating-ui/vue': private
  '@fontsource-variable/montserrat@5.2.6':
    '@fontsource-variable/montserrat': private
  '@formkit/drag-and-drop@0.5.3':
    '@formkit/drag-and-drop': private
  '@grpc/grpc-js@1.14.0':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.8.0':
    '@grpc/proto-loader': private
  '@hapi/address@5.1.1':
    '@hapi/address': private
  '@hapi/formula@3.0.2':
    '@hapi/formula': private
  '@hapi/hoek@11.0.7':
    '@hapi/hoek': private
  '@hapi/pinpoint@2.0.1':
    '@hapi/pinpoint': private
  '@hapi/tlds@1.1.3':
    '@hapi/tlds': private
  '@hapi/topo@6.0.2':
    '@hapi/topo': private
  '@hey-api/codegen-core@0.1.0(typescript@5.9.3)':
    '@hey-api/codegen-core': private
  '@hey-api/json-schema-ref-parser@1.1.0':
    '@hey-api/json-schema-ref-parser': private
  '@hey-api/openapi-ts@0.83.1(magicast@0.3.5)(typescript@5.9.3)':
    '@hey-api/openapi-ts': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.7':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-x64@0.33.5':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-x64@1.0.4':
    '@img/sharp-libvips-darwin-x64': private
  '@import-meta-env/cli@0.7.3(@import-meta-env/unplugin@0.6.2)':
    '@import-meta-env/cli': private
  '@import-meta-env/unplugin@0.6.2(@import-meta-env/cli@0.7.3)':
    '@import-meta-env/unplugin': private
  '@inquirer/ansi@1.0.1':
    '@inquirer/ansi': private
  '@inquirer/checkbox@4.3.0(@types/node@24.9.1)':
    '@inquirer/checkbox': private
  '@inquirer/confirm@5.1.19(@types/node@24.9.1)':
    '@inquirer/confirm': private
  '@inquirer/core@10.3.0(@types/node@24.9.1)':
    '@inquirer/core': private
  '@inquirer/editor@4.2.21(@types/node@24.9.1)':
    '@inquirer/editor': private
  '@inquirer/expand@4.0.21(@types/node@24.9.1)':
    '@inquirer/expand': private
  '@inquirer/external-editor@1.0.2(@types/node@24.9.1)':
    '@inquirer/external-editor': private
  '@inquirer/figures@1.0.14':
    '@inquirer/figures': private
  '@inquirer/input@4.2.5(@types/node@24.9.1)':
    '@inquirer/input': private
  '@inquirer/number@3.0.21(@types/node@24.9.1)':
    '@inquirer/number': private
  '@inquirer/password@4.0.21(@types/node@24.9.1)':
    '@inquirer/password': private
  '@inquirer/prompts@7.8.0(@types/node@24.9.1)':
    '@inquirer/prompts': private
  '@inquirer/rawlist@4.1.9(@types/node@24.9.1)':
    '@inquirer/rawlist': private
  '@inquirer/search@3.2.0(@types/node@24.9.1)':
    '@inquirer/search': private
  '@inquirer/select@4.4.0(@types/node@24.9.1)':
    '@inquirer/select': private
  '@inquirer/type@3.0.9(@types/node@24.9.1)':
    '@inquirer/type': private
  '@internationalized/date@3.10.0':
    '@internationalized/date': private
  '@internationalized/number@3.6.5':
    '@internationalized/number': private
  '@intlify/core-base@11.1.10':
    '@intlify/core-base': private
  '@intlify/eslint-plugin-vue-i18n@4.0.0(eslint@9.36.0(jiti@2.6.1))(jsonc-eslint-parser@2.4.1)(vue-eslint-parser@9.4.3(eslint@9.36.0(jiti@2.6.1)))(yaml-eslint-parser@1.3.0)':
    '@intlify/eslint-plugin-vue-i18n': private
  '@intlify/message-compiler@11.1.10':
    '@intlify/message-compiler': private
  '@intlify/shared@11.1.10':
    '@intlify/shared': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/diff-sequences@30.0.1':
    '@jest/diff-sequences': private
  '@jest/expect-utils@30.2.0':
    '@jest/expect-utils': private
  '@jest/get-type@30.1.0':
    '@jest/get-type': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/schemas@30.0.5':
    '@jest/schemas': private
  '@jest/types@30.2.0':
    '@jest/types': private
  '@johnsoncodehk/pug-beautify@0.2.2':
    '@johnsoncodehk/pug-beautify': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.11':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.31':
    '@jridgewell/trace-mapping': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@marker.io/browser@0.20.2':
    '@marker.io/browser': private
  '@microsoft/microsoft-graph-client@3.0.7(@azure/identity@4.13.0)':
    '@microsoft/microsoft-graph-client': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@mswjs/cookies@1.1.1':
    '@mswjs/cookies': private
  '@mswjs/interceptors@0.39.8':
    '@mswjs/interceptors': private
  '@nats-io/jetstream@3.2.0':
    '@nats-io/jetstream': private
  '@nats-io/jwt@0.0.11':
    '@nats-io/jwt': private
  '@nats-io/kv@3.2.0':
    '@nats-io/kv': private
  '@nats-io/nats-core@3.2.0':
    '@nats-io/nats-core': private
  '@nats-io/nkeys@2.0.3':
    '@nats-io/nkeys': private
  '@nats-io/nuid@2.0.3':
    '@nats-io/nuid': private
  '@nats-io/services@3.2.0':
    '@nats-io/services': private
  '@nats-io/transport-node@3.2.0':
    '@nats-io/transport-node': private
  '@nestjs/cli@11.0.10(@types/node@24.9.1)':
    '@nestjs/cli': private
  '@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@nestjs/common': private
  '@nestjs/config@4.0.2(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(rxjs@7.8.2)':
    '@nestjs/config': private
  '@nestjs/core@11.1.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/platform-express@11.1.7)(@nestjs/websockets@11.1.7)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@nestjs/core': private
  '@nestjs/event-emitter@3.0.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)':
    '@nestjs/event-emitter': private
  '@nestjs/jwt@11.0.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))':
    '@nestjs/jwt': private
  '@nestjs/mapped-types@2.1.0(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/mapped-types': private
  '@nestjs/platform-express@11.1.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)':
    '@nestjs/platform-express': private
  '@nestjs/platform-ws@11.1.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/websockets@11.1.7)(rxjs@7.8.2)':
    '@nestjs/platform-ws': private
  '@nestjs/schematics@11.0.9(chokidar@4.0.3)(typescript@5.9.3)':
    '@nestjs/schematics': private
  '@nestjs/swagger@11.2.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/swagger': private
  '@nestjs/testing@11.1.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(@nestjs/platform-express@11.1.7)':
    '@nestjs/testing': private
  '@nestjs/typeorm@11.0.0(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(reflect-metadata@0.2.2)(rxjs@7.8.2)(typeorm@0.3.27(pg@8.16.3)(redis@5.8.3)(reflect-metadata@0.2.2))':
    '@nestjs/typeorm': private
  '@nestjs/websockets@11.1.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@nestjs/websockets': private
  '@ngneat/falso@8.0.2':
    '@ngneat/falso': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@number-flow/vue@0.4.8(vue@3.5.13(typescript@5.9.3))':
    '@number-flow/vue': private
  '@nuxt/opencollective@0.4.1':
    '@nuxt/opencollective': private
  '@one-ini/wasm@0.1.1':
    '@one-ini/wasm': private
  '@onesignal/node-onesignal@5.2.1-beta1':
    '@onesignal/node-onesignal': private
  '@open-draft/deferred-promise@2.2.0':
    '@open-draft/deferred-promise': private
  '@open-draft/logger@0.3.0':
    '@open-draft/logger': private
  '@open-draft/until@2.1.0':
    '@open-draft/until': private
  '@opentelemetry/api-logs@0.205.0':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/context-async-hooks@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/exporter-logs-otlp-grpc@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-grpc': private
  '@opentelemetry/exporter-logs-otlp-http@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-http': private
  '@opentelemetry/exporter-logs-otlp-proto@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-logs-otlp-proto': private
  '@opentelemetry/exporter-metrics-otlp-grpc@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-grpc': private
  '@opentelemetry/exporter-metrics-otlp-http@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-http': private
  '@opentelemetry/exporter-metrics-otlp-proto@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-metrics-otlp-proto': private
  '@opentelemetry/exporter-prometheus@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-prometheus': private
  '@opentelemetry/exporter-trace-otlp-grpc@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-grpc': private
  '@opentelemetry/exporter-trace-otlp-http@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-http': private
  '@opentelemetry/exporter-trace-otlp-proto@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-trace-otlp-proto': private
  '@opentelemetry/exporter-zipkin@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/exporter-zipkin': private
  '@opentelemetry/instrumentation-amqplib@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': private
  '@opentelemetry/instrumentation-aws-sdk@0.60.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-aws-sdk': private
  '@opentelemetry/instrumentation-connect@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-dataloader@0.22.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': private
  '@opentelemetry/instrumentation-express@0.54.3(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fs@0.24.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': private
  '@opentelemetry/instrumentation-generic-pool@0.48.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': private
  '@opentelemetry/instrumentation-graphql@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-hapi@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-kafkajs@0.14.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': private
  '@opentelemetry/instrumentation-knex@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': private
  '@opentelemetry/instrumentation-koa@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-lru-memoizer@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': private
  '@opentelemetry/instrumentation-mongodb@0.57.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.50.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-nestjs-core@0.51.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-nestjs-core': private
  '@opentelemetry/instrumentation-pg@0.58.3(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation-redis-4@0.49.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis-4': private
  '@opentelemetry/instrumentation-redis@0.53.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis': private
  '@opentelemetry/instrumentation-tedious@0.23.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': private
  '@opentelemetry/instrumentation-undici@0.15.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': private
  '@opentelemetry/instrumentation@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/otlp-exporter-base@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-exporter-base': private
  '@opentelemetry/otlp-grpc-exporter-base@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-grpc-exporter-base': private
  '@opentelemetry/otlp-transformer@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/otlp-transformer': private
  '@opentelemetry/propagator-b3@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/propagator-b3': private
  '@opentelemetry/propagator-jaeger@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/propagator-jaeger': private
  '@opentelemetry/redis-common@0.37.0':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resources@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-logs@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-logs': private
  '@opentelemetry/sdk-metrics@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-metrics': private
  '@opentelemetry/sdk-node@0.205.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-node': private
  '@opentelemetry/sdk-trace-base@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/sdk-trace-node@2.1.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-node': private
  '@opentelemetry/semantic-conventions@1.37.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.41.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@oxlint/darwin-x64@0.15.13':
    '@oxlint/darwin-x64': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.1.2':
    '@pkgr/core': private
  '@playwright/mcp@0.0.40':
    '@playwright/mcp': private
  '@playwright/test@1.55.0':
    '@playwright/test': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@prisma/instrumentation@6.15.0(@opentelemetry/api@1.9.0)':
    '@prisma/instrumentation': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@redis/bloom@5.8.3(@redis/client@5.8.3)':
    '@redis/bloom': private
  '@redis/client@5.8.3':
    '@redis/client': private
  '@redis/json@5.8.3(@redis/client@5.8.3)':
    '@redis/json': private
  '@redis/search@5.8.3(@redis/client@5.8.3)':
    '@redis/search': private
  '@redis/time-series@5.8.3(@redis/client@5.8.3)':
    '@redis/time-series': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@rolldown/pluginutils@1.0.0-beta.29':
    '@rolldown/pluginutils': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.28.4)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-node-resolve@15.3.1(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@2.79.2)':
    '@rollup/plugin-terser': private
  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-darwin-x64@4.52.5':
    '@rollup/rollup-darwin-x64': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sentry-internal/browser-utils@10.5.0':
    '@sentry-internal/browser-utils': private
  '@sentry-internal/feedback@10.5.0':
    '@sentry-internal/feedback': private
  '@sentry-internal/replay-canvas@10.5.0':
    '@sentry-internal/replay-canvas': private
  '@sentry-internal/replay@10.5.0':
    '@sentry-internal/replay': private
  '@sentry-internal/tracing@7.120.4':
    '@sentry-internal/tracing': private
  '@sentry/babel-plugin-component-annotate@4.1.1':
    '@sentry/babel-plugin-component-annotate': private
  '@sentry/browser@10.5.0':
    '@sentry/browser': private
  '@sentry/bundler-plugin-core@4.1.1':
    '@sentry/bundler-plugin-core': private
  '@sentry/cli-darwin@2.56.1':
    '@sentry/cli-darwin': private
  '@sentry/cli@2.56.1':
    '@sentry/cli': private
  '@sentry/core@7.114.0':
    '@sentry/core': private
  '@sentry/integrations@7.114.0':
    '@sentry/integrations': private
  '@sentry/nestjs@10.20.0(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)':
    '@sentry/nestjs': private
  '@sentry/node-core@10.20.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/core@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.204.0(@opentelemetry/api@1.9.0))(@opentelemetry/resources@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.37.0)':
    '@sentry/node-core': private
  '@sentry/node@10.20.0':
    '@sentry/node': private
  '@sentry/opentelemetry@10.20.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/core@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@2.1.0(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.37.0)':
    '@sentry/opentelemetry': private
  '@sentry/tracing@7.120.4':
    '@sentry/tracing': private
  '@sentry/types@7.114.0':
    '@sentry/types': private
  '@sentry/utils@7.114.0':
    '@sentry/utils': private
  '@sentry/vite-plugin@4.1.1':
    '@sentry/vite-plugin': private
  '@sentry/vue@10.5.0(pinia@3.0.3(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@sentry/vue': private
  '@sinclair/typebox@0.34.41':
    '@sinclair/typebox': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@13.0.5':
    '@sinonjs/fake-timers': private
  '@sinonjs/samsam@8.0.3':
    '@sinonjs/samsam': private
  '@sqltools/formatter@1.2.5':
    '@sqltools/formatter': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@stylistic/eslint-plugin@4.4.1(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)':
    '@stylistic/eslint-plugin': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-darwin-x64@4.1.12':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': private
  '@tailwindcss/typography@0.5.16(tailwindcss@4.1.12)':
    '@tailwindcss/typography': private
  '@tailwindcss/vite@4.1.12(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))':
    '@tailwindcss/vite': private
  '@tanstack/match-sorter-utils@8.19.4':
    '@tanstack/match-sorter-utils': private
  '@tanstack/query-core@5.85.5':
    '@tanstack/query-core': private
  '@tanstack/query-devtools@5.84.0':
    '@tanstack/query-devtools': private
  '@tanstack/virtual-core@3.13.12':
    '@tanstack/virtual-core': private
  '@tanstack/vue-query-devtools@5.85.5(@tanstack/vue-query@5.85.5(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@tanstack/vue-query-devtools': private
  '@tanstack/vue-query@5.85.5(vue@3.5.13(typescript@5.9.3))':
    '@tanstack/vue-query': private
  '@tanstack/vue-virtual@3.13.12(vue@3.5.13(typescript@5.9.3))':
    '@tanstack/vue-virtual': private
  '@tiptap/core@3.7.2(@tiptap/pm@3.7.2)':
    '@tiptap/core': private
  '@tiptap/extension-blockquote@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-blockquote': private
  '@tiptap/extension-bold@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-bullet-list@3.7.2(@tiptap/extension-list@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-bullet-list': private
  '@tiptap/extension-code-block@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-code-block': private
  '@tiptap/extension-code@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-code': private
  '@tiptap/extension-document@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-document': private
  '@tiptap/extension-dropcursor@3.7.2(@tiptap/extensions@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-dropcursor': private
  '@tiptap/extension-floating-menu@3.7.2(@floating-ui/dom@1.7.4)(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@3.7.2(@tiptap/extensions@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-hard-break@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-hard-break': private
  '@tiptap/extension-heading@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-heading': private
  '@tiptap/extension-horizontal-rule@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-italic@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-link@3.2.1(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-link': private
  '@tiptap/extension-list-item@3.7.2(@tiptap/extension-list@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-list-item': private
  '@tiptap/extension-list-keymap@3.7.2(@tiptap/extension-list@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-list-keymap': private
  '@tiptap/extension-list@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extension-list': private
  '@tiptap/extension-ordered-list@3.7.2(@tiptap/extension-list@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2))':
    '@tiptap/extension-ordered-list': private
  '@tiptap/extension-paragraph@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-strike@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-text@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-text': private
  '@tiptap/extension-underline@3.2.1(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))':
    '@tiptap/extension-underline': private
  '@tiptap/extensions@3.7.2(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)':
    '@tiptap/extensions': private
  '@tiptap/pm@3.7.2':
    '@tiptap/pm': private
  '@tiptap/starter-kit@3.2.1':
    '@tiptap/starter-kit': private
  '@tiptap/vue-3@3.2.1(@floating-ui/dom@1.7.4)(@tiptap/core@3.7.2(@tiptap/pm@3.7.2))(@tiptap/pm@3.7.2)(vue@3.5.13(typescript@5.9.3))':
    '@tiptap/vue-3': private
  '@tokenizer/inflate@0.2.7':
    '@tokenizer/inflate': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tsconfig/node22@22.0.2':
    '@tsconfig/node22': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/chai@5.2.3':
    '@types/chai': private
  '@types/clone-deep@4.0.4':
    '@types/clone-deep': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/deep-eql@4.0.2':
    '@types/deep-eql': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/exceljs@1.3.2':
    '@types/exceljs': private
  '@types/express-serve-static-core@5.1.0':
    '@types/express-serve-static-core': private
  '@types/express@5.0.3':
    '@types/express': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jsdom@21.1.7':
    '@types/jsdom': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/jsonwebtoken@9.0.10':
    '@types/jsonwebtoken': private
  '@types/jwt-encode@1.0.3':
    '@types/jwt-encode': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/methods@1.1.4':
    '@types/methods': private
  '@types/microsoft-graph@2.40.1':
    '@types/microsoft-graph': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/mysql@2.15.27':
    '@types/mysql': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': private
  '@types/pg@8.15.5':
    '@types/pg': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/send@1.2.0':
    '@types/send': private
  '@types/serve-static@1.15.9':
    '@types/serve-static': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/sinon@17.0.4':
    '@types/sinon': private
  '@types/sinonjs__fake-timers@8.1.5':
    '@types/sinonjs__fake-timers': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/statuses@2.0.6':
    '@types/statuses': private
  '@types/superagent@8.1.9':
    '@types/superagent': private
  '@types/supertest@6.0.3':
    '@types/supertest': private
  '@types/tedious@4.0.14':
    '@types/tedious': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/validator@13.15.3':
    '@types/validator': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@8.46.2(@typescript-eslint/parser@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.46.2(typescript@5.9.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.46.2':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.46.2(typescript@5.9.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.46.2':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.46.2(typescript@5.9.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.46.2':
    '@typescript-eslint/visitor-keys': private
  '@typespec/ts-http-runtime@0.3.1':
    '@typespec/ts-http-runtime': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@vitejs/plugin-vue@6.0.1(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))(vue@3.5.13(typescript@5.9.3))':
    '@vitejs/plugin-vue': private
  '@vitest/coverage-v8@3.2.4(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.9.1)(jiti@2.6.1)(jsdom@26.1.0)(lightningcss@1.30.1)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))':
    '@vitest/coverage-v8': private
  '@vitest/eslint-plugin@1.3.23(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3)(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.9.1)(jiti@2.6.1)(jsdom@26.1.0)(lightningcss@1.30.1)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))':
    '@vitest/eslint-plugin': private
  '@vitest/expect@3.2.4':
    '@vitest/expect': private
  '@vitest/mocker@3.2.4(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.2.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.2.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.2.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.2.4':
    '@vitest/spy': private
  '@vitest/utils@3.2.4':
    '@vitest/utils': private
  '@volar/language-core@2.4.23':
    '@volar/language-core': private
  '@volar/language-server@2.4.23':
    '@volar/language-server': private
  '@volar/language-service@2.4.23':
    '@volar/language-service': private
  '@volar/source-map@2.4.23':
    '@volar/source-map': private
  '@volar/typescript@2.4.23':
    '@volar/typescript': private
  '@vscode/emmet-helper@2.11.0':
    '@vscode/emmet-helper': private
  '@vscode/l10n@0.0.18':
    '@vscode/l10n': private
  '@vue/babel-helper-vue-transform-on@1.5.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.5.0(@babel/core@7.28.4)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.5.0(@babel/core@7.28.4)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.22':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.22':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.22':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.22':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/devtools-core@8.0.3(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))(vue@3.5.13(typescript@5.9.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@8.0.3':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@8.0.3':
    '@vue/devtools-shared': private
  '@vue/language-core@3.0.6(typescript@5.9.3)':
    '@vue/language-core': private
  '@vue/language-server@3.0.6(typescript@5.9.3)':
    '@vue/language-server': private
  '@vue/language-service@3.0.6(typescript@5.9.3)':
    '@vue/language-service': private
  '@vue/reactivity@3.5.13':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.13':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.13':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.13(vue@3.5.13(typescript@5.9.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.22':
    '@vue/shared': private
  '@vue/test-utils@2.4.6':
    '@vue/test-utils': private
  '@vue/tsconfig@0.7.0(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3))':
    '@vue/tsconfig': private
  '@vue/typescript-plugin@3.0.6(typescript@5.9.3)':
    '@vue/typescript-plugin': private
  '@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3))':
    '@vueuse/core': private
  '@vueuse/metadata@13.7.0':
    '@vueuse/metadata': private
  '@vueuse/router@13.7.0(vue-router@4.5.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@vueuse/router': private
  '@vueuse/shared@13.7.0(vue@3.5.13(typescript@5.9.3))':
    '@vueuse/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@wisemen/app-container@2.1.20':
    '@wisemen/app-container': private
  '@wisemen/coordinates@0.0.10(@nestjs/swagger@11.2.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2))(class-validator@0.14.2)(typeorm@0.3.27(pg@8.16.3)(redis@5.8.3)(reflect-metadata@0.2.2))':
    '@wisemen/coordinates': private
  '@wisemen/decorators@0.0.10(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@wisemen/decorators': private
  '@wisemen/eslint-config-nestjs@0.1.7(@typescript-eslint/eslint-plugin@8.46.2(@typescript-eslint/parser@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(@typescript-eslint/utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1))':
    '@wisemen/eslint-config-nestjs': private
  '@wisemen/eslint-config-vue@1.2.0-beta.1(@typescript-eslint/eslint-plugin@8.46.2(@typescript-eslint/parser@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(@typescript-eslint/utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(@vue/compiler-sfc@3.5.22)(eslint@9.36.0(jiti@2.6.1))(jsonc-eslint-parser@2.4.1)(tailwindcss@4.1.12)(typescript@5.9.3)(vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.9.1)(jiti@2.6.1)(jsdom@26.1.0)(lightningcss@1.30.1)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))(vue-eslint-parser@9.4.3(eslint@9.36.0(jiti@2.6.1)))(yaml-eslint-parser@1.3.0)':
    '@wisemen/eslint-config-vue': private
  '@wisemen/nestjs-typeorm@0.0.30(8bfcf1d9f0df953b0ee4625187c7bcf5)':
    '@wisemen/nestjs-typeorm': private
  '@wisemen/one-of@0.0.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/swagger@11.2.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2))(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(typeorm@0.3.27(pg@8.16.3)(redis@5.8.3)(reflect-metadata@0.2.2))':
    '@wisemen/one-of': private
  '@wisemen/opentelemetry@0.0.4(@opentelemetry/api@1.9.0)(reflect-metadata@0.2.2)':
    '@wisemen/opentelemetry': private
  '@wisemen/pagination@0.0.7(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/swagger@11.2.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2))(@wisemen/validators@0.0.16(@wisemen/time@0.0.26(class-validator@0.14.2)(dayjs@1.11.18))(class-validator@0.14.2)(dayjs@1.11.18))(class-transformer@0.5.1)(class-validator@0.14.2)':
    '@wisemen/pagination': private
  '@wisemen/pgboss-nestjs-job@1.1.10(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(@opentelemetry/api@1.9.0)(@sentry/nestjs@10.20.0(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7))(@wisemen/nestjs-typeorm@0.0.30(8bfcf1d9f0df953b0ee4625187c7bcf5))(@wisemen/opentelemetry@0.0.4(@opentelemetry/api@1.9.0)(reflect-metadata@0.2.2))(colors@1.4.0)(pg-boss@10.3.3)(rxjs@7.8.2)(typeorm@0.3.27(pg@8.16.3)(redis@5.8.3)(reflect-metadata@0.2.2))':
    '@wisemen/pgboss-nestjs-job': private
  '@wisemen/time@0.0.26(class-validator@0.14.2)(dayjs@1.11.18)':
    '@wisemen/time': private
  '@wisemen/validators@0.0.16(@wisemen/time@0.0.26(class-validator@0.14.2)(dayjs@1.11.18))(class-validator@0.14.2)(dayjs@1.11.18)':
    '@wisemen/validators': private
  '@wisemen/vue-core-auth@2.1.0':
    '@wisemen/vue-core-auth': private
  '@wisemen/vue-core-components@1.16.0(motion-v@1.7.0(@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)))(reka-ui@2.4.1(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)))(tailwind-merge@2.6.0)(tailwindcss@4.1.12)(vue-i18n@11.1.10(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@wisemen/vue-core-components': private
  '@wisemen/vue-core-generator@1.2.0(@types/node@24.9.1)':
    '@wisemen/vue-core-generator': private
  '@wisemen/vue-core-modules@2.3.7(@wisemen/vue-core-components@1.16.0(motion-v@1.7.0(@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)))(reka-ui@2.4.1(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)))(tailwind-merge@2.6.0)(tailwindcss@4.1.12)(vue-i18n@11.1.10(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)))(motion-v@1.7.0(@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)))(vue-i18n@11.1.10(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@wisemen/vue-core-modules': private
  '@wisemen/vue-core-query@0.0.43(motion-v@1.7.0(@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)))(reka-ui@2.4.1(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)))(tailwindcss@4.1.12)(vue-i18n@11.1.10(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3))':
    '@wisemen/vue-core-query': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  '@zitadel/client@1.3.1':
    '@zitadel/client': private
  '@zitadel/proto@1.3.1':
    '@zitadel/proto': private
  abbrev@2.0.0:
    abbrev: private
  accept-language-parser@1.5.0:
    accept-language-parser: private
  accepts@2.0.0:
    accepts: private
  acorn-import-attributes@1.9.5(acorn@8.15.0):
    acorn-import-attributes: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@3.0.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  alien-signals@2.0.8:
    alien-signals: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@4.1.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  app-root-path@3.1.0:
    app-root-path: private
  append-field@1.0.0:
    append-field: private
  append-transform@2.0.0:
    append-transform: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  archy@1.0.0:
    archy: private
  are-docs-informative@0.0.2:
    are-docs-informative: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-timsort@1.0.3:
    array-timsort: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-v8-to-istanbul@0.3.7:
    ast-v8-to-istanbul: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axios@1.12.2:
    axios: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.4):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.4):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.4):
    babel-plugin-polyfill-regenerator: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  baseline-browser-mapping@2.8.18:
    baseline-browser-mapping: private
  big-integer@1.6.52:
    big-integer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  binary@0.3.0:
    binary: private
  birpc@2.6.1:
    birpc: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  body-parser@2.2.0:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.26.3:
    browserslist: private
  btoa@1.2.1:
    btoa: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer-indexof-polyfill@1.0.2:
    buffer-indexof-polyfill: private
  buffer@6.0.3:
    buffer: private
  buffers@0.1.1:
    buffers: private
  builtin-modules@5.0.0:
    builtin-modules: private
  bundle-name@4.1.0:
    bundle-name: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  c12@2.0.1(magicast@0.3.5):
    c12: private
  cac@6.7.14:
    cac: private
  caching-transform@4.0.0:
    caching-transform: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@5.3.1:
    camelcase: private
  caniuse-lite@1.0.30001751:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chai@5.3.3:
    chai: private
  chainsaw@0.1.0:
    chainsaw: private
  chalk@5.6.0:
    chalk: private
  change-case@5.4.4:
    change-case: private
  character-entities@2.0.2:
    character-entities: private
  character-parser@2.2.0:
    character-parser: private
  chardet@2.1.0:
    chardet: private
  check-error@2.1.1:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@4.3.1:
    ci-info: private
  citty@0.1.6:
    citty: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-transformer@0.5.1:
    class-transformer: private
  class-validator@0.14.2:
    class-validator: private
  clean-regexp@1.0.0:
    clean-regexp: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-width@4.1.0:
    cli-width: private
  cliui@9.0.1:
    cliui: private
  clone-deep@4.0.1:
    clone-deep: private
  clone@1.0.4:
    clone: private
  clsx@2.1.1:
    clsx: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  colors@1.4.0:
    colors: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@13.0.0:
    commander: private
  comment-json@4.4.1:
    comment-json: private
  comment-parser@1.4.1:
    comment-parser: private
  common-tags@1.8.2:
    common-tags: private
  commondir@1.0.1:
    commondir: private
  component-emitter@1.3.1:
    component-emitter: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  confbox@0.1.8:
    confbox: private
  config-chain@1.1.13:
    config-chain: private
  consola@3.4.2:
    consola: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cookiejar@2.1.4:
    cookiejar: private
  copy-anything@3.0.5:
    copy-anything: private
  core-js-compat@3.46.0:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig@8.3.6(typescript@5.8.3):
    cosmiconfig: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  crelt@1.0.6:
    crelt: private
  cron-parser@4.9.0:
    cron-parser: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  cssesc@3.0.0:
    cssesc: private
  cssstyle@4.6.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  data-urls@5.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dayjs@1.11.18:
    dayjs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.3:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  dedent@1.7.0:
    dedent: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  default-require-extensions@3.0.1:
    default-require-extensions: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.1.2:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  dezalgo@1.0.4:
    dezalgo: private
  diff-sequences@27.5.1:
    diff-sequences: private
  diff@7.0.0:
    diff: private
  dotenv-expand@12.0.1:
    dotenv-expand: private
  dotenv@17.2.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer2@0.1.4:
    duplexer2: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  editorconfig@1.0.4:
    editorconfig: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.237:
    electron-to-chromium: private
  emmet@2.4.11:
    emmet: private
  emoji-regex@10.6.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  error-ex@1.3.4:
    error-ex: private
  error-stack-parser-es@1.0.5:
    error-stack-parser-es: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es6-error@4.1.1:
    es6-error: private
  es6-promise@4.2.8:
    es6-promise: private
  esbuild@0.25.11:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-compat-utils@0.6.5(eslint@9.36.0(jiti@2.6.1)):
    eslint-compat-utils: private
  eslint-config-flat-gitignore@2.1.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-config-flat-gitignore: private
  eslint-flat-config-utils@2.1.4:
    eslint-flat-config-utils: private
  eslint-import-context@0.1.9(unrs-resolver@1.11.1):
    eslint-import-context: private
  eslint-json-compat-utils@0.2.1(eslint@9.36.0(jiti@2.6.1))(jsonc-eslint-parser@2.4.1):
    eslint-json-compat-utils: private
  eslint-merge-processors@2.0.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-merge-processors: private
  eslint-plugin-antfu@3.1.1(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-antfu: private
  eslint-plugin-check-file@3.1.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-check-file: private
  eslint-plugin-command@3.3.1(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-command: private
  eslint-plugin-es-x@7.8.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-es-x: private
  eslint-plugin-import-newlines@1.4.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-import-newlines: private
  eslint-plugin-import-x@4.16.1(@typescript-eslint/utils@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-import-x: private
  eslint-plugin-jsdoc@50.8.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-jsdoc: private
  eslint-plugin-jsonc@2.21.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-jsonc: private
  eslint-plugin-n@17.23.1(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3):
    eslint-plugin-n: private
  eslint-plugin-newline-destructuring@1.2.2(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-newline-destructuring: private
  eslint-plugin-no-only-tests@3.3.0:
    eslint-plugin-no-only-tests: private
  eslint-plugin-path@1.3.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-path: private
  eslint-plugin-perfectionist@4.10.1(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3):
    eslint-plugin-perfectionist: private
  eslint-plugin-playwright@2.2.2(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-playwright: private
  eslint-plugin-readable-tailwind@2.1.2(eslint@9.36.0(jiti@2.6.1))(tailwindcss@4.1.12):
    eslint-plugin-readable-tailwind: private
  eslint-plugin-regexp@2.10.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-regexp: private
  eslint-plugin-require-explicit-generics@1.0.0:
    eslint-plugin-require-explicit-generics: private
  eslint-plugin-simple-import-sort@12.1.1(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-simple-import-sort: private
  eslint-plugin-toml@0.12.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-toml: private
  eslint-plugin-unicorn@61.0.2(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-unicorn: private
  eslint-plugin-unused-imports@4.3.0(@typescript-eslint/eslint-plugin@8.46.2(@typescript-eslint/parser@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3))(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-unused-imports: private
  eslint-plugin-vue@9.33.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-vue: private
  eslint-plugin-vuejs-accessibility@2.4.1(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-vuejs-accessibility: private
  eslint-plugin-yml@1.19.0(eslint@9.36.0(jiti@2.6.1)):
    eslint-plugin-yml: private
  eslint-processor-vue-blocks@2.0.0(@vue/compiler-sfc@3.5.22)(eslint@9.36.0(jiti@2.6.1)):
    eslint-processor-vue-blocks: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  esm-env@1.2.2:
    esm-env: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter2@6.4.9:
    eventemitter2: private
  events@3.3.0:
    events: private
  exceljs@4.4.0:
    exceljs: private
  execa@9.6.0:
    execa: private
  expect-type@1.2.2:
    expect-type: private
  expect@30.2.0:
    expect: private
  express@5.1.0:
    express: private
  exsolve@1.0.7:
    exsolve: private
  fast-csv@4.3.6:
    fast-csv: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.1.0:
    fast-uri: private
  fast-xml-parser@5.3.0:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fault@2.0.1:
    fault: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  fflate@0.8.2:
    fflate: private
  figures@6.1.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-type@21.0.0:
    file-type: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  find-cache-dir@3.3.2:
    find-cache-dir: private
  find-up-simple@1.0.1:
    find-up-simple: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fork-ts-checker-webpack-plugin@9.1.0(typescript@5.8.3)(webpack@5.100.2):
    fork-ts-checker-webpack-plugin: private
  form-data@2.5.5:
    form-data: private
  formango@3.0.0-alpha.5(vue@3.5.13(typescript@5.9.3))(zod@3.24.4):
    formango: private
  format@0.2.2:
    format: private
  formidable@3.5.4:
    formidable: private
  forwarded-parse@2.1.2:
    forwarded-parse: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  framer-motion@12.23.12:
    framer-motion: private
  fresh@2.0.0:
    fresh: private
  fromentries@1.3.2:
    fromentries: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@11.3.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs-monkey@1.1.0:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  fstream@1.0.12:
    fstream: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  fuse.js@7.1.0:
    fuse.js: private
  generator-function@2.0.1:
    generator-function: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.4.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@9.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.12.0:
    get-tsconfig: private
  giget@1.2.5:
    giget: private
  github-slugger@2.0.0:
    github-slugger: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@11.0.1:
    glob: private
  globals@16.4.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globrex@0.1.2:
    globrex: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql@16.11.0:
    graphql: private
  handlebars@4.7.8:
    handlebars: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasha@5.2.2:
    hasha: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  headers-polyfill@4.0.3:
    headers-polyfill: private
  hey-listen@1.0.8:
    hey-listen: private
  hookable@5.5.3:
    hookable: private
  hosted-git-info@2.8.9:
    hosted-git-info: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@8.0.1:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  immediate@3.0.6:
    immediate: private
  immutable@5.1.4:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-in-the-middle@1.15.0:
    import-in-the-middle: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@12.5.0(@types/node@24.9.1):
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-builtin-module@5.0.0:
    is-builtin-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@3.0.0:
    is-docker: private
  is-expression@4.0.0:
    is-expression: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.2:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@1.0.0:
    is-interactive: private
  is-language-code@3.1.0:
    is-language-code: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-node-process@1.2.0:
    is-node-process: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@4.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-hook@3.0.0:
    istanbul-lib-hook: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-processinfo@2.0.3:
    istanbul-lib-processinfo: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@5.0.6:
    istanbul-lib-source-maps: private
  istanbul-reports@3.2.0:
    istanbul-reports: private
  iterare@1.2.1:
    iterare: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.4:
    jake: private
  jest-diff@30.2.0:
    jest-diff: private
  jest-matcher-utils@30.2.0:
    jest-matcher-utils: private
  jest-message-util@30.2.0:
    jest-message-util: private
  jest-mock@30.2.0:
    jest-mock: private
  jest-regex-util@30.0.1:
    jest-regex-util: private
  jest-util@30.2.0:
    jest-util: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.6.1:
    jiti: private
  joi@18.0.1:
    joi: private
  jose@6.1.0:
    jose: private
  js-beautify@1.15.4:
    js-beautify: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdoc-type-pratt-parser@4.8.0:
    jsdoc-type-pratt-parser: private
  jsdom@26.1.0:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.1:
    jsonc-eslint-parser: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.2.0:
    jsonfile: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jszip@3.10.1:
    jszip: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  jwt-encode@1.0.1:
    jwt-encode: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kolorist@1.8.0:
    kolorist: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  libphonenumber-js@1.12.13:
    libphonenumber-js: private
  lie@3.3.0:
    lie: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss@1.30.1:
    lightningcss: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  linkifyjs@4.3.2:
    linkifyjs: private
  listenercount@1.0.1:
    listenercount: private
  load-esm@1.0.3:
    load-esm: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  loader-runner@4.3.1:
    loader-runner: private
  local-pkg@1.1.2:
    local-pkg: private
  localforage@1.10.0:
    localforage: private
  locate-path@5.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.flattendeep@4.4.0:
    lodash.flattendeep: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  loglevel@1.9.2:
    loglevel: private
  long@5.3.2:
    long: private
  longest-streak@3.1.0:
    longest-streak: private
  loupe@3.2.1:
    loupe: private
  lru-cache@5.1.1:
    lru-cache: private
  luxon@3.7.2:
    luxon: private
  magic-string@0.30.19:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  make-dir@3.1.0:
    make-dir: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  maska@3.2.0:
    maska: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-frontmatter@2.0.1:
    mdast-util-frontmatter: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-frontmatter@2.0.0:
    micromark-extension-frontmatter: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.1.0:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp@0.5.6:
    mkdirp: private
  mlly@1.8.0:
    mlly: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  motion-dom@12.23.12:
    motion-dom: private
  motion-utils@12.23.6:
    motion-utils: private
  motion-v@1.7.0(@vueuse/core@13.7.0(vue@3.5.13(typescript@5.9.3)))(vue@3.5.13(typescript@5.9.3)):
    motion-v: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3):
    msw: private
  muggle-string@0.4.1:
    muggle-string: private
  multer@2.0.2:
    multer: private
  mute-stream@2.0.0:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.4:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  natural-orderby@5.0.0:
    natural-orderby: private
  negotiator@1.0.0:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  nestjs-i18n@10.5.1(@nestjs/common@11.1.7(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@11.1.7)(class-validator@0.14.2)(rxjs@7.8.2):
    nestjs-i18n: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-emoji@1.11.0:
    node-emoji: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-preload@0.2.1:
    node-preload: private
  node-releases@2.0.26:
    node-releases: private
  nopt@7.2.1:
    nopt: private
  normalize-package-data@2.5.0:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@6.0.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  number-flow@0.5.8:
    number-flow: private
  nwsapi@2.2.22:
    nwsapi: private
  nyc@17.1.0:
    nyc: private
  nypm@0.5.4:
    nypm: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  ohash@2.0.11:
    ohash: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@10.2.0:
    open: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  orderedmap@2.1.1:
    orderedmap: private
  outvariant@1.4.3:
    outvariant: private
  own-keys@1.0.1:
    own-keys: private
  oxlint@0.15.13:
    oxlint: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  package-hash@4.0.0:
    package-hash: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.5.0:
    package-manager-detector: private
  pako@1.0.11:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-gitignore@2.0.0:
    parse-gitignore: private
  parse-imports-exports@0.2.4:
    parse-imports-exports: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@4.0.0:
    parse-ms: private
  parse-statements@1.0.11:
    parse-statements: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@8.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.1:
    pathval: private
  perfect-debounce@2.0.0:
    perfect-debounce: private
  pg-boss@10.3.3:
    pg-boss: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pg@8.16.3:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pify@2.3.0:
    pify: private
  pinia@3.0.3(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)):
    pinia: private
  pkce-challenge@4.1.0:
    pkce-challenge: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-types@1.3.1:
    pkg-types: private
  playwright-core@1.56.0-alpha-1758750661000:
    playwright-core: private
  playwright-msw@3.0.1(@playwright/test@1.55.0)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3)):
    playwright-msw: private
  playwright@1.56.0-alpha-1758750661000:
    playwright: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@16.1.1(postcss@8.5.6):
    postcss-import: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@6.1.1:
    pretty-bytes: private
  pretty-format@30.2.0:
    pretty-format: private
  pretty-ms@9.3.0:
    pretty-ms: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process-on-spawn@1.1.0:
    process-on-spawn: private
  progress@2.0.3:
    progress: private
  prosemirror-changeset@2.3.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.7.1:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.2:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.4.0:
    prosemirror-gapcursor: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.5.1:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.3:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.2:
    prosemirror-markdown: private
  prosemirror-menu@1.2.5:
    prosemirror-menu: private
  prosemirror-model@1.25.4:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.4:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.1:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.8.1:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.25.4)(prosemirror-state@1.4.3)(prosemirror-view@1.41.3):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.4:
    prosemirror-transform: private
  prosemirror-view@1.41.3:
    prosemirror-view: private
  proto-list@1.2.4:
    proto-list: private
  protobufjs@7.5.4:
    protobufjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pug-error@2.1.0:
    pug-error: private
  pug-lexer@5.0.1:
    pug-lexer: private
  pug-parser@6.0.0:
    pug-parser: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  quansync@0.2.11:
    quansync: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.1:
    raw-body: private
  rc9@2.1.2:
    rc9: private
  react-is@18.3.1:
    react-is: private
  read-cache@1.0.0:
    read-cache: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@5.2.0:
    read-pkg: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@4.1.2:
    readdirp: private
  redis@5.8.3:
    redis: private
  refa@0.12.1:
    refa: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.2:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regexp-ast-analysis@0.7.1:
    regexp-ast-analysis: private
  regexp-tree@0.1.27:
    regexp-tree: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.4.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  reka-ui@2.4.1(typescript@5.9.3)(vue@3.5.13(typescript@5.9.3)):
    reka-ui: private
  release-zalgo@1.0.0:
    release-zalgo: private
  remove-accents@0.5.0:
    remove-accents: private
  request-light@0.7.0:
    request-light: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-in-the-middle@7.5.2:
    require-in-the-middle: private
  require-main-filename@2.0.0:
    require-main-filename: private
  requires-port@1.0.0:
    requires-port: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.11:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@2.79.2:
    rollup: private
  rollup@4.52.5:
    rollup: private
  rope-sequence@1.3.4:
    rope-sequence: private
  router@2.2.0:
    router: private
  rrweb-cssom@0.8.0:
    rrweb-cssom: private
  run-applescript@7.1.0:
    run-applescript: private
  run-async@3.0.0:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass@1.90.0:
    sass: private
  saxes@5.0.1:
    saxes: private
  schema-utils@4.3.3:
    schema-utils: private
  scmp@2.1.0:
    scmp: private
  scslre@0.3.0:
    scslre: private
  seedrandom@3.0.5:
    seedrandom: private
  semver@6.3.1:
    semver: private
  send@1.2.0:
    send: private
  serialize-error@8.1.0:
    serialize-error: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-static@2.2.0:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  sinon@21.0.0:
    sinon: private
  sirv@3.0.2:
    sirv: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  smob@1.5.0:
    smob: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spawn-wrap@2.0.0:
    spawn-wrap: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@4.0.0:
    spdx-expression-parse: private
  spdx-license-ids@3.0.22:
    spdx-license-ids: private
  speakingurl@14.0.1:
    speakingurl: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sql-highlight@6.1.0:
    sql-highlight: private
  stable-hash-x@0.2.0:
    stable-hash-x: private
  stack-utils@2.0.6:
    stack-utils: private
  stackback@0.0.2:
    stackback: private
  statuses@2.0.2:
    statuses: private
  std-env@3.10.0:
    std-env: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  strict-event-emitter@0.5.1:
    strict-event-emitter: private
  string-format@2.0.0:
    string-format: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.2:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  strip-indent@4.1.1:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@3.1.0:
    strip-literal: private
  strnum@2.1.1:
    strnum: private
  strtok3@10.3.4:
    strtok3: private
  superagent@10.2.3:
    superagent: private
  superjson@2.2.2:
    superjson: private
  supertest@7.1.4:
    supertest: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swagger-ui-dist@5.29.4:
    swagger-ui-dist: private
  symbol-observable@4.0.0:
    symbol-observable: private
  symbol-tree@3.2.4:
    symbol-tree: private
  synckit@0.9.3:
    synckit: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tailwind-variants@3.1.0(tailwind-merge@2.6.0)(tailwindcss@4.1.12):
    tailwind-variants: private
  tailwindcss@4.1.12:
    tailwindcss: private
  tapable@2.3.0:
    tapable: private
  tar-stream@2.2.0:
    tar-stream: private
  tar@7.5.1:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terser-webpack-plugin@5.3.14(webpack@5.100.2):
    terser-webpack-plugin: private
  terser@5.44.0:
    terser: private
  test-exclude@7.0.1:
    test-exclude: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.15:
    tinyglobby: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@4.0.4:
    tinyspy: private
  tldts-core@6.1.86:
    tldts-core: private
  tldts@6.1.86:
    tldts: private
  tmp@0.2.5:
    tmp: private
  to-buffer@1.2.2:
    to-buffer: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-stream@1.0.0:
    token-stream: private
  token-types@6.1.1:
    token-types: private
  toml-eslint-parser@0.10.0:
    toml-eslint-parser: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@5.1.2:
    tough-cookie: private
  tr46@5.1.1:
    tr46: private
  traverse@0.3.9:
    traverse: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.9.3):
    ts-api-utils: private
  ts-declaration-location@1.0.7(typescript@5.9.3):
    ts-declaration-location: private
  ts.cryptojs256@1.0.1:
    ts.cryptojs256: private
  tsconfig-paths-webpack-plugin@4.2.0:
    tsconfig-paths-webpack-plugin: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsx@4.20.4:
    tsx: private
  tweetnacl@1.0.3:
    tweetnacl: private
  twilio@5.10.3:
    twilio: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typedarray@0.0.6:
    typedarray: private
  typeorm@0.3.27(pg@8.16.3)(redis@5.8.3)(reflect-metadata@0.2.2):
    typeorm: private
  typescript-auto-import-cache@0.3.6:
    typescript-auto-import-cache: private
  typescript-eslint@8.46.2(eslint@9.36.0(jiti@2.6.1))(typescript@5.9.3):
    typescript-eslint: private
  typesense@2.1.0(@babel/runtime@7.28.4):
    typesense: private
  uc.micro@2.1.0:
    uc.micro: private
  ufo@1.6.1:
    ufo: private
  uglify-js@3.19.3:
    uglify-js: private
  uid@2.0.2:
    uid: private
  uint8array-extras@1.5.0:
    uint8array-extras: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@7.16.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.1:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.2.0:
    unicode-property-aliases-ecmascript: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  unique-string@2.0.0:
    unique-string: private
  unist-util-is@6.0.1:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.2:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unplugin-utils@0.3.1:
    unplugin-utils: private
  unplugin@2.3.10:
    unplugin: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  unzipper@0.10.14:
    unzipper: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.26.3):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  vite-dev-rpc@1.1.0(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-dev-rpc: private
  vite-hot-client@2.1.0(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-hot-client: private
  vite-node@3.2.4(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1):
    vite-node: private
  vite-plugin-compression@0.5.1(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-plugin-compression: private
  vite-plugin-inspect@11.3.3(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-plugin-inspect: private
  vite-plugin-istanbul@7.1.0(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-plugin-istanbul: private
  vite-plugin-pwa@1.0.3(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))(workbox-build@7.3.0)(workbox-window@7.3.0):
    vite-plugin-pwa: private
  vite-plugin-vue-devtools@8.0.0(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1))(vue@3.5.13(typescript@5.9.3)):
    vite-plugin-vue-devtools: private
  vite-plugin-vue-inspector@5.3.2(vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1)):
    vite-plugin-vue-inspector: private
  vite@7.1.3(@types/node@24.9.1)(jiti@2.6.1)(lightningcss@1.30.1)(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1):
    vite: private
  vitest@3.2.4(@types/debug@4.1.12)(@types/node@24.9.1)(jiti@2.6.1)(jsdom@26.1.0)(lightningcss@1.30.1)(msw@2.10.5(@types/node@24.9.1)(typescript@5.9.3))(sass@1.90.0)(terser@5.44.0)(tsx@4.20.4)(yaml@2.8.1):
    vitest: private
  volar-service-css@0.0.65(@volar/language-service@2.4.23):
    volar-service-css: private
  volar-service-emmet@0.0.65(@volar/language-service@2.4.23):
    volar-service-emmet: private
  volar-service-html@0.0.65(@volar/language-service@2.4.23):
    volar-service-html: private
  volar-service-json@0.0.65(@volar/language-service@2.4.23):
    volar-service-json: private
  volar-service-pug-beautify@0.0.65(@volar/language-service@2.4.23):
    volar-service-pug-beautify: private
  volar-service-pug@0.0.65:
    volar-service-pug: private
  volar-service-typescript@0.0.65(@volar/language-service@2.4.23):
    volar-service-typescript: private
  vscode-css-languageservice@6.3.8:
    vscode-css-languageservice: private
  vscode-html-languageservice@5.6.0:
    vscode-html-languageservice: private
  vscode-json-languageservice@5.6.2:
    vscode-json-languageservice: private
  vscode-jsonrpc@8.2.0:
    vscode-jsonrpc: private
  vscode-languageserver-protocol@3.17.5:
    vscode-languageserver-protocol: private
  vscode-languageserver-textdocument@1.0.12:
    vscode-languageserver-textdocument: private
  vscode-languageserver-types@3.17.5:
    vscode-languageserver-types: private
  vscode-languageserver@9.0.1:
    vscode-languageserver: private
  vscode-nls@5.2.0:
    vscode-nls: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-component-type-helpers@2.2.12:
    vue-component-type-helpers: private
  vue-demi@0.14.10(vue@3.5.13(typescript@5.9.3)):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@9.36.0(jiti@2.6.1)):
    vue-eslint-parser: private
  vue-i18n@11.1.10(vue@3.5.13(typescript@5.9.3)):
    vue-i18n: private
  vue-router@4.5.0(vue@3.5.13(typescript@5.9.3)):
    vue-router: private
  vue-sonner@1.3.0:
    vue-sonner: private
  vue-tsc@3.0.6(typescript@5.9.3):
    vue-tsc: private
  vue@3.5.13(typescript@5.9.3):
    vue: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  watchpack@2.4.4:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-node-externals@3.0.0:
    webpack-node-externals: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: private
  webpack@5.100.2:
    webpack: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  workbox-background-sync@7.3.0:
    workbox-background-sync: private
  workbox-broadcast-update@7.3.0:
    workbox-broadcast-update: private
  workbox-build@7.3.0:
    workbox-build: private
  workbox-cacheable-response@7.3.0:
    workbox-cacheable-response: private
  workbox-core@7.3.0:
    workbox-core: private
  workbox-expiration@7.3.0:
    workbox-expiration: private
  workbox-google-analytics@7.3.0:
    workbox-google-analytics: private
  workbox-navigation-preload@7.3.0:
    workbox-navigation-preload: private
  workbox-precaching@7.3.0:
    workbox-precaching: private
  workbox-range-requests@7.3.0:
    workbox-range-requests: private
  workbox-recipes@7.3.0:
    workbox-recipes: private
  workbox-routing@7.3.0:
    workbox-routing: private
  workbox-strategies@7.3.0:
    workbox-strategies: private
  workbox-streams@7.3.0:
    workbox-streams: private
  workbox-sw@7.3.0:
    workbox-sw: private
  workbox-window@7.3.0:
    workbox-window: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.2:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  wsl-utils@0.1.0:
    wsl-utils: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlbuilder@13.0.2:
    xmlbuilder: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml-eslint-parser@1.3.0:
    yaml-eslint-parser: private
  yaml@2.8.1:
    yaml: private
  yargs-parser@22.0.0:
    yargs-parser: private
  yargs@18.0.0:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors-cjs@2.1.3:
    yoctocolors-cjs: private
  yoctocolors@2.1.2:
    yoctocolors: private
  zip-stream@4.1.1:
    zip-stream: private
  zod@3.24.4:
    zod: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.18.3
pendingBuilds: []
prunedAt: Wed, 29 Oct 2025 08:46:22 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.6.0'
  - '@emnapi/runtime@1.6.0'
  - '@emnapi/wasi-threads@1.1.0'
  - '@esbuild/aix-ppc64@0.25.11'
  - '@esbuild/android-arm64@0.25.11'
  - '@esbuild/android-arm@0.25.11'
  - '@esbuild/android-x64@0.25.11'
  - '@esbuild/darwin-arm64@0.25.11'
  - '@esbuild/freebsd-arm64@0.25.11'
  - '@esbuild/freebsd-x64@0.25.11'
  - '@esbuild/linux-arm64@0.25.11'
  - '@esbuild/linux-arm@0.25.11'
  - '@esbuild/linux-ia32@0.25.11'
  - '@esbuild/linux-loong64@0.25.11'
  - '@esbuild/linux-mips64el@0.25.11'
  - '@esbuild/linux-ppc64@0.25.11'
  - '@esbuild/linux-riscv64@0.25.11'
  - '@esbuild/linux-s390x@0.25.11'
  - '@esbuild/linux-x64@0.25.11'
  - '@esbuild/netbsd-arm64@0.25.11'
  - '@esbuild/netbsd-x64@0.25.11'
  - '@esbuild/openbsd-arm64@0.25.11'
  - '@esbuild/openbsd-x64@0.25.11'
  - '@esbuild/openharmony-arm64@0.25.11'
  - '@esbuild/sunos-x64@0.25.11'
  - '@esbuild/win32-arm64@0.25.11'
  - '@esbuild/win32-ia32@0.25.11'
  - '@esbuild/win32-x64@0.25.11'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-win32-x64@0.33.5'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@oxlint/darwin-arm64@0.15.13'
  - '@oxlint/linux-arm64-gnu@0.15.13'
  - '@oxlint/linux-arm64-musl@0.15.13'
  - '@oxlint/linux-x64-gnu@0.15.13'
  - '@oxlint/linux-x64-musl@0.15.13'
  - '@oxlint/win32-arm64@0.15.13'
  - '@oxlint/win32-x64@0.15.13'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.52.5'
  - '@rollup/rollup-android-arm64@4.52.5'
  - '@rollup/rollup-darwin-arm64@4.52.5'
  - '@rollup/rollup-freebsd-arm64@4.52.5'
  - '@rollup/rollup-freebsd-x64@4.52.5'
  - '@rollup/rollup-linux-arm-gnueabihf@4.52.5'
  - '@rollup/rollup-linux-arm-musleabihf@4.52.5'
  - '@rollup/rollup-linux-arm64-gnu@4.52.5'
  - '@rollup/rollup-linux-arm64-musl@4.52.5'
  - '@rollup/rollup-linux-loong64-gnu@4.52.5'
  - '@rollup/rollup-linux-ppc64-gnu@4.52.5'
  - '@rollup/rollup-linux-riscv64-gnu@4.52.5'
  - '@rollup/rollup-linux-riscv64-musl@4.52.5'
  - '@rollup/rollup-linux-s390x-gnu@4.52.5'
  - '@rollup/rollup-linux-x64-gnu@4.52.5'
  - '@rollup/rollup-linux-x64-musl@4.52.5'
  - '@rollup/rollup-openharmony-arm64@4.52.5'
  - '@rollup/rollup-win32-arm64-msvc@4.52.5'
  - '@rollup/rollup-win32-ia32-msvc@4.52.5'
  - '@rollup/rollup-win32-x64-gnu@4.52.5'
  - '@rollup/rollup-win32-x64-msvc@4.52.5'
  - '@sentry/cli-linux-arm64@2.56.1'
  - '@sentry/cli-linux-arm@2.56.1'
  - '@sentry/cli-linux-i686@2.56.1'
  - '@sentry/cli-linux-x64@2.56.1'
  - '@sentry/cli-win32-arm64@2.56.1'
  - '@sentry/cli-win32-i686@2.56.1'
  - '@sentry/cli-win32-x64@2.56.1'
  - '@tailwindcss/oxide-android-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-arm64@4.1.12'
  - '@tailwindcss/oxide-freebsd-x64@4.1.12'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.12'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.12'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.12'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.12'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.12'
  - '@tybys/wasm-util@0.10.1'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-x64-msvc@1.11.1'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
