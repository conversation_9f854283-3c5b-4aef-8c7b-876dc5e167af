#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/cli/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/cli/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/cli/index.js" "$@"
else
  exec node  "$basedir/../.pnpm/msw@2.10.5_@types+node@24.9.1_typescript@5.9.3/node_modules/msw/cli/index.js" "$@"
fi
