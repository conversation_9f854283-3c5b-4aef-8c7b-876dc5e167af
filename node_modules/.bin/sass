#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/sass@1.90.0/node_modules/sass/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/sass@1.90.0/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/sass@1.90.0/node_modules/sass/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/sass@1.90.0/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/sass@1.90.0/node_modules/sass/sass.js" "$@"
else
  exec node  "$basedir/../.pnpm/sass@1.90.0/node_modules/sass/sass.js" "$@"
fi
