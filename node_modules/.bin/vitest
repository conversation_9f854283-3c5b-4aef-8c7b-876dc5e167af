#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules/vitest/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules/vitest/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules:/Users/<USER>/Projects/indaver/indaver-monorepo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../.pnpm/vitest@3.2.4_@types+debug@4.1.12_@types+node@24.9.1_jiti@2.6.1_jsdom@26.1.0_lightningcs_7b6e6b1c2404fd89da8787f695614d31/node_modules/vitest/vitest.mjs" "$@"
fi
