# Indaver Monorepo Development Guide

## 🚀 Quick Start

```bash
# Install all dependencies
pnpm install

# Build all packages (types first, then apps)
pnpm build

# Start all apps in development mode
pnpm dev

# Or start individual apps
pnpm dev:api    # Start API only
pnpm dev:web    # Start web app only
pnpm dev:types  # Watch types package
```

## 📁 Project Structure

```
indaver-monorepo/
├── apps/
│   ├── indaver-cz-api/     # NestJS API backend
│   └── indaver-cz-web/     # Vue.js frontend
├── packages/
│   └── types/              # Shared TypeScript types
└── package.json            # Root workspace configuration
```

## 🛠️ Available Scripts

| Command | Description |
|---------|-------------|
| `pnpm install` | Install all dependencies |
| `pnpm build` | Build all packages and apps |
| `pnpm dev` | Start all apps in development |
| `pnpm test` | Run all tests |
| `pnpm lint` | Lint all code |
| `pnpm format` | Format all code with Prettier |
| `pnpm clean` | Clean all build artifacts |
| `pnpm reset` | Clean, install, and build everything |

## 🔧 Development Workflow

1. **Make changes** to any package or app
2. **Types package** rebuilds automatically in watch mode
3. **Apps** hot-reload when types change
4. **Run tests** before committing
5. **Format code** with Prettier

## 📦 Adding Dependencies

```bash
# Add to specific app
pnpm --filter @indaver/web add vue-router
pnpm --filter nestjs-example add @nestjs/jwt

# Add to shared types package
pnpm --filter @indaver/types add zod

# Add dev dependency to root (shared tooling)
pnpm add -D -w eslint-plugin-vue
```

## 🎯 Best Practices

- ✅ Use shared types from `@indaver/types`
- ✅ Keep shared dev tools at root level
- ✅ Run `pnpm format` before committing
- ✅ Build types package before apps
- ✅ Use workspace references (`workspace:*`)
