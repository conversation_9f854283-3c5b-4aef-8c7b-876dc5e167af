{"compilerOptions": {"target": "es2020", "lib": ["es2020", "dom", "dom.iterable"], "module": "esnext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "strictPropertyInitialization": false, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "paths": {"@indaver/api/*": ["./apps/indaver-cz-api/src/*"], "@indaver/web/*": ["./apps/indaver-cz-web/src/*"], "@indaver/types": ["./packages/types/src"], "@indaver/types/*": ["./packages/types/src/*"]}}}